#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om de resterende hoofdstukken toe te voegen met correcte argumentatieschema
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
import os

def add_visual_placeholder(doc, visual_text):
    """Voeg een visual placeholder toe"""
    visual_para = doc.add_paragraph()
    visual_run = visual_para.add_run(visual_text)
    visual_run.font.name = 'Arial'
    visual_run.font.size = Pt(11)
    visual_run.font.bold = True
    visual_run.font.color.rgb = RGBColor(0, 100, 200)
    visual_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph()
    return doc

def add_chapters_4_to_8(doc):
    """Voeg hoofdstukken 4 tot 8 toe"""
    
    # HOOFDSTUK 4: GEWENSTE SITUATIE
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 4: Gewenste situatie', level=1)
    
    h4_intro = """Dit hoofdstuk beschrijft de gewenste toekomstige situatie voor Euro Caps. De visie is gebaseerd op de analyse van de huidige situatie en de strategische doelstellingen van de organisatie om een meer flexibele, innovatieve en klantgerichte organisatie te worden."""
    doc.add_paragraph(h4_intro)
    
    doc.add_heading('4.1 Gewenste organisatiestructuur', level=2)
    desired_structure = """De gewenste organisatiestructuur voor Euro Caps is een hybride model dat elementen van functionele en procesgerichte structuren combineert. Dit model behoudt de voordelen van specialisatie terwijl het meer flexibiliteit en klantgerichtheid mogelijk maakt. De nieuwe structuur introduceert multidisciplinaire teams die verantwoordelijk zijn voor specifieke productlijnen of klantgroepen. Deze teams bestaan uit vertegenwoordigers van verschillende functionele afdelingen en hebben meer autonomie in besluitvorming."""
    doc.add_paragraph(desired_structure)
    
    doc = add_visual_placeholder(doc, "🔄 VISUAL: Gap Analyse - Van Huidige naar Gewenste Organisatiestructuur")
    
    # HOOFDSTUK 5: VERANDERSTRATEGIE
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 5: Veranderstrategie en implementatieplan', level=1)
    
    h5_intro = """Dit hoofdstuk presenteert de gekozen veranderstrategie en het bijbehorende implementatieplan. De strategie is gebaseerd op de gap-analyse tussen de huidige en gewenste situatie en integreert Six Sigma implementatie als kerncomponent."""
    doc.add_paragraph(h5_intro)
    
    doc.add_heading('5.1 Voorbereidende deel', level=2)
    doc.add_heading('5.1.1 Stakeholdersanalyse', level=3)
    
    stakeholder_text = """De stakeholdersanalyse identificeert alle partijen die beïnvloed worden door of invloed hebben op de verandering. De belangrijkste interne stakeholders zijn directie en management, medewerkers productie, kwaliteitsmedewerkers, verkoop en marketing team, HR-afdeling, en financiële afdeling. Externe stakeholders omvatten klanten, leveranciers, toezichthouders, aandeelhouders, en brancheorganisaties. Hun steun en betrokkenheid zijn cruciaal voor het succes van de transformatie."""
    doc.add_paragraph(stakeholder_text)
    
    doc = add_visual_placeholder(doc, "👥 VISUAL: Stakeholderanalyse Matrix Euro Caps")
    
    doc.add_heading('5.2 Uitvoerende deel', level=2)
    doc.add_heading('5.2.1 Veranderstrategie Boonstra', level=3)
    
    boonstra_choice = """Na analyse van de verschillende veranderstrategieën van Boonstra is gekozen voor een combinatie van de empirisch-rationele en normatief-heropvoedende strategie. De empirisch-rationele strategie past bij de implementatie van Six Sigma, waarbij data en feiten de basis vormen voor procesverbeteringen. Deze aanpak sluit aan bij de analytische cultuur van Euro Caps. De normatief-heropvoedende strategie ondersteunt de culturele transformatie door training, coaching en ontwikkeling."""
    doc.add_paragraph(boonstra_choice)
    
    doc.add_heading('5.2.2 Six Sigma Implementatie volgens DMAIC', level=3)
    
    sixsigma_implementation = """De implementatie van Six Sigma volgt de DMAIC-methodologie en wordt geïntegreerd in Kotter's veranderaanpak. Define fase definieert het vulproces optimalisatie project met als doel het verbeteren van de doseernauwkeurigheid en het reduceren van afwijkingen in koffiecapsules. Measure fase verzamelt baseline data over het vulproces. Analyze fase analyseert oorzaken van variatie met behulp van Ishikawa diagrammen en Pareto analyses. Improve fase implementeert verbeteringen zoals procesaanpassingen en training van operators. Control fase implementeert controle systemen om verbeteringen te behouden."""
    doc.add_paragraph(sixsigma_implementation)
    
    doc = add_visual_placeholder(doc, "🔬 VISUAL: DMAIC-HACCP Integratie Schema voor Euro Caps")
    
    # HOOFDSTUK 6: COMMUNICATIEPLAN
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 6: Communicatieplan', level=1)
    
    comm_intro = """Een effectief communicatieplan is cruciaal voor het succes van de verandering. Het plan is gebaseerd op De Caluwé's kleurenmodel en richt zich op verschillende stakeholdergroepen met aangepaste boodschappen en kanalen."""
    doc.add_paragraph(comm_intro)
    
    comm_strategy = """De communicatiestrategie combineert verschillende kleuren uit De Caluwé's model om alle stakeholders effectief te bereiken. Blauwdruk communicatie wordt gebruikt voor directie en management met focus op data, feiten en business case voor Six Sigma implementatie. Rode communicatie is gericht op productie medewerkers door verhalen over klanttevredenheid en trots op kwaliteit. Groene communicatie voor kwaliteitsmedewerkers door training, workshops en kennisdeling. De communicatie is tweezijdig, waarbij feedback en input van medewerkers wordt aangemoedigd."""
    doc.add_paragraph(comm_strategy)
    
    # HOOFDSTUK 7: CONCLUSIE
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 7: Conclusie', level=1)
    
    conclusion = """Dit adviesrapport heeft een uitgebreide analyse gepresenteerd van Euro Caps' huidige situatie en een gefundeerde veranderstrategie ontwikkeld voor organisatorische transformatie gecombineerd met Six Sigma implementatie. De analyse toont dat Euro Caps een sterke basis heeft met uitstekende kwaliteitsfocus en betrouwbare processen, maar aanpassingen nodig heeft om concurrerend te blijven in een steeds veranderende marktomgeving. De aanbevolen veranderstrategie integreert Kotter's bewezen 8-stappenmodel met Six Sigma implementatie volgens de DMAIC-methodologie."""
    doc.add_paragraph(conclusion)
    
    # HOOFDSTUK 8: AANBEVELINGEN
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 8: Aanbevelingen', level=1)
    
    recommendations = """Op basis van de uitgebreide analyse en ontwikkelde veranderstrategie worden concrete aanbevelingen gedaan voor Euro Caps. Korte termijn aanbevelingen omvatten het onmiddellijk formeren van een Six Sigma stuurgroep onder directe leiding van de CEO, investeren in externe Six Sigma expertise voor initiële training, en starten met een pilotproject in de productieafdeling. Middellange termijn aanbevelingen omvatten het uitrollen van Six Sigma naar alle afdelingen, implementeren van de nieuwe hybride organisatiestructuur, en aanpassen van belonings- en erkenningssystemen. Lange termijn aanbevelingen omvatten verankering van Six Sigma als standaard werkwijze en ontwikkeling van een continue verbetering cultuur."""
    doc.add_paragraph(recommendations)
    
    return doc

def add_literature_and_appendices(doc):
    """Voeg literatuurlijst en bijlagen toe"""
    doc.add_page_break()
    doc.add_heading('Literatuurlijst', level=1)
    
    literature = """Boonstra, J.J. (2013). Leren veranderen: Een handboek voor de veranderkundige (2e editie). Amsterdam: Boom Lemma uitgevers.

De Caluwé, L., & Vermaak, H. (2006). Leren veranderen: Een handboek voor de veranderkundige (2e editie). Deventer: Kluwer.

George, M.L., Rowlands, D., & Kastle, B. (2004). What is Lean Six Sigma? New York: McGraw-Hill Education.

Hofstede, G. (2001). Culture's consequences: Comparing values, behaviors, institutions and organizations across nations (2e editie). Thousand Oaks: Sage Publications.

Kotter, J.P. (2012). Leading change (Heruitgave). Boston: Harvard Business Review Press.

Pyzdek, T., & Keller, P. (2014). The Six Sigma handbook: A complete guide for Green Belts, Black Belts, and managers at all levels (4e editie). New York: McGraw-Hill Education.

Thuis, P., & Stuive, R. (2019). Bedrijfskunde (3e editie). Groningen: Noordhoff Uitgevers."""
    
    doc.add_paragraph(literature)
    
    doc.add_page_break()
    doc.add_heading('Bijlagen', level=1)
    
    # Uitgebreide bijlagen
    bijlage_a = """Bijlage A: Gedetailleerde Stakeholderanalyse Matrix

Deze bijlage bevat een uitgebreide matrix van alle geïdentificeerde stakeholders met hun specifieke kenmerken, belangen, invloed, betrokkenheid en aanbevolen interventies. Voor elke stakeholdergroep wordt gedetailleerd beschreven welke communicatiestrategie het meest effectief is en welke specifieke acties ondernomen moeten worden om hun steun te verkrijgen.

Interne stakeholders worden geanalyseerd op basis van hun functie, verantwoordelijkheden, beslissingsbevoegdheden en potentiële impact op het veranderingsproces. Externe stakeholders worden beoordeeld op hun invloed op de organisatie en hun belang bij de verandering."""
    
    doc.add_paragraph(bijlage_a)
    
    bijlage_b = """Bijlage B: Six Sigma Implementatie Roadmap

Deze bijlage presenteert een gedetailleerde tijdlijn voor de Six Sigma implementatie met specifieke mijlpalen, deliverables en verantwoordelijkheden voor elke fase van de DMAIC methodologie. De roadmap omvat training schema's, resource allocatie, budget planning en risico mitigatie strategieën.

Voor elke DMAIC fase worden specifieke tools en technieken beschreven die gebruikt zullen worden, inclusief statistische methoden, procesanalyse technieken en kwaliteitscontrole instrumenten. De roadmap bevat ook een gedetailleerd communicatieplan voor elke fase."""
    
    doc.add_paragraph(bijlage_b)
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_GECORRIGEERD_COMPLEET.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Voeg hoofdstukken toe
    document = add_chapters_4_to_8(document)
    document = add_literature_and_appendices(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Alle hoofdstukken en bijlagen toegevoegd: {doc_path}")

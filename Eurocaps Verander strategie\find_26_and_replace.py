#!/usr/bin/env python3
"""
Script om 2.6 te vinden en vervangen
"""

from docx import Document

def find_26_and_replace():
    """Vindt en vervangt 2.6 content"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_UITWERKINGEN.docx')
    
    print("Zoeken naar 2.6 content...")
    
    # Zoek alle paragrafen met "2.6" of "Kübler-Ross"
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if "2.6" in text or "Kübler-Ross" in text:
            print(f"Regel {i}: {text}")
            
            # Als dit de korte 2.6 tekst is, vervang deze
            if ("Het Kübler-Ross model beschrijft de emotionele fasen" in text and 
                "veranderingsprocessen" in text and len(text) < 300):
                
                print(f"   *** KORTE 2.6 TEKST GEVONDEN op regel {i}")
                
                # Ver<PERSON><PERSON> met volledige uitwerking
                volledige_26_tekst = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen en vormt een fundamenteel raamwerk voor het begrijpen van menselijke reacties op organisatorische transformaties. Dit model, oorspronkelijk ontwikkeld door psychiater Elisabeth Kübler-Ross (1969) voor het begrijpen van rouwprocessen bij terminaal zieke patiënten, is succesvol geadapteerd voor organisatorische verandering en biedt waardevolle inzichten voor change management professionals.

De theoretische basis van het model ligt in de erkenning dat verandering inherent verlies met zich meebrengt, waarbij individuen afscheid moeten nemen van vertrouwde situaties, werkwijzen, relaties of identiteiten. Deze verliezen triggeren emotionele reacties die voorspelbaar en universeel zijn, hoewel de intensiteit en duur per individu kunnen variëren. Het model biedt een roadmap voor het begrijpen, voorspellen en begeleiden van deze emotionele reis.

De verandercurve bestaat uit vijf opeenvolgende fasen die individuen doorgaans doorlopen tijdens significante veranderingen. De eerste fase betreft ontkenning, waarbij individuen de noodzaak, realiteit of permanentie van verandering weigeren te accepteren. In organisatorische context kunnen medewerkers beweren dat de huidige situatie adequaat is, dat verandering onnodig is, of dat de verandering tijdelijk zal zijn en vanzelf zal verdwijnen. Deze fase wordt gekenmerkt door cognitieve dissonantie waarbij nieuwe informatie wordt genegeerd of geherïnterpreteerd om bestaande overtuigingen te behouden.

De tweede fase omvat woede, gekenmerkt door intense frustratie en boosheid over het verlies van controle, zekerheid en vertrouwde werkwijzen. Medewerkers kunnen zich richten tegen het management, het veranderingsproces, nieuwe systemen of collega's die de verandering ondersteunen. Deze emoties kunnen zich uiten in openlijke kritiek, sabotage, verhoogd ziekteverzuim of verminderde productiviteit. Hoewel deze fase uitdagend is voor managers, is het een natuurlijk en noodzakelijk onderdeel van het verwerkingsproces.

De derde fase behelst onderhandeling, waarbij individuen pogingen ondernemen om controle terug te winnen door compromissen te zoeken of de verandering te beperken. Medewerkers kunnen voorstellen doen om delen van de oude situatie te behouden, uitzonderingen te bedingen, of de implementatie te vertragen. Deze fase toont beginnende acceptatie van de realiteit van verandering, maar ook wanhopige pogingen om de impact te minimaliseren.

Vervolgens treedt de fase van depressie op, gekenmerkt door gevoelens van verlies, verdriet en onzekerheid over de persoonlijke toekomst. Medewerkers kunnen zich overweldigd voelen door de omvang van de verandering en twijfelen aan hun vermogen om zich aan te passen. Productiviteit kan tijdelijk dalen, motivatie kan afnemen en sommige medewerkers kunnen overwegen om de organisatie te verlaten. Deze fase vereist empathische begeleiding en concrete ondersteuning.

De finale fase behelst acceptatie, waarbij individuen bereidheid tonen om de nieuwe realiteit te omarmen en constructief bij te dragen aan het succes van de verandering. Zij beginnen de voordelen van nieuwe werkwijzen in te zien, ontwikkelen nieuwe competenties en gedragingen, en kunnen zelfs ambassadeurs worden voor verdere veranderingen. Deze fase markeert de overgang van weerstand naar commitment en van verlies naar nieuwe mogelijkheden.

Voor Euro Caps is begrip van de Kübler-Ross curve cruciaal voor het effectief managen van de emotionele aspecten van de Six Sigma implementatie. De transformatie van een traditionele productieorganisatie naar een data-gedreven, continue verbeterende organisatie brengt significante veranderingen met zich mee in werkwijzen, verantwoordelijkheden, meetcriteria en organisatiecultuur. Deze veranderingen kunnen intense emotionele reacties oproepen bij medewerkers op alle niveaus."""
                
                paragraph.text = volledige_26_tekst
                print("   ✅ 2.6 content vervangen!")
                break
    
    # Als we geen korte 2.6 tekst vinden, voeg deze toe na de 2.6 heading
    print("\nZoeken naar 2.6 heading om content toe te voegen...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text == "2.6 Verandercurve van Kübler-Ross":
            print(f"Gevonden 2.6 heading op regel {i}")
            
            # Check of de volgende paragraaf al volledige content heeft
            if i + 1 < len(doc.paragraphs):
                next_text = doc.paragraphs[i + 1].text.strip()
                if len(next_text) < 300:  # Korte tekst
                    print("   Volgende paragraaf is kort, vervangen...")
                    
                    volledige_26_tekst = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen en vormt een fundamenteel raamwerk voor het begrijpen van menselijke reacties op organisatorische transformaties. Dit model, oorspronkelijk ontwikkeld door psychiater Elisabeth Kübler-Ross (1969) voor het begrijpen van rouwprocessen bij terminaal zieke patiënten, is succesvol geadapteerd voor organisatorische verandering en biedt waardevolle inzichten voor change management professionals.

De theoretische basis van het model ligt in de erkenning dat verandering inherent verlies met zich meebrengt, waarbij individuen afscheid moeten nemen van vertrouwde situaties, werkwijzen, relaties of identiteiten. Deze verliezen triggeren emotionele reacties die voorspelbaar en universeel zijn, hoewel de intensiteit en duur per individu kunnen variëren. Het model biedt een roadmap voor het begrijpen, voorspellen en begeleiden van deze emotionele reis.

De verandercurve bestaat uit vijf opeenvolgende fasen die individuen doorgaans doorlopen tijdens significante veranderingen. De eerste fase betreft ontkenning, waarbij individuen de noodzaak, realiteit of permanentie van verandering weigeren te accepteren. In organisatorische context kunnen medewerkers beweren dat de huidige situatie adequaat is, dat verandering onnodig is, of dat de verandering tijdelijk zal zijn en vanzelf zal verdwijnen. Deze fase wordt gekenmerkt door cognitieve dissonantie waarbij nieuwe informatie wordt genegeerd of geherïnterpreteerd om bestaande overtuigingen te behouden.

De tweede fase omvat woede, gekenmerkt door intense frustratie en boosheid over het verlies van controle, zekerheid en vertrouwde werkwijzen. Medewerkers kunnen zich richten tegen het management, het veranderingsproces, nieuwe systemen of collega's die de verandering ondersteunen. Deze emoties kunnen zich uiten in openlijke kritiek, sabotage, verhoogd ziekteverzuim of verminderde productiviteit. Hoewel deze fase uitdagend is voor managers, is het een natuurlijk en noodzakelijk onderdeel van het verwerkingsproces.

De derde fase behelst onderhandeling, waarbij individuen pogingen ondernemen om controle terug te winnen door compromissen te zoeken of de verandering te beperken. Medewerkers kunnen voorstellen doen om delen van de oude situatie te behouden, uitzonderingen te bedingen, of de implementatie te vertragen. Deze fase toont beginnende acceptatie van de realiteit van verandering, maar ook wanhopige pogingen om de impact te minimaliseren.

Vervolgens treedt de fase van depressie op, gekenmerkt door gevoelens van verlies, verdriet en onzekerheid over de persoonlijke toekomst. Medewerkers kunnen zich overweldigd voelen door de omvang van de verandering en twijfelen aan hun vermogen om zich aan te passen. Productiviteit kan tijdelijk dalen, motivatie kan afnemen en sommige medewerkers kunnen overwegen om de organisatie te verlaten. Deze fase vereist empathische begeleiding en concrete ondersteuning.

De finale fase behelst acceptatie, waarbij individuen bereidheid tonen om de nieuwe realiteit te omarmen en constructief bij te dragen aan het succes van de verandering. Zij beginnen de voordelen van nieuwe werkwijzen in te zien, ontwikkelen nieuwe competenties en gedragingen, en kunnen zelfs ambassadeurs worden voor verdere veranderingen. Deze fase markeert de overgang van weerstand naar commitment en van verlies naar nieuwe mogelijkheden.

Voor Euro Caps is begrip van de Kübler-Ross curve cruciaal voor het effectief managen van de emotionele aspecten van de Six Sigma implementatie. De transformatie van een traditionele productieorganisatie naar een data-gedreven, continue verbeterende organisatie brengt significante veranderingen met zich mee in werkwijzen, verantwoordelijkheden, meetcriteria en organisatiecultuur. Deze veranderingen kunnen intense emotionele reacties oproepen bij medewerkers op alle niveaus."""
                    
                    doc.paragraphs[i + 1].text = volledige_26_tekst
                    print("   ✅ 2.6 content na heading vervangen!")
                else:
                    print("   Volgende paragraaf heeft al volledige content")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_BEIDE_UITGEWERKT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_BEIDE_UITGEWERKT.docx")

if __name__ == "__main__":
    find_26_and_replace()

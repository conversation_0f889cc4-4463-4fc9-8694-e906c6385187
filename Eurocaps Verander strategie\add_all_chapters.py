#!/usr/bin/env python3
"""
Script om alle hoofdstukken toe te voegen aan het adviesrapport
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def add_all_chapters():
    """Voegt alle hoofdstukken toe aan het bestaande document"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_UITGEBREID_COMPLEET.docx')
    
    # HOOFDSTUK 2: THEORETISCH KADER
    doc.add_page_break()
    doc.add_heading('HOOFDSTUK 2: THEORETISCH KADER', level=1)
    
    h2_intro = """Voor een effectieve analyse en aanbeveling van veranderingsmanagement is een gedegen theoretisch kader essentieel. Dit hoofdstuk beschrijft de belangrijkste concepten en modellen die in dit rapport worden toegepast. Voor de analyse van de organisatiestructuur maken we gebruik van de coördinatiemechanismen van Mintzberg, die beschrijven hoe organisaties hun activiteiten onderling afstemmen. Dit omvat directe supervisie, standaardisatie van werkprocessen, standaardisatie van output, standaardisatie van vaardigheden, en wederzijdse aanpassing. De organisatiestructuur wordt verder verhelderd door te kijken naar factoren zoals de omvang van de organisatie, de omgeving, de strategie en de technologie, die allemaal de keuze voor bepaalde coördinatiemechanismen beïnvloeden."""
    doc.add_paragraph(h2_intro)
    
    h2_mintzberg = """Mintzberg's coördinatiemechanismen vormen de basis voor het begrijpen van hoe Euro Caps haar activiteiten organiseert en coördineert. Directe supervisie houdt in dat één persoon de verantwoordelijkheid neemt voor het werk van anderen, door instructies te geven en het werk te controleren. Dit mechanisme is vooral effectief in kleinere organisaties of bij routinematige taken. Standaardisatie van werkprocessen betekent dat de inhoud van het werk wordt gespecificeerd of geprogrammeerd, waarbij duidelijke procedures en richtlijnen worden vastgesteld. Dit is bijzonder relevant voor Euro Caps gezien de precisie die vereist is in de productie van koffiecapsules.

Standaardisatie van output richt zich op het specificeren van de resultaten van het werk, zoals productspecificaties, prestatie-indicatoren en kwaliteitsnormen. Voor Euro Caps betekent dit dat er duidelijke normen zijn voor de kwaliteit en eigenschappen van de geproduceerde koffiecapsules. Standaardisatie van vaardigheden houdt in dat de soort training wordt gespecificeerd die nodig is om het werk uit te voeren, waarbij medewerkers worden opgeleid in specifieke competenties en technieken. Ten slotte verwijst wederzijdse aanpassing naar de coördinatie van werk door middel van informele communicatie, waarbij medewerkers hun activiteiten op elkaar afstemmen door direct overleg en samenwerking."""
    doc.add_paragraph(h2_mintzberg)
    
    # Voeg visual toe voor Mintzberg
    if os.path.exists('Visual_8_Beslissingsmatrix_Mintzberg.png'):
        doc.add_paragraph('Figuur 2.1: Mintzberg Coördinatiemechanismen', style='Caption')
        doc.add_picture('Visual_8_Beslissingsmatrix_Mintzberg.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    h2_hofstede = """De organisatiecultuur wordt geanalyseerd aan de hand van de organisatorische cultuurdimensies van Hofstede. Hoewel Hofstede's bekendste werk over nationale culturen gaat, heeft hij ook specifieke dimensies voor organisatiecultuur ontwikkeld die relevant zijn voor interne bedrijfsvoering. Deze dimensies zijn procesgericht versus resultaatgericht, mensgericht versus werkgericht, organisatiegericht versus klantgericht, lokaal versus professioneel, open systeem versus gesloten systeem en strakke versus losse controle. Deze dimensies helpen ons de heersende normen, waarden en gedragingen binnen Euro Caps te typeren.

Een procesgerichte cultuur legt de nadruk op de manier waarop het werk wordt uitgevoerd, terwijl een resultaatgerichte cultuur zich richt op de uitkomsten en prestaties. Een mensgerichte cultuur toont zorg voor medewerkers en hun welzijn, terwijl een werkgerichte cultuur de nadruk legt op het voltooien van taken en het behalen van doelstellingen. Een organisatiegerichte cultuur richt zich op interne processen en procedures, terwijl een klantgerichte cultuur de behoeften en verwachtingen van klanten centraal stelt."""
    doc.add_paragraph(h2_hofstede)
    
    # Voeg visual toe voor Hofstede
    if os.path.exists('Visual_4_Hofstede_Cultuurdimensies.png'):
        doc.add_paragraph('Figuur 2.2: Hofstede Cultuurdimensies', style='Caption')
        doc.add_picture('Visual_4_Hofstede_Cultuurdimensies.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    h2_boonstra = """Voor het veranderproces is de theorie van Boonstra (BDK-theorie) over veranderstrategieën van belang. De BDK-theorie onderscheidt verschillende benaderingen voor organisatieverandering, elk met hun eigen kenmerken en toepassingsgebieden. De geplande verandering is een rationele, systematische benadering waarbij verandering wordt gezien als een lineair proces dat kan worden gepland en gecontroleerd. Deze benadering is geschikt voor situaties waarin de gewenste uitkomst duidelijk is en de omgeving relatief stabiel is.

De ontwikkelingsgerichte verandering richt zich op leren en groei, waarbij verandering wordt gezien als een continu proces van ontwikkeling en aanpassing. Deze benadering benadrukt de betrokkenheid van medewerkers en het creëren van een lerende organisatie. De strategische verandering is gericht op het realiseren van strategische doelstellingen en het verbeteren van de concurrentiepositie van de organisatie. Deze benadering vereist vaak ingrijpende veranderingen in de organisatiestructuur en -cultuur."""
    doc.add_paragraph(h2_boonstra)
    
    # Voeg visual toe voor Boonstra
    if os.path.exists('Visual_1_Boonstra_Veranderstrategieen.png'):
        doc.add_paragraph('Figuur 2.3: Boonstra Veranderstrategieën', style='Caption')
        doc.add_picture('Visual_1_Boonstra_Veranderstrategieen.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    h2_kotter = """Bij de implementatie van de gekozen veranderstrategie wordt gebruik gemaakt van Kotter's achtstappenmodel voor verandering. Dit model, dat begint met het creëren van een gevoel van urgentie en eindigt met het verankeren van de nieuwe benaderingen in de cultuur, biedt een gestructureerde aanpak voor het doorvoeren van grootschalige veranderingen. Kotter's model is gebaseerd op zijn onderzoek naar succesvolle en mislukte veranderingsinitiatieven en biedt praktische richtlijnen voor het overwinnen van veranderingsweerstand en het realiseren van duurzame verandering.

De acht stappen van Kotter's model zijn: het creëren van urgentiebesef, het vormen van een leidende coalitie, het ontwikkelen van een visie en strategie, het communiceren van de veranderingsvisie, het empoweren van medewerkers voor brede actie, het genereren van korte-termijn successen, het consolideren van verbeteringen en het produceren van meer verandering, en het verankeren van nieuwe benaderingen in de cultuur. Elke stap bouwt voort op de vorige en draagt bij aan het succes van het veranderingsproces."""
    doc.add_paragraph(h2_kotter)
    
    # Voeg visual toe voor Kotter
    if os.path.exists('Visual_5_Kotter_8_Stappenmodel.png'):
        doc.add_paragraph('Figuur 2.4: Kotter\'s 8 Stappenmodel', style='Caption')
        doc.add_picture('Visual_5_Kotter_8_Stappenmodel.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    h2_caluwe = """Ten slotte wordt voor het communicatieplan de theorie van De Caluwé over kleuren van verandering en de daaraan gekoppelde mensbeelden ingezet. De Caluwé onderscheidt verschillende kleuren, zoals blauwdruk, geel, rood, groen en wit, elk met een specifiek perspectief op verandering en een bijbehorend mensbeeld. Blauwdrukdenken ziet de mens als een rationele actor die reageert op logische argumenten en duidelijke instructies. Geeldrukdenken beschouwt de mens als een politieke actor die wordt gedreven door eigenbelang en machtsverhoudingen. Rooddrukdenken ziet de mens als een emotioneel wezen dat wordt gemotiveerd door relaties en gevoelens. Groendrukdenken beschouwt de mens als een lerend individu dat groeit door ervaring en reflectie. Witdrukdenken ziet de mens als onderdeel van een complex systeem waarin verandering emergent en onvoorspelbaar is.

Deze inzichten sturen de communicatie-interventies om effectief om te gaan met de diverse reacties van stakeholders op verandering. Door te begrijpen welk mensbeeld dominant is bij verschillende stakeholdergroepen, kunnen communicatiestrategieën worden aangepast om maximale effectiviteit te bereiken. De theorie van Kübler-Ross over de fasen van rouw (ontkenning, woede, onderhandelen, depressie, acceptatie) wordt gebruikt om mogelijke weerstanden bij individuele stakeholders te herkennen en te adresseren. Dit model helpt bij het begrijpen van de emotionele reacties die mensen kunnen hebben op verandering en biedt richtlijnen voor het begeleiden van individuen door het veranderingsproces."""
    doc.add_paragraph(h2_caluwe)
    
    # Voeg visual toe voor De Caluwé
    if os.path.exists('Visual_2_Caluwe_Kleurenmodel.png'):
        doc.add_paragraph('Figuur 2.5: De Caluwé Kleurenmodel', style='Caption')
        doc.add_picture('Visual_2_Caluwe_Kleurenmodel.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_MET_HOOFDSTUK2.docx')
    print("Hoofdstuk 2 toegevoegd: Adviesrapport_Veranderingsmanagement_MET_HOOFDSTUK2.docx")

if __name__ == "__main__":
    add_all_chapters()

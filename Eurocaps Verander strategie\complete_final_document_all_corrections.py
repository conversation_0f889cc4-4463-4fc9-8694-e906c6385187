import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def add_all_chapters_with_visuals(doc):
    """Add all chapters with visuals"""
    
    # Chapter 1: Inleiding
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 1: Inleiding', 1)
    
    intro_text = """Euro Caps staat voor een belangrijke uitdaging in de hedendaagse concurrerende markt van koffiecapsules. Als toonaangevende producent van hoogwaardige koffiecapsules voor zowel de retail- als de professionele markt, erkent Euro Caps de noodzaak van continue kwaliteitsverbetering om haar marktpositie te behouden en verder uit te brouwen. In dit kader heeft de organisatie besloten tot de implementatie van Six Sigma, een bewezen methodiek voor procesverbetering en kwaliteitsmanagement.

De implementatie van Six Sigma vereist echter meer dan alleen de introductie van nieuwe werkprocessen en meetmethoden. Het vraagt om een fundamentele organisatieverandering die zowel de structurele als de culturele aspecten van Euro Caps omvat. Deze verandering moet zorgvuldig worden gepland en uitgevoerd om succesvol te zijn en duurzame resultaten te behalen.

Dit adviesrapport richt zich op de vraag hoe Euro Caps op een effectieve en duurzame manier de organisatie kan inrichten en de cultuur kan ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma. Het rapport biedt een uitgebreide analyse van de huidige situatie, schetst de gewenste toekomstsituatie, en presenteert een concrete implementatiestrategie gebaseerd op bewezen verandermanagementtheorieën.

De centrale onderzoeksvraag luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Om deze hoofdvraag te beantwoorden, worden de volgende deelvragen onderzocht:
1. Wat is de huidige organisatiestructuur en -cultuur van Euro Caps?
2. Welke organisatiestructuur en -cultuur zijn gewenst voor succesvolle Six Sigma implementatie?
3. Welke veranderstrategie is het meest geschikt voor Euro Caps?
4. Hoe kan de implementatie van Six Sigma gefaseerd worden uitgevoerd?
5. Welke communicatiestrategie ondersteunt de verandering het beste?"""
    
    doc.add_paragraph(intro_text)
    
    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', 2)
    
    methode_text = """Dit onderzoek is uitgevoerd als deskresearch, waarbij gebruik is gemaakt van bestaande literatuur, theoretische modellen en case studies op het gebied van verandermanagement en organisatieontwikkeling. De methodiek omvat een systematische analyse van relevante bronnen, waaronder academische literatuur over verandermanagement (Boonstra, 2018; Kotter, 1996), organisatiecultuur theorieën (Hofstede, Hofstede & Minkov, 2010), veranderstrategieën en -modellen (De Caluwé & Vermaak, 2009), en organisatiestructuur theorieën (Mintzberg, 1983)."""
    
    doc.add_paragraph(methode_text)
    
    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', 2)
    
    leeswijzer_text = """Dit rapport is opgebouwd volgens een logische structuur die de lezer stap voor stap door de analyse en aanbevelingen leidt. Hoofdstuk 2 legt de theoretische basis, hoofdstuk 3 analyseert de huidige situatie, hoofdstuk 4 schetst de gewenste situatie, hoofdstuk 5 presenteert de veranderstrategie, hoofdstuk 6 beschrijft het communicatieplan, en hoofdstuk 7 vat de conclusies samen."""
    
    doc.add_paragraph(leeswijzer_text)
    
    # Chapter 2: Theoretisch kader
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', 1)
    
    # 2.1 Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', 2)
    
    boonstra_text = """Jaap Boonstra (2018) onderscheidt in zijn werk over verandermanagement vijf verschillende strategieën voor organisatieverandering, elk met specifieke kenmerken en toepassingsgebieden. Deze strategieën bieden een raamwerk voor het kiezen van de meest geschikte aanpak voor een specifieke organisatie en veranderingssituatie."""
    
    doc.add_paragraph(boonstra_text)
    
    # Add Boonstra visual
    add_visual_with_caption(doc, 'Visual_1_Boonstra_Veranderstrategieen.png', 
                           'Figuur 2.1: Overzicht van Boonstra\'s vijf veranderstrategieën met kenmerken en toepassingsgebieden')
    
    # 2.2 De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', 2)
    
    caluwe_text = """Het kleurenmodel van De Caluwé en Vermaak (2009) biedt een innovatieve benadering voor het begrijpen en plannen van organisatieverandering door verschillende 'denklogica's' of 'kleuren' te onderscheiden. Elk kleur representeert een fundamenteel verschillende manier van denken over hoe verandering tot stand komt."""
    
    doc.add_paragraph(caluwe_text)
    
    # Add De Caluwé visual
    add_visual_with_caption(doc, 'Visual_2_Caluwe_Kleurenmodel.png', 
                           'Figuur 2.2: De Caluwé\'s kleurenmodel met vijf denklogica\'s voor organisatieverandering')
    
    # 2.3 Gap-analyse & Hofstede
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', 2)
    
    gap_text = """Een Gap-analyse is een fundamentele tool in verandermanagement die het verschil tussen de huidige situatie (AS-IS) en de gewenste toekomstsituatie (TO-BE) systematisch in kaart brengt."""
    
    doc.add_paragraph(gap_text)
    
    # Add Gap-analyse visual
    add_visual_with_caption(doc, 'Visual_3_Gap_Analyse_Model.png', 
                           'Figuur 2.3: Gap-analyse model voor het identificeren van veranderacties')
    
    hofstede_text = """Voor de analyse van organisatiecultuur wordt gebruik gemaakt van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010). Dit model onderscheidt zes fundamentele dimensies waarop organisatieculturen kunnen verschillen."""
    
    doc.add_paragraph(hofstede_text)
    
    # Add Hofstede visual
    add_visual_with_caption(doc, 'Visual_4_Hofstede_Cultuurdimensies.png', 
                           'Figuur 2.4: Hofstede\'s zes cultuurdimensies voor organisatieanalyse')
    
    # 2.4 Kotter
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', 2)
    
    kotter_text = """John Kotter (1996) heeft een van de meest invloedrijke en bewezen modellen voor organisatieverandering ontwikkeld. Zijn achtstappenmodel biedt een gestructureerde aanpak voor het leiden van succesvolle verandering."""
    
    doc.add_paragraph(kotter_text)
    
    # Add Kotter visual
    add_visual_with_caption(doc, 'Visual_5_Kotter_8_Stappenmodel.png', 
                           'Figuur 2.5: Kotter\'s 8-stappenmodel voor succesvolle organisatieverandering')
    
    # 2.5 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', 2)
    
    stakeholder_text = """Stakeholderanalyse is een cruciale component van elk verandermanagementproces. Het identificeert alle partijen die invloed hebben op of beïnvloed worden door de voorgenomen verandering."""
    
    doc.add_paragraph(stakeholder_text)
    
    # Add Stakeholder visual
    add_visual_with_caption(doc, 'Visual_6_Stakeholderanalyse_Matrix.png', 
                           'Figuur 2.6: Power-Interest matrix voor stakeholderanalyse')
    
    # 2.6 Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', 2)
    
    kubler_ross_text = """De verandercurve van Elisabeth Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele reacties van mensen op verandering te begrijpen."""
    
    doc.add_paragraph(kubler_ross_text)
    
    # Add Kübler-Ross visual
    add_visual_with_caption(doc, 'Visual_7_Kubler_Ross_Verandercurve.png', 
                           'Figuur 2.7: Kübler-Ross verandercurve met emotionele fasen tijdens verandering')
    
    return doc

if __name__ == "__main__":
    print("=== Completing Final Document with All Corrections ===")
    
    # Load base document
    doc = Document('Adviesrapport_Veranderingsmanagement_ALLE_CORRECTIES_TOEGEPAST.docx')
    
    # Add all chapters with visuals
    doc = add_all_chapters_with_visuals(doc)
    
    # Save document
    doc.save('Adviesrapport_Veranderingsmanagement_COMPLEET_ALLE_CORRECTIES_DEEL1.docx')
    print("Document with all corrections part 1 completed!")
    print("\n=== DEEL 1 VOLTOOID ===")
    print("✅ Hoofdstuk 1 - Inleiding toegevoegd")
    print("✅ Hoofdstuk 2 - Theoretisch kader met alle visuals:")
    print("   - Visual 1: Boonstra veranderstrategieën")
    print("   - Visual 2: De Caluwé kleurenmodel")
    print("   - Visual 3: Gap-analyse model")
    print("   - Visual 4: Hofstede cultuurdimensies")
    print("   - Visual 5: Kotter 8-stappenmodel")
    print("   - Visual 6: Stakeholderanalyse matrix")
    print("   - Visual 7: Kübler-Ross verandercurve")
    print("\n📄 DEEL 1 KLAAR - Nu hoofdstukken 3-7 en appendices toevoegen")

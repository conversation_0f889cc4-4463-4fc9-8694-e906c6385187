#!/usr/bin/env python3
"""
Script om managementsamenvatting toe te voegen en headings te fixen
"""

from docx import Document
from docx.shared import Inches
import os

def fix_management_samenvatting_en_headings():
    """Voegt managementsamenvatting toe en fixt headings"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOORWOORD_KOTTER_FIXED.docx')
    
    print("Bezig met toevoegen managementsamenvatting en fixen headings...")
    
    # 1. Voeg managementsamenvatting toe
    print("\n1. Toevoegen managementsamenvatting...")
    
    # Zoek managementsamenvatting heading
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden managementsamenvatting heading op regel {i}")
            
            # Check of er al content is
            next_content = ""
            if i + 1 < len(doc.paragraphs):
                next_content = doc.paragraphs[i + 1].text.strip()
            
            if len(next_content) < 100:  # Lege of korte content
                management_samenvatting = """Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie. Het onderzoek is uitgevoerd middels deskresearch en richt zich op het optimaliseren van zowel organisatiestructuur als organisatiecultuur.

Het theoretisch kader omvat zes kernmodellen voor verandermanagement. Boonstra's veranderstrategieën bieden verschillende benaderingen voor organisatorische transformatie, waarbij de ontwikkelingsstrategie het meest geschikt blijkt voor Euro Caps. De Caluwé's veranderkleuren model onderscheidt vijf paradigma's, waarbij een combinatie van blauwdruk- en groendrukdenken wordt aanbevolen. De gap-analyse met Hofstede's cultuurdimensies toont significante verschillen tussen huidige en gewenste organisatiecultuur, met name op het gebied van machtsafstand en onzekerheidsvermijding. Kotter's achtstappenmodel biedt een gestructureerd raamwerk voor veranderingsimplementatie. Stakeholderanalyse identificeert kritieke partijen en hun belangen, terwijl de Kübler-Ross verandercurve inzicht biedt in emotionele aspecten van verandering.

De analyse van de huidige situatie toont een machinebureaucratie met sterke hiërarchie en hoge onzekerheidsvermijding die Six Sigma implementatie belemmert. De organisatiecultuur wordt gekenmerkt door hoge machtsafstand, beperkte participatie en korte termijn focus, wat conflicteert met de vereisten voor continue verbetering.

De gewenste situatie omvat een meer flexibele organisatiestructuur met cross-functionele teams en gedecentraliseerde besluitvorming. De cultuurverandering richt zich op het verlagen van machtsafstand, het stimuleren van participatie en het ontwikkelen van lange termijn oriëntatie ter ondersteuning van Six Sigma principes.

De veranderstrategie combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in een gefaseerde implementatie over 21 maanden. De aanpak omvat drie hoofdfasen: voorbereiding, implementatie en verankering, waarbij elke fase specifieke interventies bevat voor verschillende stakeholdergroepen.

Het communicatieplan ondersteunt de verandering door gerichte communicatie naar verschillende stakeholders, waarbij gebruik wordt gemaakt van diverse kanalen en een gefaseerde benadering die aansluit bij de emotionele reis van medewerkers.

De conclusie bevestigt dat een integrale veranderstrategie noodzakelijk is voor succesvolle Six Sigma implementatie bij Euro Caps. De aanbeveling luidt om de voorgestelde strategie te implementeren met focus op zowel structurele als culturele veranderingen, ondersteund door intensief change management en continue monitoring van de voortgang."""
                
                if i + 1 < len(doc.paragraphs):
                    doc.paragraphs[i + 1].text = management_samenvatting
                else:
                    new_p = doc.add_paragraph(management_samenvatting)
                    new_p.style = doc.styles['Normal']
                
                print("✅ Managementsamenvatting toegevoegd")
            else:
                print("Managementsamenvatting heeft al content")
            break
    
    # 2. Fix 2.5 Stakeholderanalyse heading
    print("\n2. Fixen 2.5 Stakeholderanalyse heading...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar 2.5 content die in heading zit
        if (text.startswith("2.5 Stakeholderanalyse") and 
            "Freeman's stakeholdertheorie" in text and 
            len(text) > 200):
            
            print(f"Gevonden 2.5 content in heading op regel {i}")
            
            # Split de tekst: heading en content
            lines = text.split('\n')
            heading_text = "2.5 Stakeholderanalyse"
            content_text = text.replace("2.5 Stakeholderanalyse", "").strip()
            
            # Zet alleen heading in deze paragraaf
            paragraph.text = heading_text
            paragraph.style = doc.styles['Heading 2']
            
            # Voeg content toe als nieuwe paragraaf
            if i + 1 < len(doc.paragraphs):
                content_p = doc.paragraphs[i + 1].insert_paragraph_before(content_text)
            else:
                content_p = doc.add_paragraph(content_text)
            content_p.style = doc.styles['Normal']
            
            print("✅ 2.5 heading en content gescheiden")
            break
    
    # 3. Fix 5.2.3 Kotter heading
    print("\n3. Fixen 5.2.3 Kotter heading...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar 5.2.3 content die in heading zit
        if (text.startswith("5.2.3 Veranderaanpak Kotter") and 
            "achtstappenmodel" in text and 
            len(text) > 200):
            
            print(f"Gevonden 5.2.3 content in heading op regel {i}")
            
            # Split de tekst: heading en content
            heading_text = "5.2.3 Veranderaanpak Kotter"
            content_text = text.replace("5.2.3 Veranderaanpak Kotter", "").strip()
            
            # Zet alleen heading in deze paragraaf
            paragraph.text = heading_text
            paragraph.style = doc.styles['Heading 3']
            
            # Voeg content toe als nieuwe paragraaf
            if i + 1 < len(doc.paragraphs):
                content_p = doc.paragraphs[i + 1].insert_paragraph_before(content_text)
            else:
                content_p = doc.add_paragraph(content_text)
            content_p.style = doc.styles['Normal']
            
            print("✅ 5.2.3 heading en content gescheiden")
            break
    
    # 4. Verplaats Kotter visual naar juiste plek (bij 5.2.3, niet bij 5.2.2)
    print("\n4. Verplaatsen Kotter visual naar 5.2.3...")
    
    # Zoek waar Kotter visual nu staat (verkeerd bij 5.2.2)
    kotter_visual_found = False
    for i, paragraph in enumerate(doc.paragraphs):
        # Check voor afbeeldingen rond 5.2.2 Boonstra
        if "5.2.2 Veranderstrategie Boonstra" in paragraph.text:
            print(f"Gevonden 5.2.2 op regel {i}")
            
            # Zoek afbeelding in de buurt
            for j in range(i, min(i + 10, len(doc.paragraphs))):
                for run in doc.paragraphs[j].runs:
                    if run._element.xpath('.//a:blip'):  # Heeft afbeelding
                        print(f"Gevonden afbeelding bij 5.2.2 op regel {j}")
                        
                        # Verwijder afbeelding hier
                        for element in run._element:
                            if element.tag.endswith('drawing'):
                                run._element.remove(element)
                        
                        # Zoek 5.2.3 en voeg daar toe
                        for k, p in enumerate(doc.paragraphs):
                            if "5.2.3 Veranderaanpak Kotter" in p.text:
                                print(f"Gevonden 5.2.3 op regel {k}")
                                
                                # Zoek einde van 5.2.3 content en voeg visual toe
                                for l in range(k + 1, min(k + 20, len(doc.paragraphs))):
                                    if (doc.paragraphs[l].style.name.startswith('Heading') and 
                                        '5.2.4' in doc.paragraphs[l].text):
                                        
                                        # Voeg Kotter visual toe voor 5.2.4
                                        visual_text = "\nFiguur 5.1: Kotter's 8 Stappenmodel\n"
                                        visual_p = doc.paragraphs[l].insert_paragraph_before(visual_text)
                                        visual_p.style = doc.styles['Normal']
                                        
                                        # Voeg afbeelding toe
                                        if os.path.exists('kotter_8_steps_model_new.png'):
                                            run = visual_p.add_run()
                                            run.add_picture('kotter_8_steps_model_new.png', width=Inches(6))
                                        
                                        print("✅ Kotter visual verplaatst naar 5.2.3")
                                        kotter_visual_found = True
                                        break
                                break
                        break
            if kotter_visual_found:
                break
    
    # 5. Controleer Hofstede visual bij 2.3
    print("\n5. Controleren Hofstede visual bij 2.3...")
    
    hofstede_visual_correct = False
    for i, paragraph in enumerate(doc.paragraphs):
        if "Figuur 2.2: Hofstede Gap-analyse Euro Caps" in paragraph.text:
            print(f"Gevonden Hofstede visual label op regel {i}")
            
            # Check of er een afbeelding bij staat
            for j in range(i, min(i + 3, len(doc.paragraphs))):
                for run in doc.paragraphs[j].runs:
                    if run._element.xpath('.//a:blip'):
                        print("✅ Hofstede visual correct geplaatst")
                        hofstede_visual_correct = True
                        break
                if hofstede_visual_correct:
                    break
            break
    
    if not hofstede_visual_correct:
        print("❌ Hofstede visual niet gevonden, toevoegen...")
        
        # Zoek 2.3 en voeg Hofstede visual toe
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip() == "2.3 Gap-analyse & Hofstede-model":
                # Zoek einde van 2.3 content
                for j in range(i + 1, len(doc.paragraphs)):
                    if doc.paragraphs[j].style.name.startswith('Heading'):
                        # Voeg Hofstede visual toe
                        visual_text = "\nFiguur 2.2: Hofstede Gap-analyse Euro Caps\n"
                        visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                        visual_p.style = doc.styles['Normal']
                        
                        # Voeg afbeelding toe
                        if os.path.exists('hofstede_gap_analysis.png'):
                            run = visual_p.add_run()
                            run.add_picture('hofstede_gap_analysis.png', width=Inches(6))
                        
                        print("✅ Hofstede visual toegevoegd bij 2.3")
                        break
                break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HEADINGS_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HEADINGS_FIXED.docx")
    print("✅ Managementsamenvatting toegevoegd")
    print("✅ 2.5 Stakeholderanalyse heading gefixed")
    print("✅ 5.2.3 Kotter heading gefixed")
    print("✅ Kotter visual verplaatst naar 5.2.3")
    print("✅ Hofstede visual gecontroleerd bij 2.3")

if __name__ == "__main__":
    fix_management_samenvatting_en_headings()

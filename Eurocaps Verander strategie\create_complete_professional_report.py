#!/usr/bin/env python3
"""
Script om het complete professionele adviesrapport te maken met alle verbeteringen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from docx.shared import RGBColor
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import os

def add_hyperlink(paragraph, url, text):
    """Voegt een hyperlink toe aan een paragraaf"""
    part = paragraph.part
    r_id = part.relate_to(url, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)
    
    hyperlink = OxmlElement('w:hyperlink')
    hyperlink.set(qn('r:id'), r_id)
    
    new_run = OxmlElement('w:r')
    rPr = OxmlElement('w:rPr')
    
    # Maak de tekst blauw en onderstreept
    color = OxmlElement('w:color')
    color.set(qn('w:val'), '0000FF')
    rPr.append(color)
    
    u = OxmlElement('w:u')
    u.set(qn('w:val'), 'single')
    rPr.append(u)
    
    new_run.append(rPr)
    new_run.text = text
    hyperlink.append(new_run)
    
    paragraph._p.append(hyperlink)
    return hyperlink

def create_table_with_data(doc, headers, data, caption=""):
    """Maakt een tabel met data"""
    if caption:
        caption_para = doc.add_paragraph(caption)
        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        caption_run = caption_para.runs[0]
        caption_run.bold = True
    
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Voeg headers toe
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        hdr_cells[i].paragraphs[0].runs[0].bold = True
    
    # Voeg data toe
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
    
    return table

def add_visual_placeholder(doc, visual_name, caption):
    """Voegt een visual toe als deze bestaat, anders een placeholder"""
    visual_path = f"{visual_name}"
    
    if os.path.exists(visual_path):
        try:
            # Voeg caption toe
            caption_para = doc.add_paragraph()
            caption_run = caption_para.add_run(caption)
            caption_run.bold = True
            caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Voeg afbeelding toe
            doc.add_picture(visual_path, width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            doc.add_paragraph()
            return True
        except Exception as e:
            print(f"Kon {visual_path} niet toevoegen: {e}")
    
    # Voeg placeholder toe
    placeholder = doc.add_paragraph()
    placeholder_run = placeholder.add_run(f"[{caption}]")
    placeholder_run.italic = True
    placeholder.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph()
    return False

def create_complete_professional_report():
    """Maakt het complete professionele adviesrapport"""
    
    # Maak een nieuw document
    doc = Document()
    
    # VOORPAGINA
    title = doc.add_heading('Adviesrapport Veranderingsmanagement:', level=1)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('Optimalisatie van Euro Caps door Gerichte Structuur- en Cultuurverandering met Six Sigma', level=2)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    # School informatie
    school_info = doc.add_paragraph()
    school_info.add_run('Hogeschool Rotterdam BIM\n').bold = True
    school_info.add_run('Naam: Shuja Schadon\n')
    school_info.add_run('Studentnummer: 1066741\n')
    school_info.add_run('Onderwijsperiode: OP4\n')
    school_info.add_run('Plaats en datum: Rotterdam, 5 juli 2025\n')
    school_info.add_run('Docenten: Robert Vlug en Aicha Manuela Martijn\n')
    school_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # MANAGEMENTSAMENVATTING
    doc.add_heading('MANAGEMENTSAMENVATTING', level=1)
    
    ms_text = """Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd.

Het theoretisch kader in Hoofdstuk 2 licht de fundamenten van verandermanagement toe, waaronder strategieën van Boonstra (2018), de veranderkleuren van De Caluwé & Vermaak (2009), Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010), Kotter's achtstappenmodel (Kotter, 1996), stakeholderanalyse en de verandercurve van Kübler-Ross (1969).

Hoofdstuk 3 biedt een grondige analyse van de huidige organisatiestructuur en -cultuur van Euro Caps volgens Mintzberg (1983) en identificeert concrete knelpunten die de implementatie van Six Sigma belemmeren. Vervolgens schetst Hoofdstuk 4 de gewenste situatie, met een focus op een flexibelere structuur en een meer mensgerichte, lerende cultuur, essentieel voor continue kwaliteitsverbetering.

De kern van dit rapport, Hoofdstuk 5, ontvouwt een gedetailleerde veranderstrategie en implementatieplan volgens Kotter's achtstappenmodel, waarbij Six Sigma's DMAIC-cyclus een integrale rol speelt in de operationele uitvoering over een periode van 21 maanden. Dit omvat ook een uitgebreide stakeholdersanalyse en de aanpak van mogelijke weerstanden op basis van Kübler-Ross.

Tenslotte beschrijft Hoofdstuk 6 een concreet communicatieplan, afgestemd op de mensbeelden van De Caluwé, hoe de boodschap effectief wordt overgebracht aan alle betrokkenen. De conclusie in Hoofdstuk 7 vat de bevindingen samen en mondt uit in concrete aanbevelingen die Euro Caps in staat stellen een duurzaam competitieve en lerende organisatie te worden."""
    
    doc.add_paragraph(ms_text)
    
    doc.add_page_break()
    
    # VOORWOORD
    doc.add_heading('VOORWOORD', level=1)
    
    voorwoord_text = """Graag wil ik mijn docenten Robert Vlug en Aicha Manuela Martijn bedanken voor het beschikbaar stellen van de benodigde documenten en begeleiding tijdens dit project. Hun expertise en ondersteuning hebben bijgedragen aan de totstandkoming van dit adviesrapport.

De aanleiding voor dit rapport is de positieve beoordeling van een eerder opgesteld adviesdocument door Euro Caps. Deze voortzetting biedt de mogelijkheid om dieper in te gaan op de implementatie van structurele en culturele veranderingen die nodig zijn om de ingezette kwaliteitsverbetering, in het bijzonder via de Six Sigma methodiek, duurzaam te borgen.

De keuze voor dit onderwerp is gemotiveerd door de cruciale rol die organisatorische flexibiliteit en een lerende cultuur spelen in het succesvol doorvoeren van continue procesverbeteringen. De Six Sigma methodiek kan pas haar volledige potentieel benutten wanneer de omringende organisatie en haar medewerkers optimaal zijn afgestemd op en betrokken zijn bij dit streven naar perfectie."""
    
    doc.add_paragraph(voorwoord_text)
    
    doc.add_page_break()
    
    # INHOUDSOPGAVE
    doc.add_heading('INHOUDSOPGAVE', level=1)
    
    toc_items = [
        'Managementsamenvatting',
        'Voorwoord',
        '',
        'Hoofdstuk 1: Inleiding',
        '    1.1 Deskresearch methode',
        '    1.2 Leeswijzer',
        'Hoofdstuk 2: Theoretisch kader',
        '    2.1 Veranderstrategieën volgens Boonstra',
        '    2.2 Veranderkleuren van De Caluwé',
        '    2.3 Gap-analyse & Hofstede-model',
        '    2.4 Kotter\'s 8 Stappenmodel',
        '    2.5 Stakeholderanalyse',
        '    2.6 Verandercurve van Kübler-Ross',
        'Hoofdstuk 3: Huidige situatie',
        '    3.1 Huidige organisatiestructuur',
        '    3.2 Huidige organisatiecultuur',
        '    3.3 Deelconclusie',
        'Hoofdstuk 4: Gewenste situatie',
        '    4.1 Gewenste organisatiestructuur',
        '    4.2 Gewenste organisatiecultuur',
        '    4.3 Deelconclusie',
        'Hoofdstuk 5: Veranderstrategie + implementatieplan',
        '    5.1 Voorbereidende deel',
        '        5.1.1 Organisatiestructuur veranderingen',
        '        5.1.2 Organisatiecultuur veranderingen',
        '        5.1.3 Stakeholdersanalyse',
        '        5.1.4 Mogelijke weerstanden van Kübler-Ross',
        '    5.2 Uitvoerende deel',
        '        5.2.1 Strategische veranderaanpak',
        '        5.2.2 Veranderstrategie Boonstra',
        '        5.2.3 Veranderaanpak Kotter (in fasen)',
        '        5.2.4 Interventies van de stakeholder',
        '    5.3 Deelconclusie',
        'Hoofdstuk 6: Communicatieplan',
        '    6.1 Overzicht communicatieplan',
        'Hoofdstuk 7: Conclusie',
        'Aanbevelingen',
        'Literatuurlijst',
        'Bijlagen'
    ]
    
    for item in toc_items:
        doc.add_paragraph(item)
    
    doc.add_page_break()
    
    # HOOFDSTUK 1: INLEIDING
    doc.add_heading('HOOFDSTUK 1: INLEIDING', level=1)
    
    h1_intro = """Dit inleidende hoofdstuk schetst de achtergrond en het doel van dit adviesrapport voor Euro Caps. Het bouwt voort op eerdere bevindingen en aanbevelingen met betrekking tot kwaliteitsmanagement binnen Euro Caps, specifiek de reeds vastgestelde keuze voor de Six Sigma methodiek met het DMAIC-raamwerk (Bron: Six Sigma interne projectdocumentatie Euro Caps). Er wordt ingegaan op de noodzaak om, naast procesoptimalisatie, ook de organisatiestructuur en -cultuur te analyseren en waar nodig aan te passen om de effectiviteit van Six Sigma te maximaliseren en duurzame verbetering te garanderen."""
    doc.add_paragraph(h1_intro)
    
    h1_context = """Euro Caps staat voor belangrijke operationele en organisatorische uitdagingen die vragen om een gestructureerde veranderingsaanpak. In de vorige onderwijsperiode is een voorstel gedaan tot veranderingen, waarbij interne processen van Euro Caps onder de loep zijn genomen en mogelijkheden tot automatisering zijn onderzocht. Hierbij zijn knelpunten herkend en is de Six Sigma methodiek gekozen als passende kwaliteitsmanagementmethode om deze knelpunten te verhelpen, specifiek gericht op het optimaliseren van het vulproces van koffiecapsules en het minimaliseren van defecten volgens het DMAIC-raamwerk.

De centrale vraag die in dit rapport wordt beantwoord, luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Het doel van dit rapport is om een onderbouwd veranderplan te formuleren en een praktisch uitvoerbaar plan op te stellen waarmee Euro Caps de organisatorische transitie succesvol kan realiseren. Hierbij worden zowel interne als externe factoren in kaart gebracht en vertaald naar een concreet actie-, communicatie- en implementatieplan."""
    doc.add_paragraph(h1_context)
    
    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', level=2)

    desk_text = """De methode van onderzoek bestaat primair uit deskresearch, waarbij gebruik is gemaakt van relevante literatuur en theorieën op het gebied van organisatiestructuur, organisatiecultuur, verandermanagement, communicatie en stakeholdermanagement. Specifiek zijn theorieën van Mintzberg over coördinatiemechanismen (Mintzberg, 1983) en Hofstede's cultuurdimensies voor organisaties (Hofstede, Hofstede & Minkov, 2010) toegepast om de huidige en gewenste situatie te analyseren. Voor de veranderstrategie is geput uit de BDK-theorie van Boonstra (Boonstra, 2018) en het achtstappenmodel van Kotter (Kotter, 1996), aangevuld met communicatieprincipes gebaseerd op de mensbeelden van De Caluwé (De Caluwé & Vermaak, 2009). De theorie van Kübler-Ross over de fasen van rouw (Kübler-Ross, 1969) is ingezet voor de analyse van mogelijke weerstanden."""
    doc.add_paragraph(desk_text)

    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', level=2)

    lees_text = """Dit adviesrapport is als volgt gestructureerd: Hoofdstuk 2 presenteert het theoretisch kader dat de basis vormt voor de analyse. Hoofdstuk 3 beschrijft de huidige situatie van Euro Caps, met aandacht voor de organisatiestructuur en -cultuur. Hoofdstuk 4 schetst de gewenste situatie, eveneens op het vlak van structuur en cultuur. Hoofdstuk 5 formuleert de veranderstrategie en het implementatieplan, inclusief een stakeholdersanalyse en een inschatting van mogelijke weerstanden. Hoofdstuk 6 detailleert het communicatieplan. Ten slotte biedt Hoofdstuk 7 de conclusie en concrete aanbevelingen voor Euro Caps."""
    doc.add_paragraph(lees_text)

    doc.add_page_break()

    # HOOFDSTUK 2: THEORETISCH KADER
    doc.add_heading('HOOFDSTUK 2: THEORETISCH KADER', level=1)

    h2_intro = """Dit hoofdstuk is van fundamenteel belang voor het adviesrapport, aangezien het de theoretische grondslagen legt voor alle verdere analyses en aanbevelingen. Hierin worden de cruciale concepten en modellen geïntroduceerd en kort toegelicht, die onmisbaar zijn voor een diepgaand begrip van organisatiestructuren, -culturen en verandermanagementprocessen. Deze theoretische kaders zullen als leidraad dienen voor de beschrijving van zowel de huidige als de gewenste situatie, de selectie van de veranderstrategie en de ontwikkeling van het communicatieplan."""
    doc.add_paragraph(h2_intro)

    # 2.1 Veranderstrategieën volgens Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', level=2)

    boonstra_text = """Boonstra (2018) onderscheidt verschillende strategieën voor organisatieverandering, waarbij de keuze afhangt van factoren zoals urgentie, organisatiecultuur en gewenste betrokkenheid van medewerkers. De ontwikkelingsstrategie richt zich op verandering door collectief leren en participatie, wat tijdrovend maar duurzaam is. Een ingrijpende strategie kenmerkt zich door een top-down aanpak en snelheid, vooral geschikt bij urgente situaties, maar met risico op weerstand. De machtsstrategie legt de nadruk op controle en hiërarchie en is effectief in sterk taakgerichte contexten. Een onderhandelingsstrategie tracht belangen samen te brengen via overleg en vraagt tijd, maar creëert daardoor breed draagvlak. Tot slot stimuleert de verleidingsstrategie motivatie via voorbeeldgedrag en communicatie, wat goed werkt in mensgerichte culturen. Deze differentiatie biedt een kader voor het kiezen van de meest geschikte aanpak voor Euro Caps."""
    doc.add_paragraph(boonstra_text)

    # Voeg visual placeholder toe
    add_visual_placeholder(doc, "Strategische veranderaanpak.png", "Figuur 2.1: Boonstra's veranderstrategieën matrix")

    # 2.2 Veranderkleuren van De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', level=2)

    caluwe_text = """Het kleurenmodel van De Caluwé (De Caluwé & Vermaak, 2009) biedt een methodiek om verschillende 'denklogica's' over veranderen te visualiseren en te begrijpen. Blauwdrukdenken (Blauw) staat voor planmatige en rationele veranderingen, geschikt bij voorspelbare trajecten waarbij de uitkomst vooraf vaststaat. Geeldrukdenken (Geel) draait om macht, invloed en belangen, en is passend bij politieke omgevingen waar onderhandeling en coalitievorming essentieel zijn. Rooddrukdenken (Rood) focust op het motiveren van mensen door te bouwen aan relaties en samenwerking. Groendrukdenken (Groen) kenmerkt zich door leren en ontwikkelen, waarbij verandering ontstaat uit experiment en reflectie. Witdrukken (Wit) omarmt emergentie en de complexiteit van verandering die niet volledig te plannen is, en ziet verandering als een continu proces van co-creatie. Dit model helpt bij het afstemmen van communicatiestrategieën op de specifieke mindset van diverse stakeholdergroepen."""
    doc.add_paragraph(caluwe_text)

    # Voeg visual placeholder toe voor De Caluwé kleuren
    add_visual_placeholder(doc, "de_caluwe_kleuren.png", "Figuur 2.2: De Caluwé's kleurenmodel voor verandering")

    # 2.3 Gap-analyse & Hofstede-model
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', level=2)

    gap_text = """Een Gap-analyse is een essentiële tool voor verandermanagement die het verschil tussen de huidige situatie en de gewenste situatie in kaart brengt. Door deze 'kloof' te identificeren, kunnen specifieke veranderacties worden gepland om de gewenste staat te bereiken."""
    doc.add_paragraph(gap_text)

    # Voeg visual placeholder toe voor Gap-analyse
    add_visual_placeholder(doc, "gap_analyse.png", "Figuur 2.3: Gap-analyse model")

    hofstede_text = """Voor de analyse van de organisatiecultuur wordt gebruik gemaakt van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010), die zes fundamentele dimensies van organisatiecultuur onderscheiden en zo helpen bij het typeren van de normen, waarden en gedragingen binnen een organisatie. Deze dimensies zijn:

1. Machtsafstand: De mate waarin minder machtige leden van organisaties accepteren dat macht ongelijk verdeeld is.
2. Individualisme versus Collectivisme: Geeft aan of de nadruk ligt op individuele prestatie en onafhankelijkheid, of op groepsloyaliteit en onderlinge verbondenheid.
3. Masculien versus Feminien: Een cultuur met een masculiene oriëntatie hecht waarde aan assertiviteit, competitie en materiële successen, terwijl een feminiene cultuur gericht is op samenwerking, bescheidenheid en de kwaliteit van het leven.
4. Lange- versus kortetermijngerichtheid: Deze dimensie beschrijft de focus op tradities en het verleden versus de focus op de toekomst, aanpassing en doorzettingsvermogen.
5. Onzekerheidsvermijding: De mate waarin een cultuur zich bedreigd voelt door onzekere of onbekende situaties.
6. Toegeeflijkheid versus Terughoudendheid: De mate waarin de samenleving het bevredigen van menselijke basisbehoeften en genot toestaat versus de controle hierop door strikte sociale normen."""
    doc.add_paragraph(hofstede_text)

    # Voeg visual placeholder toe voor Hofstede
    add_visual_placeholder(doc, "hofstede_dimensies.png", "Figuur 2.4: Hofstede's zes cultuurdimensies")

    # 2.4 Kotter's 8 Stappenmodel
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', level=2)

    kotter_text = """Kotter (1996) heeft een gestructureerd achtstappenmodel ontwikkeld dat dient als leidraad voor succesvolle organisatieverandering. Dit model begint met het creëren van urgentiebesef en het vormen van een leidende coalitie, stappen die essentieel zijn voor het opbouwen van momentum. De volgende stappen omvatten het ontwikkelen van een visie en strategie, en het effectief communiceren van deze visie om draagvlak te creëren. Verdere stappen richten zich op het wegnemen van obstakels, het genereren van korte-termijn successen, het consolideren van verbeteringen voor duurzame verandering, en het uiteindelijk verankeren van de nieuwe benaderingen in de organisatiecultuur. Dit sequentiële model biedt een praktische aanpak voor complexe verandertrajecten.

Het model wordt in dit rapport niet als afzonderlijke stappen behandeld, maar geïntegreerd in drie hoofdfasen van implementatie, waarbij elke fase meerdere stappen van Kotter omvat voor een meer holistische benadering."""
    doc.add_paragraph(kotter_text)

    # Voeg visual placeholder toe voor Kotter - AANGEPAST NAAR FASEN
    add_visual_placeholder(doc, "kotter 8 stappenmodel voorbeeld.png", "Figuur 2.5: Kotter's 8-stappenmodel georganiseerd in fasen")

    # 2.5 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', level=2)

    stakeholder_text = """Een stakeholderanalyse is een belangrijke tool in verandermanagement om de invloed en belangen van alle betrokken partijen bij een verandering te analyseren (Boonstra, 2018). Het proces begint met het identificeren van alle stakeholders, zowel intern als extern. Vervolgens vindt een categorisatie van deze stakeholders plaats op basis van hun macht (invloed) en belang ten opzichte van het project, om zo prioriteit en een passende communicatie- en betrokkenheidsstrategie te bepalen. Dit inzicht is cruciaal voor het managen van verwachtingen en het mitigeren van weerstand."""
    doc.add_paragraph(stakeholder_text)

    # Voeg visual placeholder toe voor Stakeholderanalyse
    add_visual_placeholder(doc, "stakeholder_matrix.png", "Figuur 2.6: Power/Interest matrix voor stakeholderanalyse")

    # 2.6 Verandercurve van Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', level=2)

    kubler_text = """De verandercurve van Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele fases te duiden die mensen doorlopen wanneer zij geconfronteerd worden met ingrijpende veranderingen. Deze fases omvatten ontkenning, waarbij de verandering wordt afgewezen; frustratie, die kan leiden tot weerstand en woede; onderhandeling, een poging om de verandering af te zwakken of te beïnvloeden; depressie, een gevoel van onmacht of verdriet over het verlies van het oude; en uiteindelijk acceptatie, waarbij de nieuwe situatie wordt omarmd. Het begrijpen van deze curve helpt leidinggevenden om menselijke reacties op verandering te anticiperen en effectief te begeleiden."""
    doc.add_paragraph(kubler_text)

    # Voeg visual placeholder toe voor Kübler-Ross
    add_visual_placeholder(doc, "kubler_ross_curve.png", "Figuur 2.7: Verandercurve van Kübler-Ross")

    doc.add_page_break()

    # HOOFDSTUK 3: HUIDIGE SITUATIE
    doc.add_heading('HOOFDSTUK 3: HUIDIGE SITUATIE', level=1)

    h3_intro = """Dit hoofdstuk is gewijd aan een diepgaande analyse van de huidige organisatorische context van Euro Caps. Het biedt een helder beeld van hoe de organisatie momenteel functioneert, zowel op het vlak van structuur als cultuur. Door de toepassing van de eerder geïntroduceerde theoretische kaders, worden de dominante kenmerken en eventuele knelpunten van de huidige situatie blootgelegd, wat een cruciale basis vormt voor het formuleren van effectieve veranderingsstrategieën."""
    doc.add_paragraph(h3_intro)

    # 3.1 Huidige organisatiestructuur
    doc.add_heading('3.1 Huidige organisatiestructuur', level=2)

    h31_text = """De organisatiestructuur van Euro Caps wordt gekenmerkt door functionele silo's (Operatie, MCO, Sales, Finance, IT) met beperkte horizontale communicatie. De toepassing van coördinatiemechanismen van Mintzberg (1983) toont een beperkte onderlinge aanpassing door deze silo-vorming. Direct toezicht is sterk aanwezig, met name in de productieprocessen. Standaardisatie van werkprocessen ontbreekt in veel processen, terwijl standaardisatie van output voornamelijk voor eindproducten geldt. Standaardisatie van vaardigheden is beperkt, vooral tussen afdelingen.

Voor Euro Caps is het van belang een organisatiestructuur te kiezen die aansluit bij haar ambities en de behoeften van haar operaties. Een beslissingsmatrix op basis van het Mintzberg-model (Mintzberg, 1983) vergelijkt verschillende typen organisaties op basis van hun kenmerken en relevantie voor Euro Caps."""
    doc.add_paragraph(h31_text)

    # Voeg visual placeholder toe voor huidige organisatiestructuur
    add_visual_placeholder(doc, "Huidige Organisatie structuur.png", "Figuur 3.1: Huidige organisatiestructuur Euro Caps")

    # Maak beslissingsmatrix tabel
    headers = ['Organisatiestructuur', 'Standaardisatie & controle', 'Innovatievermogen', 'Flexibiliteit & klantgerichtheid', 'Technologische innovatie', 'Productefficiëntie', 'Klantgerichtheid & efficiëntie', 'Score']
    data = [
        ['Ondernemende organisatie', '2', '5', '4', '3', '2', '3', '19'],
        ['Machinebureaucratie', '5', '1', '1', '3', '5', '4', '19'],
        ['Professionele organisatie', '4', '2', '2', '2', '4', '3', '17'],
        ['Gediversifieerde organisatie', '3', '2', '3', '3', '3', '3', '17'],
        ['Innovatieve organisatie', '1', '5', '5', '4', '1', '2', '18'],
        ['Missionaire organisatie', '2', '2', '3', '2', '2', '3', '14'],
        ['Politieke organisatie', '1', '1', '1', '2', '1', '2', '8']
    ]

    create_table_with_data(doc, headers, data, "Tabel 3.1: Beslissingsmatrix organisatiestructuur (Mintzberg, 1983)")

    h31_analyse = """Uit deze beslissingsmatrix blijkt dat de Machinebureaucratie en de Ondernemende organisatie beide een score van 19 behalen, gevolgd door de Innovatieve organisatie met een score van 18. Dit bevestigt de eerdere waarneming dat Euro Caps kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie. De machinebureaucratie is duidelijk zichtbaar in de gestandaardiseerde productieprocessen en de nadruk op efficiëntie in de massaproductie, waarbij dagelijks ongeveer 3 miljoen capsules worden geproduceerd (Bron: Euro Caps productiedata, 2024)."""
    doc.add_paragraph(h31_analyse)

    # 3.2 Huidige organisatiecultuur
    doc.add_heading('3.2 Huidige organisatiecultuur', level=2)

    h32_text = """Wat betreft de huidige organisatiecultuur van Euro Caps, kan deze worden geanalyseerd aan de hand van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010). Euro Caps kenmerkt zich door een relatief kleine machtsafstand, wat blijkt uit de toegankelijkheid van het management en de open communicatie tussen verschillende organisatieniveaus. Er is een balans tussen individualisme en collectivisme, waarbij individueel initiatief wordt aangemoedigd, maar teamwerk en samenwerking centraal staan. De cultuur neigt naar femininiteit met een focus op goede werkrelaties, samenwerking en zorg voor medewerkers via opleidingen en persoonlijke ontwikkeling.

De onzekerheidsvermijding is gemiddeld tot hoog door gestandaardiseerde werkprocessen, maar de organisatie biedt wel ruimte voor innovatie. De organisatie heeft een lange termijn oriëntatie, gericht op duurzame groei en ontwikkeling, zoals blijkt uit investeringen in nieuwe productiefaciliteiten en de ambitie om in tien jaar tijd tien miljard capsules te produceren (Bron: Euro Caps strategisch plan 2024-2034). Tot slot toont Euro Caps een cultuur van toegeeflijkheid met ruimte voor plezier naast hard werken."""
    doc.add_paragraph(h32_text)

    # Voeg visual placeholder toe voor Hofstede analyse
    add_visual_placeholder(doc, "hofstede_euro_caps.png", "Figuur 3.2: Hofstede dimensies analyse Euro Caps")

    kernwaarden_text = """De kernwaarden van het bedrijf, 'Ondernemend' ("Kan niet bestaat niet"), 'Partnerschap', 'Eigenzinnig' (ruimte voor ideeën en feedback) en 'Nu' (focus op heden, leren van verleden, kijken naar toekomst), bevestigen de beschreven culturele kenmerken en tonen aan dat de organisatie al een basis heeft voor verandering en aanpassing. Deze combinatie van een machineachtige structuur met innovatieve elementen, en een cultuur die openstaat voor ontwikkeling, maakt Euro Caps uniek."""
    doc.add_paragraph(kernwaarden_text)

    # 3.3 Deelconclusie
    doc.add_heading('3.3 Deelconclusie', level=2)

    h33_text = """De huidige situatie van Euro Caps kenmerkt zich door een functionele organisatiestructuur die een combinatie is van een machineorganisatie met sterke standaardisatie van werkprocessen en output, en elementen van een innovatieve organisatie met wederzijdse aanpassing (Mintzberg, 1983). De cultuur is een mix van resultaatgerichtheid en mensgerichtheid, met een relatief lage machtsafstand, neigend naar collectivisme en femininiteit, en een gemiddelde onzekerheidsvermijding met langetermijnfocus (Hofstede, Hofstede & Minkov, 2010). Deze eigenschappen, samen met de geïdentificeerde knelpunten in productieprocessen, communicatie en IT-systemen, wijzen op een sterke behoefte aan verandering om de Six Sigma implementatie te optimaliseren en duurzame verbetering te realiseren."""
    doc.add_paragraph(h33_text)

    doc.add_page_break()

    # HOOFDSTUK 4: GEWENSTE SITUATIE
    doc.add_heading('HOOFDSTUK 4: GEWENSTE SITUATIE', level=1)

    h4_intro = """In dit hoofdstuk wordt het toekomstbeeld voor Euro Caps geschetst, waarbij de ideale staat van zowel de organisatiestructuur als de organisatiecultuur wordt gedefinieerd. Voortbouwend op de gedetailleerde analyse van de huidige situatie, wordt hier beschreven hoe Euro Caps zich kan ontwikkelen om niet alleen de effectiviteit van de Six Sigma methodiek te maximaliseren, maar ook om als organisatie adaptiever, innovatiever en duurzaam succesvol te zijn. Dit omvat een optimale balans tussen efficiëntie en flexibiliteit, en tussen procesfocus en een versterkte mensgerichtheid."""
    doc.add_paragraph(h4_intro)

    # 4.1 Gewenste organisatiestructuur
    doc.add_heading('4.1 Gewenste organisatiestructuur', level=2)

    h41_text = """De gewenste situatie voor Euro Caps bouwt voort op de sterke punten van de huidige organisatie, met een versterking van flexibiliteit, innovatie en adaptief vermogen, essentieel voor een voortdurende verbetering en een duurzame concurrentiepositie. De Six Sigma methodiek, hoewel uitstekend voor procesoptimalisatie, vereist een organisatie die openstaat voor continue verbetering en veranderingen effectief kan implementeren en borgen. De integratie van HACCP-elementen in Six Sigma, zoals eerder gedefinieerd in de basisdocumentatie, benadrukt de noodzaak van een hybride aanpak die zowel kwaliteit als voedselveiligheid optimaliseert.

De gewenste organisatiestructuur voor Euro Caps zal nog steeds een solide basis hebben in de standaardisatie van werkprocessen en output, aangezien precisie en kwaliteit essentieel blijven in de voedingsmiddelenindustrie (Mintzberg, 1983). Echter, er dient meer nadruk te komen op wederzijdse aanpassing, met name op de hogere managementniveaus en bij multidisciplinaire projectteams die betrokken zijn bij Six Sigma-projecten. Dit betekent dat er meer ruimte komt voor overleg en informele communicatie tussen afdelingen om problemen gezamenlijk op te lossen en processen te optimaliseren."""
    doc.add_paragraph(h41_text)

    # 4.2 Gewenste organisatiecultuur
    doc.add_heading('4.2 Gewenste organisatiecultuur', level=2)

    h42_text = """De organisatiestructuur van Euro Caps is momenteel sterk gericht op structuur, controle en het behalen van resultaten. Door hoge productieniveaus en vaste werkinstructies ligt de nadruk op taakgericht werken, standaardisatie en prestatiebewaking via KPI's. Met de verdere integratie van de Six Sigma methodiek, met de nadruk op datagestuurde procesoptimalisatie en continue verbetering, zijn er duidelijke cultuurveranderingen nodig om deze systemen succesvol te implementeren en duurzaam te verankeren in de organisatie.

Een belangrijke verschuiving is de toename van zelfstandigheid bij medewerkers. Voorheen werkten zij mogelijk meer op basis van directe instructies van hun leidinggevende. In de nieuwe situatie, aangedreven door de data en analyses uit Six Sigma, zullen taken vaker gebaseerd zijn op systematische inzichten. Medewerkers moeten hierdoor in toenemende mate zelfstandig kunnen werken, snel kunnen schakelen bij afwijkingen en een dieper inzicht hebben in hun verantwoordelijkheden binnen het geoptimaliseerde proces."""
    doc.add_paragraph(h42_text)

    # 4.3 Deelconclusie
    doc.add_heading('4.3 Deelconclusie', level=2)

    h43_text = """De gewenste situatie voor Euro Caps kenmerkt zich door een robuuste organisatiestructuur die een balans vindt tussen standaardisatie en flexibiliteit door middel van versterkte wederzijdse aanpassing en standaardisatie van vaardigheden (Mintzberg, 1983). De organisatiecultuur zal naast de bestaande resultaat- en procesgerichtheid, een grotere nadruk leggen op mensgerichtheid, leren, en openheid (Hofstede, Hofstede & Minkov, 2010), waardoor een cultuur van continue verbetering en innovatie verder wordt gestimuleerd en geborgd. Specifiek vereisen de Six Sigma implementaties een verhoogde zelfstandigheid van medewerkers, een verschuiving in leiderschapsstijl naar coaching, en een cultuur van duidelijke afspraken en eigenaarschap in interafdelingssamenwerking."""
    doc.add_paragraph(h43_text)

    doc.add_page_break()

    # HOOFDSTUK 5: VERANDERSTRATEGIE + IMPLEMENTATIEPLAN
    doc.add_heading('HOOFDSTUK 5: VERANDERSTRATEGIE + IMPLEMENTATIEPLAN', level=1)

    h5_intro = """Dit hoofdstuk vormt de kern van het adviesrapport, aangezien hier de specifieke veranderstrategie voor Euro Caps wordt uiteengezet en een concreet implementatieplan wordt gepresenteerd. Het begint met een gedetailleerd voorbereidende fase, die de noodzakelijke structurele en culturele veranderingen identificeert en de betrokken stakeholders analyseert op mogelijke weerstanden. Vervolgens wordt dieper ingegaan op de onderbouwing van de gekozen veranderstrategie en de implementatie ervan, inclusief de cruciale rol van Six Sigma in dit gehele transformatieproces."""
    doc.add_paragraph(h5_intro)

    # 5.1 Voorbereidende deel
    doc.add_heading('5.1 Voorbereidende deel', level=2)

    h51_intro = """De voorbereiding op de verandering omvat een zorgvuldige analyse van de te verwachten aanpassingen in zowel structuur als cultuur, gevolgd door een diepgaande stakeholdersanalyse en een inschatting van mogelijke weerstanden."""
    doc.add_paragraph(h51_intro)

    # 5.1.1 Organisatiestructuur veranderingen
    doc.add_heading('5.1.1 Organisatiestructuur veranderingen', level=3)

    h511_text = """De structurele veranderingen richten zich op het faciliteren van meer wederzijdse aanpassing en standaardisatie van vaardigheden binnen Euro Caps, ter ondersteuning van de Six Sigma aanpak (Mintzberg, 1983). Dit betekent het implementeren van meer multidisciplinaire teams, vooral rondom specifieke Six Sigma projecten (DMAIC-cycli), waarin medewerkers van verschillende afdelingen met diverse expertise samenwerken aan procesverbeteringen. De oprichting van 'werkgroepen kwaliteit' of 'verbeterkringen' kan de dialoog en probleemoplossing bevorderen."""
    doc.add_paragraph(h511_text)

    # 5.1.2 Organisatiecultuur veranderingen
    doc.add_heading('5.1.2 Organisatiecultuur veranderingen', level=3)

    h512_text = """De culturele veranderingen zijn gericht op het cultiveren van een mensgerichte en lerende organisatiecultuur, naast de bestaande focus op kwaliteit en resultaten (Hofstede, Hofstede & Minkov, 2010). Dit vereist het proactief stimuleren van feedback en dialoog, zowel top-down als bottom-up. Trainingen in communicatievaardigheden en conflictbemiddeling voor leidinggevenden zijn essentieel om een open en veilige omgeving te creëren waarin medewerkers zich vrij voelen om ideeën en zorgen te uiten."""
    doc.add_paragraph(h512_text)

    # 5.1.3 Stakeholdersanalyse
    doc.add_heading('5.1.3 Stakeholdersanalyse', level=3)

    h513_text = """Voor een succesvolle implementatie is het essentieel om een helder beeld te hebben van de betrokken stakeholders. De onderstaande tabellen bieden een overzicht van de belangrijkste stakeholders, hun rol, mate van betrokkenheid, belang, positie en invloed, alsook een categorisatie voor een goede uitvoering van het implementatieadvies."""
    doc.add_paragraph(h513_text)

    # Stakeholder overzicht tabel
    stakeholder_headers = ['Stakeholdergroep', 'Naam stakeholder', 'Functie', 'Betrokkenheid', 'Belang', 'Positie', 'Invloed']
    stakeholder_data = [
        ['ICT', 'Erik Dekker', 'Manager ICT', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Productie', 'Maik Ritter', 'Productiemanager', 'Gemiddeld', 'Hoog', 'Blocker', 'Hoog'],
        ['CEO', 'Nils Clement', 'CEO', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Logistiek', 'Rijk Wegen', 'Manager Logistiek', 'Gemiddeld', 'Hoog', 'Floater', 'Hoog'],
        ['Productie', 'Ismail Berenschot', 'Productiemedewerker', 'Gemiddeld', 'Gemiddeld', 'Blocker', 'Laag'],
        ['Logistiek', 'Tila Karren', 'Medewerker Logistiek', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Inkoop', 'Ko Jager', 'Manager Inkoop', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Bedrijfsvoering', 'Servé Bosland', 'Manager Bedrijfsvoering', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['HR', 'Uwe Regel', 'HR Manager', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Laag'],
        ['Productie', 'Maria Stanić', 'Productiemanager', 'Gemiddeld', 'Hoog', 'Blocker', 'Hoog'],
        ['Kwaliteitsbeheer', 'Kees Keurig', 'Hoofd Kwaliteitsbeheer', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Gemiddeld'],
        ['Projectleiding', 'Niene Tepe', 'Projectleider', 'Hoog', 'Hoog', 'Mover', 'Hoog'],
        ['Financiën', 'Berkan Arrindell', 'Hoofd Financiën', 'Hoog', 'Hoog', 'Mover', 'Hoog']
    ]

    create_table_with_data(doc, stakeholder_headers, stakeholder_data, "Tabel 5.1: Overzicht stakeholders Euro Caps")

    # Voeg visual placeholder toe voor stakeholder matrix
    add_visual_placeholder(doc, "stakeholder_analysis_euro_caps.png", "Figuur 5.1: Power/Interest matrix stakeholders Euro Caps")

    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_PROFESSIONEEL_VOLLEDIG.docx')
    print("Professioneel adviesrapport volledig aangemaakt: Adviesrapport_Veranderingsmanagement_PROFESSIONEEL_VOLLEDIG.docx")

if __name__ == "__main__":
    create_complete_professional_report()

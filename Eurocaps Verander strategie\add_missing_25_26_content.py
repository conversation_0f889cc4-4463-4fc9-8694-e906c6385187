#!/usr/bin/env python3
"""
Script om ontbrekende 2.5 en 2.6 uitwerkingen toe te voegen
"""

from docx import Document
from docx.shared import Inches

def add_missing_25_26_content():
    """Voegt ontbrekende uitwerkingen toe voor 2.5 en 2.6"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEKST_GECORRIGEERD.docx')
    
    print("Bezig met toevoegen ontbrekende 2.5 en 2.6 uitwerkingen...")
    
    # 1. Zoek 2.5 en voeg uitgebreide uitwerking toe
    print("\n1. Toevoegen uitgebreide 2.5 Stakeholderanalyse uitwerking...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.5 Stakeholderanalyse":
            # Zoek de volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            # Verwijder bestaande korte content
            content_to_remove = []
            j = i + 1
            while j < next_heading_idx:
                if doc.paragraphs[j].text.strip():
                    content_to_remove.append(j)
                j += 1
            
            # Verwijder van achteren naar voren
            for idx in reversed(content_to_remove):
                if idx < len(doc.paragraphs):
                    p = doc.paragraphs[idx]._element
                    p.getparent().remove(p)
            
            # Voeg uitgebreide uitwerking toe
            stakeholder_uitwerking = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Freeman's stakeholdertheorie (2010) vormt de theoretische basis voor deze benadering en onderscheidt primaire stakeholders die directe invloed hebben op de organisatie en secundaire stakeholders die indirecte invloed uitoefenen.

De stakeholderanalyse omvat vier essentiële stappen die systematisch worden doorlopen. De eerste stap behelst de identificatie van alle relevante stakeholders door middel van brainstormsessies en documentanalyse. Vervolgens wordt in de tweede stap een grondige analyse uitgevoerd van hun specifieke belangen, verwachtingen en behoeften ten aanzien van de organisatorische verandering. De derde stap richt zich op de beoordeling van hun relatieve invloed en attitude ten opzichte van de voorgestelde veranderingen, waarbij gebruik wordt gemaakt van invloed-belangmatrices. Tot slot worden in de vierde stap specifieke benaderingsstrategieën ontwikkeld voor elke stakeholdergroep, gebaseerd op hun positie in de matrix.

Voor Euro Caps is stakeholderanalyse van cruciaal belang voor het succesvol implementeren van organisatorische veranderingen ter ondersteuning van Six Sigma. Een effectieve stakeholderbenadering verhoogt de acceptatie van veranderingen, vermindert weerstand tijdens het implementatieproces en zorgt voor duurzame verankering van nieuwe werkwijzen. De analyse helpt het management om gerichte communicatie- en interventiestrategieën te ontwikkelen die aansluiten bij de specifieke behoeften en verwachtingen van verschillende stakeholdergroepen.

De stakeholdermatrix voor Euro Caps toont de positionering van verschillende partijen op basis van hun invloed en belang bij de Six Sigma implementatie. Senior management en de kwaliteitsafdeling bevinden zich in het kwadrant met hoge invloed en hoog belang, wat betekent dat zij intensief gemanaged moeten worden. Klanten en aandeelhouders hebben eveneens hoog belang maar variërende invloed, terwijl medewerkers en leveranciers verschillende posities innemen afhankelijk van hun specifieke rol in het veranderingsproces.

Figuur 2.1: Stakeholder Matrix Euro Caps

[Hier wordt de stakeholder_matrix.png visual ingevoegd]

De matrix biedt concrete handvatten voor het ontwikkelen van stakeholder-specifieke strategieën waarbij partijen met hoge invloed en hoog belang prioriteit krijgen in de communicatie en besluitvorming."""
            
            # Voeg content toe
            if next_heading_idx < len(doc.paragraphs):
                stakeholder_content = doc.paragraphs[next_heading_idx].insert_paragraph_before(stakeholder_uitwerking)
                stakeholder_content.style = doc.styles['Normal']
            
            print("Uitgebreide 2.5 uitwerking toegevoegd")
            break
    
    # 2. Zoek 2.6 en voeg uitgebreide uitwerking toe
    print("\n2. Toevoegen uitgebreide 2.6 Verandercurve uitwerking...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.6 Verandercurve van Kübler-Ross":
            # Zoek de volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            # Verwijder bestaande korte content
            content_to_remove = []
            j = i + 1
            while j < next_heading_idx:
                if doc.paragraphs[j].text.strip():
                    content_to_remove.append(j)
                j += 1
            
            # Verwijder van achteren naar voren
            for idx in reversed(content_to_remove):
                if idx < len(doc.paragraphs):
                    p = doc.paragraphs[idx]._element
                    p.getparent().remove(p)
            
            # Voeg uitgebreide uitwerking toe
            kublerross_uitwerking = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen en biedt een waardevol raamwerk voor het begrijpen van menselijke reacties op organisatorische transformaties. Oorspronkelijk ontwikkeld door Elisabeth Kübler-Ross (1969) voor het begrijpen van rouwprocessen, is het model succesvol geadapteerd voor organisatorische verandering om weerstand te begrijpen, te voorspellen en effectief te begeleiden.

De verandercurve bestaat uit vijf opeenvolgende fasen die individuen doorgaans doorlopen, waarbij de intensiteit en duur per persoon kunnen variëren. De eerste fase betreft ontkenning, waarbij medewerkers de noodzaak of realiteit van verandering weigeren te accepteren. Zij kunnen beweren dat de huidige situatie adequaat is en dat verandering onnodig of tijdelijk is. Deze fase wordt gevolgd door woede, gekenmerkt door frustratie en boosheid over het verlies van controle en vertrouwde werkwijzen. Deze emoties kunnen zich uiten in openlijke kritiek op het management, het veranderingsproces of de nieuwe werkwijzen.

De derde fase omvat onderhandeling, waarbij medewerkers pogingen ondernemen om compromissen te vinden of de verandering te beperken. Zij zoeken naar manieren om delen van de oude situatie te behouden of de impact van veranderingen te minimaliseren. Vervolgens treedt de fase van depressie op, gekenmerkt door gevoelens van verlies en onzekerheid over de persoonlijke toekomst. Productiviteit kan tijdelijk dalen en motivatie kan afnemen terwijl medewerkers worstelen met de realiteit van de verandering.

De finale fase behelst acceptatie, waarbij medewerkers bereidheid tonen om de nieuwe situatie te omarmen en constructief bij te dragen aan het succes van de verandering. Zij beginnen de voordelen van de nieuwe werkwijzen in te zien en ontwikkelen nieuwe competenties en gedragingen die aansluiten bij de organisatorische transformatie.

Voor Euro Caps is begrip van deze emotionele reis cruciaal voor het ontwikkelen van gerichte interventies tijdens de Six Sigma implementatie. Door de verschillende fasen te herkennen, kan het management passende ondersteuning bieden, realistische tijdlijnen hanteren en weerstand effectief adresseren. Het model helpt bij het ontwikkelen van communicatiestrategieën die aansluiten bij de emotionele behoeften van medewerkers in verschillende fasen van het veranderingsproces.

Figuur 2.2: Kübler-Ross Verandercurve

[Hier wordt de kubler_ross_curve.png visual ingevoegd]

De curve illustreert de emotionele impact over tijd en toont hoe medewerkers geleidelijk van weerstand naar acceptatie bewegen, wat essentiële inzichten biedt voor change management bij Euro Caps."""
            
            # Voeg content toe
            if next_heading_idx < len(doc.paragraphs):
                kublerross_content = doc.paragraphs[next_heading_idx].insert_paragraph_before(kublerross_uitwerking)
                kublerross_content.style = doc.styles['Normal']
            
            print("Uitgebreide 2.6 uitwerking toegevoegd")
            break
    
    # 3. Voeg ook uitwerkingen toe voor 2.2 en 2.3 als deze kort zijn
    print("\n3. Controleren en uitbreiden 2.2 en 2.3 uitwerkingen...")
    
    # Check 2.2 De Caluwé
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.2 Veranderkleuren van De Caluwé":
            # Zoek of er al uitgebreide content is
            next_content = ""
            j = i + 1
            while j < len(doc.paragraphs) and not doc.paragraphs[j].style.name.startswith('Heading'):
                next_content += doc.paragraphs[j].text
                j += 1
            
            if len(next_content) < 200:  # Als content te kort is
                caluwe_uitwerking = """De Caluwé en Vermaak (2009) hebben een kleurenmodel ontwikkeld dat verschillende paradigma's voor organisatieverandering onderscheidt. Het model helpt change agents bij het kiezen van de meest passende veranderaanpak door vijf verschillende 'kleuren' of denkwijzen te identificeren.

Blauwdrukdenken kenmerkt zich door een rationele, geplande benadering waarbij verandering wordt gezien als een logisch, controleerbaar proces. Geeldrukdenken richt zich op belangen en politieke processen, waarbij verandering wordt bereikt door onderhandeling en coalitievorming. Rooddrukdenken benadrukt macht en dwang als drijfveren voor verandering, waarbij leiderschap en directieve sturing centraal staan.

Groendrukdenken focust op leren en ontwikkeling, waarbij verandering ontstaat door collectief leren en experimenteren. Witdrukdenken tenslotte gaat uit van zelforganisatie en emergente processen, waarbij verandering spontaan ontstaat zonder directe sturing.

Voor Euro Caps biedt het kleurenmodel inzicht in verschillende benaderingen voor de Six Sigma implementatie, waarbij een combinatie van blauwdruk- en groendrukdenken het meest geschikt lijkt.

Figuur 2.3: De Caluwé's Veranderkleuren Model

[Hier wordt de caluwe_kleuren_model.png visual ingevoegd]"""
                
                if j < len(doc.paragraphs):
                    caluwe_content = doc.paragraphs[j].insert_paragraph_before(caluwe_uitwerking)
                    caluwe_content.style = doc.styles['Normal']
                print("2.2 De Caluwé uitwerking uitgebreid")
            break
    
    # Check 2.3 Gap-analyse
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.3 Gap-analyse & Hofstede-model":
            # Zoek of er al uitgebreide content is
            next_content = ""
            j = i + 1
            while j < len(doc.paragraphs) and not doc.paragraphs[j].style.name.startswith('Heading'):
                next_content += doc.paragraphs[j].text
                j += 1
            
            if len(next_content) < 200:  # Als content te kort is
                hofstede_uitwerking = """Gap-analyse in combinatie met Hofstede's cultuurdimensiemodel (2010) biedt een systematische methode voor het analyseren van de kloof tussen de huidige en gewenste organisatiecultuur. Hofstede identificeerde zes cultuurdimensies die organisaties karakteriseren en die cruciaal zijn voor het begrijpen van veranderingsprocessen.

De zes dimensies omvatten machtsafstand (acceptatie van hiërarchische verschillen), individualisme versus collectivisme (focus op individu of groep), onzekerheidsvermijding (tolerantie voor ambiguïteit), masculiniteit versus femininiteit (competitie versus samenwerking), lange versus korte termijn oriëntatie (toekomstgerichtheid) en toegeeflijkheid versus beheersing (controle over impulsen).

Voor Euro Caps toont de gap-analyse significante verschillen tussen de huidige cultuur en de gewenste cultuur voor Six Sigma ondersteuning. De huidige hoge machtsafstand en onzekerheidsvermijding belemmeren de participatieve benadering die Six Sigma vereist, terwijl de korte termijn oriëntatie conflicteert met de lange termijn verbeterdoelstellingen.

Figuur 2.4: Hofstede Gap-analyse Euro Caps

[Hier wordt de hofstede_gap_analysis.png visual ingevoegd]

De analyse toont concrete verbeterpunten voor cultuurverandering ter ondersteuning van Six Sigma implementatie."""
                
                if j < len(doc.paragraphs):
                    hofstede_content = doc.paragraphs[j].insert_paragraph_before(hofstede_uitwerking)
                    hofstede_content.style = doc.styles['Normal']
                print("2.3 Gap-analyse uitwerking uitgebreid")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOLLEDIG_UITGEWERKT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOLLEDIG_UITGEWERKT.docx")
    print("✅ Alle theoretische modellen volledig uitgewerkt")
    print("✅ Visuals klaar voor handmatige toevoeging:")
    print("   - caluwe_kleuren_model.png")
    print("   - hofstede_gap_analysis.png") 
    print("   - kotter_8_steps_model.png")
    print("   - stakeholder_matrix.png")
    print("   - kubler_ross_curve.png")

if __name__ == "__main__":
    add_missing_25_26_content()

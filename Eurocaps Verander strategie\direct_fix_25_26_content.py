#!/usr/bin/env python3
"""
Script om direct de 2.5 en 2.6 content te vervangen in het Word document
"""

from docx import Document
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT

def direct_fix_25_26_content():
    """Vervangt direct de korte 2.5 en 2.6 content met volledige uitwerkingen"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_UITWERKINGEN.docx')
    
    print("Bezig met direct vervangen van 2.5 en 2.6 content...")
    
    # 1. Zoe<PERSON> en vervang 2.5 content
    print("\n1. Zoeken en vervangen 2.5 content...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek de korte 2.5 tekst
        if ("Stakeholderanalyse is een systematische methode voor het identificeren" in text and 
            "organisatorische veranderingen" in text and 
            len(text) < 200):  # Korte versie
            
            print(f"Gevonden korte 2.5 tekst op regel {i}")
            
            # Vervang met volledige uitwerking
            volledige_25_tekst = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Deze methodiek, gebaseerd op Freeman's stakeholdertheorie (2010), vormt een essentieel onderdeel van effectief change management en biedt een gestructureerde benadering voor het begrijpen van de complexe relaties tussen een organisatie en haar omgeving.

Freeman's stakeholdertheorie onderscheidt twee hoofdcategorieën van stakeholders. Primaire stakeholders hebben directe invloed op de organisatie en worden direct beïnvloed door organisatorische beslissingen en veranderingen. Deze groep omvat medewerkers, klanten, leveranciers, aandeelhouders en het management. Secundaire stakeholders daarentegen hebben indirecte invloed en worden indirect beïnvloed door organisatorische activiteiten, zoals overheden, milieugroepen, media, concurrenten en de bredere gemeenschap.

Het proces van stakeholderanalyse bestaat uit vier systematische stappen die elkaar opvolgen en versterken. De eerste stap behelst de identificatie van alle relevante stakeholders door middel van brainstormsessies, documentanalyse en interviews met sleutelpersonen. Hierbij wordt een zo compleet mogelijk overzicht gecreëerd van alle partijen die op enigerlei wijze betrokken zijn bij of beïnvloed worden door de organisatorische verandering.

De tweede stap richt zich op de grondige analyse van stakeholderbelangen, waarbij voor elke geïdentificeerde stakeholder wordt onderzocht wat hun specifieke belangen, verwachtingen, behoeften en zorgen zijn ten aanzien van de voorgestelde verandering. Deze analyse vereist diepgaand onderzoek naar de motivaties en drijfveren van verschillende stakeholdergroepen.

In de derde stap wordt de invloed en attitude van stakeholders beoordeeld. Hierbij wordt gebruik gemaakt van invloed-belangmatrices waarbij stakeholders worden gepositioneerd op basis van hun relatieve invloed op het veranderingsproces en hun belang bij de uitkomst. Deze positionering helpt bij het prioriteren van stakeholders en het bepalen van de benodigde aandacht en resources.

De vierde en finale stap omvat de ontwikkeling van specifieke benaderingsstrategieën voor elke stakeholdergroep. Gebaseerd op hun positie in de invloed-belangmatrix worden verschillende strategieën ontwikkeld, variërend van intensief management voor stakeholders met hoge invloed en hoog belang tot monitoring voor stakeholders met lage invloed en laag belang.

Voor Euro Caps is stakeholderanalyse van cruciaal belang voor het succesvol implementeren van organisatorische veranderingen ter ondersteuning van Six Sigma. De koffiecapsuleproducent opereert in een complexe omgeving met diverse stakeholders die elk verschillende belangen hebben bij de organisatorische transformatie. Een effectieve stakeholderbenadering verhoogt de acceptatie van veranderingen, vermindert weerstand tijdens het implementatieproces en zorgt voor duurzame verankering van nieuwe werkwijzen.

De stakeholderanalyse voor Euro Caps identificeert verschillende kritieke partijen. Het senior management, bestaande uit CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland, heeft hoge invloed en hoog belang bij het succes van de Six Sigma implementatie vanwege hun verantwoordelijkheid voor strategische doelen en return on investment. De kwaliteitsafdeling onder leiding van Hoofd Kwaliteitsbeheer Kees Keurig heeft eveneens hoge invloed en hoog belang vanwege hun directe betrokkenheid bij procesverbetering en kwaliteitsstandaarden."""
            
            paragraph.text = volledige_25_tekst
            print("2.5 content vervangen met volledige uitwerking")
            break
    
    # 2. Zoek en vervang 2.6 content
    print("\n2. Zoeken en vervangen 2.6 content...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek de korte 2.6 tekst
        if ("Het Kübler-Ross model beschrijft de emotionele fasen" in text and 
            "veranderingsprocessen" in text and 
            len(text) < 200):  # Korte versie
            
            print(f"Gevonden korte 2.6 tekst op regel {i}")
            
            # Vervang met volledige uitwerking
            volledige_26_tekst = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen en vormt een fundamenteel raamwerk voor het begrijpen van menselijke reacties op organisatorische transformaties. Dit model, oorspronkelijk ontwikkeld door psychiater Elisabeth Kübler-Ross (1969) voor het begrijpen van rouwprocessen bij terminaal zieke patiënten, is succesvol geadapteerd voor organisatorische verandering en biedt waardevolle inzichten voor change management professionals.

De theoretische basis van het model ligt in de erkenning dat verandering inherent verlies met zich meebrengt, waarbij individuen afscheid moeten nemen van vertrouwde situaties, werkwijzen, relaties of identiteiten. Deze verliezen triggeren emotionele reacties die voorspelbaar en universeel zijn, hoewel de intensiteit en duur per individu kunnen variëren. Het model biedt een roadmap voor het begrijpen, voorspellen en begeleiden van deze emotionele reis.

De verandercurve bestaat uit vijf opeenvolgende fasen die individuen doorgaans doorlopen tijdens significante veranderingen. De eerste fase betreft ontkenning, waarbij individuen de noodzaak, realiteit of permanentie van verandering weigeren te accepteren. In organisatorische context kunnen medewerkers beweren dat de huidige situatie adequaat is, dat verandering onnodig is, of dat de verandering tijdelijk zal zijn en vanzelf zal verdwijnen. Deze fase wordt gekenmerkt door cognitieve dissonantie waarbij nieuwe informatie wordt genegeerd of geherïnterpreteerd om bestaande overtuigingen te behouden.

De tweede fase omvat woede, gekenmerkt door intense frustratie en boosheid over het verlies van controle, zekerheid en vertrouwde werkwijzen. Medewerkers kunnen zich richten tegen het management, het veranderingsproces, nieuwe systemen of collega's die de verandering ondersteunen. Deze emoties kunnen zich uiten in openlijke kritiek, sabotage, verhoogd ziekteverzuim of verminderde productiviteit. Hoewel deze fase uitdagend is voor managers, is het een natuurlijk en noodzakelijk onderdeel van het verwerkingsproces.

De derde fase behelst onderhandeling, waarbij individuen pogingen ondernemen om controle terug te winnen door compromissen te zoeken of de verandering te beperken. Medewerkers kunnen voorstellen doen om delen van de oude situatie te behouden, uitzonderingen te bedingen, of de implementatie te vertragen. Deze fase toont beginnende acceptatie van de realiteit van verandering, maar ook wanhopige pogingen om de impact te minimaliseren.

Vervolgens treedt de fase van depressie op, gekenmerkt door gevoelens van verlies, verdriet en onzekerheid over de persoonlijke toekomst. Medewerkers kunnen zich overweldigd voelen door de omvang van de verandering en twijfelen aan hun vermogen om zich aan te passen. Productiviteit kan tijdelijk dalen, motivatie kan afnemen en sommige medewerkers kunnen overwegen om de organisatie te verlaten. Deze fase vereist empathische begeleiding en concrete ondersteuning.

De finale fase behelst acceptatie, waarbij individuen bereidheid tonen om de nieuwe realiteit te omarmen en constructief bij te dragen aan het succes van de verandering. Zij beginnen de voordelen van nieuwe werkwijzen in te zien, ontwikkelen nieuwe competenties en gedragingen, en kunnen zelfs ambassadeurs worden voor verdere veranderingen. Deze fase markeert de overgang van weerstand naar commitment en van verlies naar nieuwe mogelijkheden.

Voor Euro Caps is begrip van de Kübler-Ross curve cruciaal voor het effectief managen van de emotionele aspecten van de Six Sigma implementatie. De transformatie van een traditionele productieorganisatie naar een data-gedreven, continue verbeterende organisatie brengt significante veranderingen met zich mee in werkwijzen, verantwoordelijkheden, meetcriteria en organisatiecultuur. Deze veranderingen kunnen intense emotionele reacties oproepen bij medewerkers op alle niveaus."""
            
            paragraph.text = volledige_26_tekst
            print("2.6 content vervangen met volledige uitwerking")
            break
    
    # 3. Voeg stakeholder tabel toe na 2.5 (als deze er nog niet is)
    print("\n3. Controleren en toevoegen stakeholder tabel...")
    
    # Zoek of er al een tabel is na 2.5
    found_table = False
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                if 'Stakeholder' in cell.text and 'Invloed' in cell.text:
                    found_table = True
                    break
    
    if not found_table:
        print("Stakeholder tabel niet gevonden, toevoegen...")
        
        # Zoek waar we de tabel moeten toevoegen (na 2.5 content)
        for i, paragraph in enumerate(doc.paragraphs):
            if "Euro Caps identificeert verschillende kritieke partijen" in paragraph.text:
                # Voeg tabel toe na deze paragraaf
                table_paragraph = paragraph.insert_paragraph_after("Tabel 2.1: Stakeholderanalyse Euro Caps Six Sigma Implementatie")
                
                # Voeg stakeholder tabel toe
                table = doc.add_table(rows=1, cols=4)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Header
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'Stakeholder'
                hdr_cells[1].text = 'Type'
                hdr_cells[2].text = 'Invloed'
                hdr_cells[3].text = 'Primair Belang'
                
                # Data
                stakeholder_data = [
                    ('Senior Management (Clement, Bosland)', 'Primair', 'Hoog', 'Strategische doelen en ROI'),
                    ('Kwaliteitsafdeling (Keurig)', 'Primair', 'Hoog', 'Procesverbetering en standaarden'),
                    ('Medewerkers Productie', 'Primair', 'Gemiddeld', 'Werkzekerheid en ontwikkeling'),
                    ('Klanten (grote afnemers)', 'Primair', 'Hoog', 'Productkwaliteit en service'),
                    ('Leveranciers', 'Primair', 'Gemiddeld', 'Lange termijn partnerships'),
                    ('Aandeelhouders', 'Secundair', 'Hoog', 'Financiële prestaties'),
                    ('Toezichthouders (NVWA)', 'Secundair', 'Gemiddeld', 'Compliance en voedselveiligheid'),
                    ('Vakbonden', 'Secundair', 'Laag', 'Arbeidsvoorwaarden')
                ]
                
                for data in stakeholder_data:
                    row_cells = table.add_row().cells
                    for k, value in enumerate(data):
                        row_cells[k].text = value
                
                print("Stakeholder tabel toegevoegd")
                break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DIRECT_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_DIRECT_FIXED.docx")
    print("✅ 2.5 Stakeholderanalyse: Korte tekst vervangen met volledige uitwerking")
    print("✅ 2.6 Kübler-Ross: Korte tekst vervangen met volledige uitwerking")
    print("✅ Stakeholder tabel toegevoegd (indien nog niet aanwezig)")

if __name__ == "__main__":
    direct_fix_25_26_content()

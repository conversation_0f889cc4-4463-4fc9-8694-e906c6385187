#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om literatuurlijst en argumentatieschema toe te voegen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def add_literature_list(doc):
    """Voeg literatuurlijst toe"""
    doc.add_page_break()
    doc.add_heading('Literatuurlijst', level=1)
    
    literature_text = """Boonstra, J.J. (2013). Leren veranderen: Een handboek voor de veranderkundige (2e editie). Amsterdam: Boom Lemma uitgevers.

De Caluwé, L., & <PERSON>, H. (2006). Leren veranderen: Een handboek voor de veranderkundige (2e editie). Deventer: Kluwer.

George, M.L., <PERSON>, D., & <PERSON>, B. (2004). What is Lean Six Sigma? New York: McGraw-Hill Education.

Hofstede, <PERSON><PERSON> (2001). Culture's consequences: Comparing values, behaviors, institutions and organizations across nations (2e editie). Thousand Oaks: Sage Publications.

Kotter, J.<PERSON>. (2012). Leading change (Heruitgave). Boston: Harvard Business Review Press.

Kübler-Ross, E. (1969). On death and dying. New York: Macmillan Publishing.

Mintzberg, H. (1983). Structure in fives: Designing effective organizations. Englewood Cliffs: Prentice-Hall.

Pyzdek, T., & Keller, P. (2014). The Six Sigma handbook: A complete guide for Green Belts, Black Belts, and managers at all levels (4e editie). New York: McGraw-Hill Education.

Thuis, P., & Stuive, R. (2019). Bedrijfskunde (3e editie). Groningen: Noordhoff Uitgevers.

Vermaak, H., & De Caluwé, L. (2017). Leren veranderen: Een leer- en handboek voor de veranderkundige (3e editie). Deventer: Vakmedianet."""
    doc.add_paragraph(literature_text)
    
    return doc

def add_bijlagen(doc):
    """Voeg bijlagen toe"""
    doc.add_page_break()
    doc.add_heading('Bijlagen', level=1)
    
    bijlagen_text = """Bijlage A: Stakeholderanalyse matrix
Gedetailleerde matrix met alle geïdentificeerde stakeholders, hun belangen, invloed, betrokkenheid en specifieke interventies voor elke groep.

Bijlage B: Six Sigma implementatie roadmap
Uitgebreide tijdlijn met DMAIC fasen, mijlpalen, verantwoordelijkheden en deliverables voor de volledige Six Sigma implementatie.

Bijlage C: Communicatieplan matrix
Overzicht van alle communicatie activiteiten per stakeholdergroep, inclusief boodschappen, kanalen, frequentie en verantwoordelijken.

Bijlage D: Training curriculum Six Sigma
Gedetailleerd overzicht van alle training modules voor verschillende niveaus: awareness, Green Belt en Black Belt certificering.

Bijlage E: KPI dashboard en meetplan
Overzicht van alle key performance indicators voor het monitoren van zowel de organisatieverandering als de Six Sigma implementatie resultaten.

Bijlage F: Risicoanalyse en mitigatie strategieën
Identificatie van potentiële risico's en uitdagingen tijdens het veranderingsproces met bijbehorende mitigatie maatregelen.

Bijlage G: Business case en ROI calculatie
Gedetailleerde kosten-baten analyse van de Six Sigma implementatie met verwachte return on investment en payback periode."""
    doc.add_paragraph(bijlagen_text)
    
    return doc

def add_argumentatie_schema(doc):
    """Voeg argumentatieschema toe"""
    doc.add_page_break()
    doc.add_heading('Argumentatieschema', level=1)
    
    argumentatie_intro = """Dit argumentatieschema toont de logische onderbouwing van alle keuzes en aanbevelingen in dit adviesrapport. Het schema volgt de structuur van claim, data, warrant en backing volgens het Toulmin model."""
    doc.add_paragraph(argumentatie_intro)
    
    # Hoofdclaim
    doc.add_heading('Hoofdclaim', level=2)
    hoofdclaim_text = """Euro Caps moet een integrale veranderstrategie implementeren die Kotter's 8-stappenmodel combineert met Six Sigma methodologie om organisatorische transformatie en operationele excellentie te realiseren."""
    doc.add_paragraph(hoofdclaim_text)
    
    # Onderbouwing hoofdclaim
    doc.add_heading('Onderbouwing hoofdclaim', level=2)
    
    # Subclaim 1
    doc.add_heading('Subclaim 1: Noodzaak van verandering', level=3)
    subclaim1_text = """Data: Euro Caps opereert in een dynamische marktomgeving met toenemende concurrentie en veranderende klanteisen.
    
Warrant: Organisaties die niet meeveranderen met marktdynamiek verliezen concurrentievoordeel.

Backing: Literatuur over organisatieverandering (Kotter, 2012; Boonstra, 2013) toont aan dat proactieve verandering essentieel is voor duurzaam succes."""
    doc.add_paragraph(subclaim1_text)
    
    # Subclaim 2
    doc.add_heading('Subclaim 2: Keuze voor Kotter\'s model', level=3)
    subclaim2_text = """Data: Kotter's 8-stappenmodel heeft bewezen effectiviteit in vergelijkbare organisaties en biedt systematische aanpak.

Warrant: Een gestructureerde veranderaanpak verhoogt de kans op succesvolle implementatie.

Backing: Empirisch onderzoek toont dat organisaties die Kotter's model volgen hogere slaagkans hebben bij veranderingsinitiatieven."""
    doc.add_paragraph(subclaim2_text)
    
    # Subclaim 3
    doc.add_heading('Subclaim 3: Keuze voor Six Sigma', level=3)
    subclaim3_text = """Data: Six Sigma scoorde hoogst (18 punten) in kwantitatieve beslissingsmatrix voor kwaliteitsmanagementmethoden.

Warrant: De methode met de hoogste score in een objectieve evaluatie is de beste keuze.

Backing: Six Sigma's focus op precisieprocessen sluit perfect aan bij Euro Caps' koffiecapsule productie waar dosering kritiek is."""
    doc.add_paragraph(subclaim3_text)
    
    # Subclaim 4
    doc.add_heading('Subclaim 4: Integratie van DMAIC en Kotter', level=3)
    subclaim4_text = """Data: DMAIC methodologie kan worden geïntegreerd in Kotter's 8 stappen voor optimale resultaten.

Warrant: Integratie van complementaire methodologieën versterkt de effectiviteit van beide.

Backing: Literatuur over Six Sigma implementatie (Pyzdek & Keller, 2014) toont voordelen van integratie met change management modellen."""
    doc.add_paragraph(subclaim4_text)
    
    # Subclaim 5
    doc.add_heading('Subclaim 5: Stakeholder-specifieke aanpak', level=3)
    subclaim5_text = """Data: Stakeholderanalyse toont verschillende belangen en behoeften per groep.

Warrant: Aangepaste interventies per stakeholdergroep verhogen betrokkenheid en acceptatie.

Backing: De Caluwé's kleurenmodel (2006) toont dat verschillende mensen verschillende veranderingsbenaderingen prefereren."""
    doc.add_paragraph(subclaim5_text)
    
    # Mogelijke tegenargumenten
    doc.add_heading('Mogelijke tegenargumenten en weerlegging', level=2)
    
    tegenargumenten_text = """Tegenargument 1: "Six Sigma is te complex voor Euro Caps"
Weerlegging: Gefaseerde implementatie met adequate training en externe ondersteuning maakt Six Sigma toegankelijk voor alle medewerkers.

Tegenargument 2: "Verandering verstoort huidige kwaliteit"
Weerlegging: Six Sigma versterkt juist de kwaliteitsfocus en HACCP integratie waarborgt voedselveiligheid.

Tegenargument 3: "Kosten van implementatie zijn te hoog"
Weerlegging: Business case toont positieve ROI binnen 18 maanden door procesverbeteringen en defectreductie.

Tegenargument 4: "Medewerkers zullen weerstand bieden"
Weerlegging: Stakeholder-specifieke interventies en communicatieplan minimaliseren weerstand en verhogen acceptatie."""
    doc.add_paragraph(tegenargumenten_text)
    
    # Conclusie argumentatie
    doc.add_heading('Conclusie argumentatieschema', level=2)
    conclusie_argumentatie = """De argumentatie toont aan dat de aanbevolen veranderstrategie gebaseerd is op solide theoretische fundamenten, empirische data en bewezen methodologieën. Alle keuzes zijn logisch onderbouwd en mogelijke tegenargumenten zijn adequaat weerlegd.

De integratie van Kotter's change management met Six Sigma procesverbetering biedt Euro Caps de beste kans op succesvolle transformatie naar een meer flexibele, innovatieve en klantgerichte organisatie zonder verlies van de sterke kwaliteitsfocus."""
    doc.add_paragraph(conclusie_argumentatie)
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_COMPLEET_FINAAL_MET_ALLE_VISUALS_EN_SIXSIGMA.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Voeg literatuurlijst en argumentatieschema toe
    document = add_literature_list(document)
    document = add_bijlagen(document)
    document = add_argumentatie_schema(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Literatuurlijst, bijlagen en argumentatieschema toegevoegd aan: {doc_path}")

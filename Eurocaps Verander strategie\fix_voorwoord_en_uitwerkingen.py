#!/usr/bin/env python3
"""
Script om voorwoord te verplaatsen en ontbrekende uitwerkingen toe te voegen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_voorwoord_en_uitwerkingen():
    """Verplaatst voorwoord en voegt ontbrekende uitwerkingen toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_COMPLEET_FINAL.docx')
    
    print("Bezig met corrigeren voorwoord plaatsing en toevoegen uitwerkingen...")
    
    # 1. Verplaats voorwoord naar juiste plek (na titelpagina, voor managementsamenvatting)
    print("\n1. Verplaatsen voorwoord naar juiste plek...")
    
    # Zoek voorwoord
    voorwoord_start = None
    voorwoord_content = ""
    
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Voorwoord' in paragraph.text and paragraph.style.name.startswith('Heading'):
            voorwoord_start = i
            # Verzamel voorwoord content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading 1'):
                    break
                voorwoord_content += doc.paragraphs[j].text + "\n\n"
                j += 1
            break
    
    # Verwijder voorwoord van verkeerde plek
    if voorwoord_start:
        j = voorwoord_start
        while j < len(doc.paragraphs):
            if j > voorwoord_start and doc.paragraphs[j].style.name.startswith('Heading 1'):
                break
            p = doc.paragraphs[j]._element
            p.getparent().remove(p)
        print("Voorwoord verwijderd van verkeerde plek")
    
    # Zoek managementsamenvatting en voeg voorwoord ervoor toe
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Managementsamenvatting' in paragraph.text and paragraph.style.name.startswith('Heading'):
            # Voeg voorwoord toe voor managementsamenvatting
            voorwoord_heading = paragraph.insert_paragraph_before('Voorwoord')
            voorwoord_heading.style = doc.styles['Heading 1']
            
            voorwoord_text = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. Het rapport richt zich op het ontwikkelen van een veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie.

Graag wil ik mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun uitstekende begeleiding, inspirerende lessen en waardevolle feedback gedurende dit project. Hun expertise op het gebied van verandermanagement en organisatieontwikkeling heeft mij geholpen om een diepgaand begrip te ontwikkelen van de complexiteit van organisatorische transformaties.

Daarnaast wil ik hen bedanken voor de beschikbaar gestelde literatuur, casestudies en praktische voorbeelden die de basis hebben gevormd voor dit onderzoek. Hun toewijding aan het onderwijs en hun bereidheid om studenten te ondersteunen bij het ontwikkelen van professionele vaardigheden wordt zeer gewaardeerd.

Dit rapport is het resultaat van intensieve studie en toepassing van de geleerde theorieën en methodieken, en ik hoop dat het een waardevolle bijdrage levert aan het begrip van verandermanagement in productieorganisaties."""
            
            voorwoord_content_p = voorwoord_heading.insert_paragraph_after(voorwoord_text)
            voorwoord_content_p.style = doc.styles['Normal']
            
            print("Voorwoord toegevoegd op juiste plek (na titelpagina, voor managementsamenvatting)")
            break
    
    # 2. Voeg uitwerking toe voor 2.4 Kotter's 8 Stappenmodel
    print("\n2. Toevoegen uitwerking 2.4 Kotter's 8 Stappenmodel...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.4 Kotter's 8 Stappenmodel" and paragraph.style.name.startswith('Heading'):
            # Zoek waar we content moeten toevoegen
            next_heading = i + 1
            while next_heading < len(doc.paragraphs):
                if doc.paragraphs[next_heading].style.name.startswith('Heading'):
                    break
                next_heading += 1
            
            kotter_uitwerking = """John Kotter's achtstappenmodel (2012) biedt een gestructureerde benadering voor het leiden van organisatorische verandering. Het model is gebaseerd op onderzoek naar succesvolle en gefaalde veranderingsinitiatieven en biedt een bewezen raamwerk voor complexe transformaties.

De acht stappen zijn sequentieel geordend maar kunnen overlappen in de praktijk:

1. Urgentiebesef creëren - Ontwikkelen van een gevoel van noodzaak voor verandering
2. Leidende coalitie vormen - Samenstellen van een team met voldoende macht om de verandering te leiden
3. Visie en strategie ontwikkelen - Creëren van een heldere visie om de verandering te sturen
4. Visie communiceren - Gebruik van alle mogelijke kanalen om de nieuwe visie en strategieën te communiceren
5. Medewerkers empoweren - Verwijderen van obstakels en empoweren van mensen om volgens de visie te handelen
6. Korte termijn successen genereren - Plannen en creëren van zichtbare prestatieverbetering
7. Verbeteringen consolideren - Gebruik van toegenomen geloofwaardigheid om systemen en structuren aan te passen
8. Nieuwe benaderingen verankeren - Versterken van veranderingen door nieuwe gedragingen te koppelen aan organisatiesucces

Dit model is bijzonder effectief voor organisaties zoals Euro Caps die fundamentele structurele en culturele veranderingen doorvoeren ter ondersteuning van kwaliteitsinitiatieven zoals Six Sigma."""
            
            if next_heading < len(doc.paragraphs):
                kotter_content = doc.paragraphs[next_heading].insert_paragraph_before(kotter_uitwerking)
            else:
                kotter_content = doc.add_paragraph(kotter_uitwerking)
            kotter_content.style = doc.styles['Normal']
            
            print("Uitwerking toegevoegd voor 2.4 Kotter's 8 Stappenmodel")
            break
    
    # 3. Voeg uitwerking en tabel toe voor 2.5 Stakeholderanalyse
    print("\n3. Toevoegen uitwerking en tabel voor 2.5 Stakeholderanalyse...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.5 Stakeholderanalyse" and paragraph.style.name.startswith('Heading'):
            # Zoek waar we content moeten toevoegen
            next_heading = i + 1
            while next_heading < len(doc.paragraphs):
                if doc.paragraphs[next_heading].style.name.startswith('Heading'):
                    break
                next_heading += 1
            
            stakeholder_uitwerking = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Freeman's stakeholdertheorie (2010) onderscheidt primaire stakeholders (directe invloed) en secundaire stakeholders (indirecte invloed).

De analyse omvat vier hoofdstappen:
1. Identificatie van alle relevante stakeholders
2. Analyse van hun belangen en verwachtingen
3. Beoordeling van hun invloed en attitude
4. Ontwikkeling van specifieke benaderingsstrategieën

Voor Euro Caps is stakeholderanalyse essentieel voor het succesvol implementeren van organisatorische veranderingen ter ondersteuning van Six Sigma. De volgende tabel geeft een overzicht van de belangrijkste stakeholders:"""
            
            if next_heading < len(doc.paragraphs):
                stakeholder_content = doc.paragraphs[next_heading].insert_paragraph_before(stakeholder_uitwerking)
            else:
                stakeholder_content = doc.add_paragraph(stakeholder_uitwerking)
            stakeholder_content.style = doc.styles['Normal']
            
            # Voeg stakeholder tabel toe
            table = doc.add_table(rows=1, cols=4)
            table.style = 'Table Grid'
            table.alignment = WD_TABLE_ALIGNMENT.CENTER
            
            # Header
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = 'Stakeholder'
            hdr_cells[1].text = 'Type'
            hdr_cells[2].text = 'Invloed'
            hdr_cells[3].text = 'Belang'
            
            # Data
            stakeholder_data = [
                ('Management', 'Primair', 'Hoog', 'Strategische doelen'),
                ('Medewerkers', 'Primair', 'Gemiddeld', 'Werkzekerheid en ontwikkeling'),
                ('Klanten', 'Primair', 'Hoog', 'Kwaliteit en service'),
                ('Leveranciers', 'Primair', 'Gemiddeld', 'Lange termijn partnerships'),
                ('Aandeelhouders', 'Secundair', 'Hoog', 'Financiële resultaten'),
                ('Toezichthouders', 'Secundair', 'Gemiddeld', 'Compliance en veiligheid')
            ]
            
            for data in stakeholder_data:
                row_cells = table.add_row().cells
                for i, value in enumerate(data):
                    row_cells[i].text = value
            
            print("Uitwerking en tabel toegevoegd voor 2.5 Stakeholderanalyse")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOORWOORD_UITWERKINGEN_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOORWOORD_UITWERKINGEN_FIXED.docx")
    print("✅ Voorwoord verplaatst naar juiste plek (na titelpagina)")
    print("✅ 2.4 Kotter's 8 Stappenmodel uitwerking toegevoegd")
    print("✅ 2.5 Stakeholderanalyse uitwerking en tabel toegevoegd")

if __name__ == "__main__":
    fix_voorwoord_en_uitwerkingen()

#!/usr/bin/env python3
"""
Script om alle ontbrekende content toe te voegen en voorwoord te verplaatsen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_all_missing_content():
    """Voegt alle ontbrekende content toe en verplaatst voorwoord"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_COMPLEET_FINAL.docx')
    
    print("<PERSON>zig met toevoegen van alle ontbrekende content...")
    
    # 1. Verplaats voorwoord naar juiste plek (na titelpagina, voor managementsamenvatting)
    print("\n1. Verplaatsen voorwoord...")
    
    # Zoek en verwijder voorwoord van verkeerde plek
    voorwoord_indices = []
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Voorwoord' in paragraph.text and paragraph.style.name.startswith('Heading'):
            # Markeer voorwoord en content voor verwijdering
            j = i
            while j < len(doc.paragraphs):
                if j > i and doc.paragraphs[j].style.name.startswith('Heading 1'):
                    break
                voorwoord_indices.append(j)
                j += 1
            break
    
    # Verwijder voorwoord van verkeerde plek
    for idx in reversed(voorwoord_indices):
        if idx < len(doc.paragraphs):
            p = doc.paragraphs[idx]._element
            p.getparent().remove(p)
    
    # Voeg voorwoord toe op juiste plek (voor managementsamenvatting)
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Managementsamenvatting' in paragraph.text and paragraph.style.name.startswith('Heading'):
            # Voeg voorwoord toe
            voorwoord_heading = paragraph.insert_paragraph_before('Voorwoord')
            voorwoord_heading.style = doc.styles['Heading 1']
            
            voorwoord_text = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. Het rapport richt zich op het ontwikkelen van een veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie.

Graag wil ik mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun uitstekende begeleiding, inspirerende lessen en waardevolle feedback gedurende dit project. Hun expertise op het gebied van verandermanagement en organisatieontwikkeling heeft mij geholpen om een diepgaand begrip te ontwikkelen van de complexiteit van organisatorische transformaties.

Daarnaast wil ik hen bedanken voor de beschikbaar gestelde literatuur, casestudies en praktische voorbeelden die de basis hebben gevormd voor dit onderzoek. Hun toewijding aan het onderwijs en hun bereidheid om studenten te ondersteunen bij het ontwikkelen van professionele vaardigheden wordt zeer gewaardeerd.

Dit rapport is het resultaat van intensieve studie en toepassing van de geleerde theorieën en methodieken, en ik hoop dat het een waardevolle bijdrage levert aan het begrip van verandermanagement in productieorganisaties."""
            
            voorwoord_content = paragraph.insert_paragraph_before(voorwoord_text)
            voorwoord_content.style = doc.styles['Normal']
            
            print("Voorwoord toegevoegd op juiste plek")
            break
    
    # 2. Voeg uitwerking toe voor 2.4 Kotter's 8 Stappenmodel
    print("\n2. Toevoegen uitwerking 2.4 Kotter's 8 Stappenmodel...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.4 Kotter's 8 Stappenmodel":
            # Zoek volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            kotter_uitwerking = """John Kotter's achtstappenmodel (2012) biedt een gestructureerde benadering voor het leiden van organisatorische verandering. Het model is gebaseerd op onderzoek naar succesvolle en gefaalde veranderingsinitiatieven en biedt een bewezen raamwerk voor complexe transformaties.

De acht stappen zijn sequentieel geordend maar kunnen overlappen in de praktijk:

1. Urgentiebesef creëren - Ontwikkelen van een gevoel van noodzaak voor verandering door concrete data en externe bedreigingen te presenteren
2. Leidende coalitie vormen - Samenstellen van een team met voldoende macht, expertise en geloofwaardigheid om de verandering te leiden
3. Visie en strategie ontwikkelen - Creëren van een heldere, inspirerende visie die gemakkelijk te communiceren is
4. Visie communiceren - Gebruik van alle mogelijke kanalen om de nieuwe visie en strategieën breed te communiceren
5. Medewerkers empoweren - Verwijderen van obstakels en empoweren van mensen om volgens de visie te handelen
6. Korte termijn successen genereren - Plannen en creëren van zichtbare prestatieverbetering binnen 6-18 maanden
7. Verbeteringen consolideren - Gebruik van toegenomen geloofwaardigheid om systemen, structuren en beleid aan te passen
8. Nieuwe benaderingen verankeren - Versterken van veranderingen door nieuwe gedragingen te koppelen aan organisatiesucces

Dit model is bijzonder effectief voor organisaties zoals Euro Caps die fundamentele structurele en culturele veranderingen doorvoeren ter ondersteuning van kwaliteitsinitiatieven zoals Six Sigma."""
            
            if next_heading_idx < len(doc.paragraphs):
                kotter_content = doc.paragraphs[next_heading_idx].insert_paragraph_before(kotter_uitwerking)
                kotter_content.style = doc.styles['Normal']
            
            print("Uitwerking toegevoegd voor 2.4 Kotter's 8 Stappenmodel")
            break
    
    # 3. Voeg uitwerking en tabel toe voor 2.5 Stakeholderanalyse
    print("\n3. Toevoegen uitwerking en tabel voor 2.5 Stakeholderanalyse...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.5 Stakeholderanalyse":
            # Zoek volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            stakeholder_uitwerking = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Freeman's stakeholdertheorie (2010) onderscheidt primaire stakeholders (directe invloed op organisatie) en secundaire stakeholders (indirecte invloed).

De analyse omvat vier hoofdstappen:
1. Identificatie van alle relevante stakeholders
2. Analyse van hun belangen en verwachtingen  
3. Beoordeling van hun invloed en attitude ten opzichte van de verandering
4. Ontwikkeling van specifieke benaderingsstrategieën per stakeholdergroep

Voor Euro Caps is stakeholderanalyse essentieel voor het succesvol implementeren van organisatorische veranderingen ter ondersteuning van Six Sigma. Een effectieve stakeholderbenadering verhoogt de acceptatie en vermindert weerstand tijdens het veranderingsproces.

Tabel 2.1: Stakeholderanalyse Euro Caps"""
            
            if next_heading_idx < len(doc.paragraphs):
                stakeholder_content = doc.paragraphs[next_heading_idx].insert_paragraph_before(stakeholder_uitwerking)
                stakeholder_content.style = doc.styles['Normal']
                
                # Voeg stakeholder tabel toe
                table = doc.add_table(rows=1, cols=4)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Header
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'Stakeholder'
                hdr_cells[1].text = 'Type'
                hdr_cells[2].text = 'Invloed'
                hdr_cells[3].text = 'Primair Belang'
                
                # Data
                stakeholder_data = [
                    ('Senior Management', 'Primair', 'Hoog', 'Strategische doelen en ROI'),
                    ('Medewerkers Productie', 'Primair', 'Gemiddeld', 'Werkzekerheid en training'),
                    ('Kwaliteitsafdeling', 'Primair', 'Hoog', 'Procesverbetering en standaarden'),
                    ('Klanten', 'Primair', 'Hoog', 'Productkwaliteit en service'),
                    ('Leveranciers', 'Primair', 'Gemiddeld', 'Lange termijn partnerships'),
                    ('Aandeelhouders', 'Secundair', 'Hoog', 'Financiële prestaties'),
                    ('Toezichthouders', 'Secundair', 'Gemiddeld', 'Compliance en veiligheid'),
                    ('Vakbonden', 'Secundair', 'Laag', 'Arbeidsvoorwaarden')
                ]
                
                for data in stakeholder_data:
                    row_cells = table.add_row().cells
                    for j, value in enumerate(data):
                        row_cells[j].text = value
            
            print("Uitwerking en tabel toegevoegd voor 2.5 Stakeholderanalyse")
            break
    
    # 4. Voeg uitwerking toe voor 2.6 Verandercurve van Kübler-Ross
    print("\n4. Toevoegen uitwerking 2.6 Verandercurve van Kübler-Ross...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.6 Verandercurve van Kübler-Ross":
            # Zoek volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            kublerross_uitwerking = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen. Oorspronkelijk ontwikkeld voor rouwprocessen, is het model geadapteerd voor organisatorische verandering om weerstand te begrijpen en te begeleiden.

De vijf fasen van de verandercurve zijn:

1. Ontkenning - Weigering om de noodzaak of realiteit van verandering te accepteren. Medewerkers kunnen beweren dat de huidige situatie adequaat is.

2. Woede - Frustratie en boosheid over het verlies van controle en vertrouwde werkwijzen. Deze fase kan zich uiten in kritiek op het management of het veranderingsproces.

3. Onderhandeling - Pogingen om compromissen te vinden of de verandering te beperken. Medewerkers zoeken naar manieren om delen van de oude situatie te behouden.

4. Depressie - Gevoel van verlies en onzekerheid over de persoonlijke toekomst. Productiviteit kan tijdelijk dalen en motivatie kan afnemen.

5. Acceptatie - Bereidheid om de nieuwe situatie te omarmen en constructief bij te dragen aan het succes van de verandering.

Voor Euro Caps is begrip van deze fasen cruciaal voor het ontwikkelen van gerichte interventies tijdens de Six Sigma implementatie. Door de emotionele reis van medewerkers te herkennen, kan het management passende ondersteuning bieden en weerstand effectief adresseren."""
            
            if next_heading_idx < len(doc.paragraphs):
                kublerross_content = doc.paragraphs[next_heading_idx].insert_paragraph_before(kublerross_uitwerking)
                kublerross_content.style = doc.styles['Normal']
            
            print("Uitwerking toegevoegd voor 2.6 Verandercurve van Kübler-Ross")
            break
    
    # 5. Herstel Bijlage sectie (als deze weg is)
    print("\n5. Controleren en herstellen Bijlage sectie...")
    
    # Zoek of er een Bijlage sectie is
    bijlage_found = False
    for paragraph in doc.paragraphs:
        if 'Bijlage' in paragraph.text and paragraph.style.name.startswith('Heading'):
            bijlage_found = True
            break
    
    if not bijlage_found:
        # Voeg Bijlage sectie toe aan het einde
        bijlage_heading = doc.add_paragraph('Bijlage A: Argumentatieschema')
        bijlage_heading.style = doc.styles['Heading 1']
        
        argumentatie_content = """Het volgende argumentatieschema onderbouwt de gekozen veranderstrategie voor Euro Caps op basis van dit onderzoek.

Standpunt:
Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.

Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit
De huidige machinebureaucratie met sterke hiërarchie beperkt cross-functionele samenwerking die essentieel is voor Six Sigma projecten. Gecentraliseerde besluitvorming vertraagt procesverbeteringen en beperkt medewerkerparticipatie in kwaliteitsinitiatieven.

Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering  
De analyse toont hoge onzekerheidsvermijding (score 80/100) die experimenteren en innovatie belemmert. Hoge machtsafstand (score 75/100) beperkt medewerkerparticipatie in verbeterprocessen.

Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces
Boonstra's ontwikkelingsstrategie faciliteert participatieve verandering die aansluit bij Six Sigma principes. Kotter's achtstappenmodel biedt bewezen raamwerk voor complexe organisatorische transformaties.

Tegenargument: Gefaseerde aanpak is voldoende
Geleidelijke aanpassing van alleen organisatiestructuur of cultuur vereist minder resources en creëert minder weerstand bij implementatie.

Weerlegging: Geïsoleerde veranderingen zijn onvoldoende voor duurzame transformatie
Structuurverandering zonder cultuurverandering leidt tot weerstand en terugval naar oude patronen. Six Sigma vereist fundamentele transformatie die alleen door integrale aanpak wordt gerealiseerd."""
        
        bijlage_content = doc.add_paragraph(argumentatie_content)
        bijlage_content.style = doc.styles['Normal']
        
        print("Bijlage A: Argumentatieschema toegevoegd")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_CONTENT_COMPLEET.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_CONTENT_COMPLEET.docx")
    print("✅ Voorwoord verplaatst naar juiste plek (na titelpagina)")
    print("✅ 2.4 Kotter's 8 Stappenmodel uitwerking toegevoegd")
    print("✅ 2.5 Stakeholderanalyse uitwerking en tabel toegevoegd")
    print("✅ 2.6 Verandercurve van Kübler-Ross uitwerking toegevoegd")
    print("✅ Bijlage A: Argumentatieschema hersteld/toegevoegd")
    print("✅ Document is nu volledig compleet!")

if __name__ == "__main__":
    fix_all_missing_content()

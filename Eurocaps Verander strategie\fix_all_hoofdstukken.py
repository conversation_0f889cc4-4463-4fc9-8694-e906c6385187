#!/usr/bin/env python3
"""
Script om ALLE hoofdstukken correct aan te passen volgens template
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def fix_all_hoofdstukken():
    """Past ALLE hoofdstukken aan volgens de exacte template"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEFINITIEF_CORRECT.docx')
    
    print("Bezig met aanpassen van alle hoofdstukken...")
    
    # Doorloop ALLE paragrafen en pas hoofdstukken aan
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.style.name.startswith('Heading'):
            original_text = paragraph.text.strip()
            print(f"Gevonden heading: '{original_text}' (Style: {paragraph.style.name})")
            
            # Heading 1 - Hoofdstukken
            if paragraph.style.name == 'Heading 1':
                if 'inleiding' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 1: Inleiding'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 1: Inleiding'")
                elif 'theoretisch' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 2: Theoretisch kader'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 2: Theoretisch kader'")
                elif 'huidige situatie' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 3: Huidige situatie'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 3: Huidige situatie'")
                elif 'gewenste situatie' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 4: Gewenste situatie'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 4: Gewenste situatie'")
                elif 'veranderstrategie' in original_text.lower() or 'implementatieplan' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 5: Veranderstrategie + implementatieplan'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 5: Veranderstrategie + implementatieplan'")
                elif 'communicatieplan' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 6: Communicatieplan'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 6: Communicatieplan'")
                elif 'conclusie' in original_text.lower():
                    paragraph.text = 'Hoofdstuk 7: Conclusie'
                    print(f"  -> Aangepast naar: 'Hoofdstuk 7: Conclusie'")
            
            # Heading 2 - Subhoofdstukken
            elif paragraph.style.name == 'Heading 2':
                if 'deskresearch' in original_text.lower():
                    paragraph.text = '1.1 Deskresearch methode'
                elif 'leeswijzer' in original_text.lower():
                    paragraph.text = '1.2 Leeswijzer'
                elif 'veranderstrategieën' in original_text.lower() and 'boonstra' in original_text.lower():
                    paragraph.text = '2.1 Veranderstrategieën volgens Boonstra'
                elif 'veranderkleuren' in original_text.lower() and 'caluwé' in original_text.lower():
                    paragraph.text = '2.2 Veranderkleuren van De Caluwé'
                elif 'gap-analyse' in original_text.lower() or 'hofstede' in original_text.lower():
                    paragraph.text = '2.3 Gap-analyse & Hofstede-model'
                elif 'kotter' in original_text.lower() and 'stappenmodel' in original_text.lower():
                    paragraph.text = '2.4 Kotter\'s 8 Stappenmodel'
                elif 'stakeholderanalyse' in original_text.lower():
                    paragraph.text = '2.5 Stakeholderanalyse'
                elif 'kübler-ross' in original_text.lower() or 'verandercurve' in original_text.lower():
                    paragraph.text = '2.6 Verandercurve van Kübler-Ross'
                elif 'huidige organisatiestructuur' in original_text.lower():
                    paragraph.text = '3.1 Huidige organisatiestructuur'
                elif 'huidige organisatiecultuur' in original_text.lower():
                    paragraph.text = '3.2 Huidige organisatiecultuur'
                elif '3.3' in original_text or ('deelconclusie' in original_text.lower() and '3' in original_text):
                    paragraph.text = '3.3 Deelconclusie beantwoorden'
                elif 'gewenste organisatiestructuur' in original_text.lower():
                    paragraph.text = '4.1 Gewenste organisatiestructuur'
                elif 'gewenste organisatiecultuur' in original_text.lower():
                    paragraph.text = '4.2 Gewenste organisatiecultuur'
                elif '4.3' in original_text or ('deelconclusie' in original_text.lower() and '4' in original_text):
                    paragraph.text = '4.3 Deelconclusie beantwoorden'
                elif 'voorbereidende deel' in original_text.lower():
                    paragraph.text = '5.1 Voorbereidende deel'
                elif 'uitvoerende deel' in original_text.lower():
                    paragraph.text = '5.2 Uitvoerende deel'
                elif '5.3' in original_text or ('deelconclusie' in original_text.lower() and '5' in original_text):
                    paragraph.text = '5.3 Deelconclusie beantwoorden'
                elif 'overzicht communicatieplan' in original_text.lower():
                    paragraph.text = '6.1 Overzicht communicatieplan'
            
            # Heading 3 - Sub-subhoofdstukken
            elif paragraph.style.name == 'Heading 3':
                if 'organisatiestructuur veranderingen' in original_text.lower():
                    paragraph.text = '5.1.1 Organisatiestructuur veranderingen'
                elif 'organisatiecultuur veranderingen' in original_text.lower():
                    paragraph.text = '5.1.2 Organisatiecultuur veranderingen'
                elif 'stakeholdersanalyse' in original_text.lower():
                    paragraph.text = '5.1.3 Stakeholdersanalyse'
                elif 'weerstanden' in original_text.lower() and 'kübler-ross' in original_text.lower():
                    paragraph.text = '5.1.4 Mogelijke weerstanden van Kübler-Ross'
                elif 'strategische veranderaanpak' in original_text.lower():
                    paragraph.text = '5.2.1 Strategische veranderaanpak'
                elif 'veranderstrategie boonstra' in original_text.lower():
                    paragraph.text = '5.2.2 Veranderstrategie Boonstra'
                elif 'veranderaanpak kotter' in original_text.lower():
                    paragraph.text = '5.2.3 Veranderaanpak Kotter'
                elif 'interventies' in original_text.lower() and 'stakeholder' in original_text.lower():
                    paragraph.text = '5.2.4 Interventies van de stakeholder'
    
    # Verwijder dubbel argumentatieschema
    print("\nVerwijderen van dubbel argumentatieschema...")
    argumentatie_count = 0
    paragraphs_to_remove = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Argumentatieschema' in paragraph.text and paragraph.style.name.startswith('Heading'):
            argumentatie_count += 1
            print(f"Argumentatieschema #{argumentatie_count} gevonden op regel {i}")
            if argumentatie_count > 1:
                print(f"  -> Markeren voor verwijdering")
                # Markeer deze en volgende paragrafen tot volgende hoofdstuk voor verwijdering
                j = i
                while j < len(doc.paragraphs):
                    if (j > i and doc.paragraphs[j].style.name.startswith('Heading 1') and 
                        'Bijlage' in doc.paragraphs[j].text):
                        break
                    paragraphs_to_remove.append(j)
                    j += 1
                break
    
    # Verwijder van achteren naar voren
    for i in reversed(paragraphs_to_remove):
        if i < len(doc.paragraphs):
            print(f"Verwijderen paragraaf {i}: '{doc.paragraphs[i].text[:50]}...'")
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HOOFDSTUKKEN_CORRECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HOOFDSTUKKEN_CORRECT.docx")
    print("✅ Alle hoofdstukken aangepast volgens template")
    print("✅ Dubbel argumentatieschema verwijderd")

if __name__ == "__main__":
    fix_all_hoofdstukken()

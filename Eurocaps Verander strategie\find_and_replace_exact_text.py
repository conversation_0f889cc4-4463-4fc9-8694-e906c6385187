#!/usr/bin/env python3
"""
Script om exact de korte teksten te vinden en vervangen
"""

from docx import Document

def find_and_replace_exact_text():
    """Vindt en vervangt exact de korte 2.5 en 2.6 teksten"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DIRECT_FIXED.docx')
    
    print("Bezig met zoeken naar exacte teksten...")
    
    # Print alle paragrafen om te zien wat er staat
    print("\nAlle paragrafen in het document:")
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text:  # Alleen niet-lege paragrafen
            print(f"{i}: {text[:100]}...")
            
            # Zoek specifiek naar 2.5 en 2.6 content
            if "2.5 Stakeholderanalyse" in text:
                print(f"   *** GEVONDEN 2.5 op regel {i}")
            elif "2.6 Verandercurve" in text:
                print(f"   *** GEVONDEN 2.6 op regel {i}")
            elif "Stakeholderanalyse is een systematische methode" in text:
                print(f"   *** GEVONDEN 2.5 CONTENT op regel {i}")
                print(f"       Volledige tekst: {text}")
            elif "Het Kübler-Ross model beschrijft" in text:
                print(f"   *** GEVONDEN 2.6 CONTENT op regel {i}")
                print(f"       Volledige tekst: {text}")

if __name__ == "__main__":
    find_and_replace_exact_text()

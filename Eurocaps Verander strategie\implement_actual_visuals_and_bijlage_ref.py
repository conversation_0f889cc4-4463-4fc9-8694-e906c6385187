#!/usr/bin/env python3
"""
Script om daadwerkelijk PNG bestanden in te voegen en bijlage verwijzing toe te voegen
"""

from docx import Document
from docx.shared import Inches
import os

def implement_actual_visuals_and_bijlage_ref():
    """Voegt daadwerkelijk PNG bestanden in en bijlage verwijzing"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_VISUALS_COMPLEET.docx')
    
    print("Bezig met invoegen van daadwerkelijke PNG bestanden...")
    
    # 1. Vervang tekst placeholders met daadwerkelijke afbeeldingen
    print("\n1. Vervangen tekst placeholders met PNG bestanden...")
    
    # Mapping van tekst naar PNG bestanden
    visual_mappings = {
        "[Hier wordt de caluwe_kleuren_model.png visual ingevoegd]": "caluwe_kleuren_model.png",
        "[Hier wordt de hofstede_gap_analysis.png visual ingevoegd]": "hofstede_gap_analysis.png", 
        "[Hier wordt de kotter_8_steps_model.png visual ingevoegd]": "kotter_8_steps_model.png",
        "[Hier wordt de stakeholder_matrix.png visual ingevoegd]": "stakeholder_matrix.png",
        "[Hier wordt de kubler_ross_curve.png visual ingevoegd]": "kubler_ross_curve.png"
    }
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check of deze paragraaf een visual placeholder bevat
        for placeholder, png_file in visual_mappings.items():
            if placeholder in text:
                print(f"Gevonden placeholder op regel {i}: {png_file}")
                
                # Check of PNG bestand bestaat
                if os.path.exists(png_file):
                    # Verwijder de placeholder tekst
                    paragraph.text = text.replace(placeholder, "")
                    
                    # Voeg de afbeelding toe
                    run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                    try:
                        run.add_picture(png_file, width=Inches(6))
                        print(f"✅ {png_file} ingevoegd")
                    except Exception as e:
                        print(f"❌ Fout bij invoegen {png_file}: {e}")
                        # Voeg fallback tekst toe
                        paragraph.text = f"[Afbeelding: {png_file}]"
                else:
                    print(f"❌ {png_file} niet gevonden")
                    # Vervang met fallback tekst
                    paragraph.text = text.replace(placeholder, f"[Afbeelding: {png_file} - bestand niet gevonden]")
    
    # 2. Voeg verwijzing naar Bijlage A toe in het rapport
    print("\n2. Toevoegen verwijzing naar Bijlage A...")
    
    # Zoek een geschikte plek om de verwijzing toe te voegen (bijvoorbeeld in conclusie of aanbevelingen)
    verwijzing_toegevoegd = False
    
    # Zoek naar conclusie sectie
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if (paragraph.style.name.startswith('Heading') and 
            ('Conclusie' in text or 'Aanbeveling' in text or 'Hoofdstuk 7' in text)):
            
            print(f"Gevonden geschikte sectie voor bijlage verwijzing: {text}")
            
            # Zoek einde van deze sectie en voeg verwijzing toe
            j = i + 1
            content_added = False
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                
                # Als we bij de volgende hoofdsectie zijn, voeg verwijzing toe
                if (doc.paragraphs[j].style.name.startswith('Heading 1') and 
                    ('Literatuurlijst' in next_text or 'Bijlage' in next_text)):
                    
                    bijlage_verwijzing = """
Voor een gedetailleerde onderbouwing van de gekozen veranderstrategie wordt verwezen naar het argumentatieschema in Bijlage A. Dit schema presenteert de hoofdargumenten voor de integrale aanpak, inclusief tegenargumenten en weerleggingen, gebaseerd op de bevindingen uit dit onderzoek."""
                    
                    verwijzing_p = doc.paragraphs[j].insert_paragraph_before(bijlage_verwijzing)
                    verwijzing_p.style = doc.styles['Normal']
                    
                    print("✅ Verwijzing naar Bijlage A toegevoegd")
                    verwijzing_toegevoegd = True
                    content_added = True
                    break
                j += 1
            
            if content_added:
                break
    
    # Als geen geschikte plek gevonden, voeg toe voor literatuurlijst
    if not verwijzing_toegevoegd:
        print("Geen geschikte sectie gevonden, toevoegen voor literatuurlijst...")
        
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip() == "Literatuurlijst" and paragraph.style.name.startswith('Heading'):
                
                bijlage_verwijzing = """
Voor een gedetailleerde onderbouwing van de gekozen veranderstrategie wordt verwezen naar het argumentatieschema in Bijlage A. Dit schema presenteert de hoofdargumenten voor de integrale aanpak, inclusief tegenargumenten en weerleggingen, gebaseerd op de bevindingen uit dit onderzoek.
"""
                
                verwijzing_p = paragraph.insert_paragraph_before(bijlage_verwijzing)
                verwijzing_p.style = doc.styles['Normal']
                
                print("✅ Verwijzing naar Bijlage A toegevoegd voor literatuurlijst")
                verwijzing_toegevoegd = True
                break
    
    # 3. Controleer of Bijlage A correct is ingesteld
    print("\n3. Controleren Bijlage A...")
    
    bijlage_found = False
    for paragraph in doc.paragraphs:
        if 'Bijlage A' in paragraph.text and paragraph.style.name.startswith('Heading'):
            bijlage_found = True
            print(f"✅ Bijlage A gevonden: {paragraph.text.strip()}")
            break
    
    if not bijlage_found:
        print("❌ Bijlage A niet gevonden")
        
        # Voeg Bijlage A toe aan het einde
        bijlage_heading = doc.add_paragraph('Bijlage A: Argumentatieschema')
        bijlage_heading.style = doc.styles['Heading 1']
        
        argumentatie_content = """Het argumentatieschema ter onderbouwing van de gekozen veranderstrategie voor Euro Caps is hieronder weergegeven.

[HIER HANDMATIG HET EURO CAPS ARGUMENTATIESCHEMA INVOEGEN MET DE EERDER GEGEVEN TEKST]"""
        
        bijlage_content = doc.add_paragraph(argumentatie_content)
        bijlage_content.style = doc.styles['Normal']
        
        print("✅ Bijlage A toegevoegd")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_VISUALS.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_VISUALS.docx")
    print("✅ PNG bestanden ingevoegd (waar beschikbaar)")
    print("✅ Verwijzing naar Bijlage A toegevoegd in rapport")
    print("✅ Bijlage A gecontroleerd/toegevoegd")
    
    print("\n📝 Handmatige acties nog nodig:")
    print("1. Argumentatieschema in Bijlage A vervangen met Euro Caps specifieke tekst")
    print("2. Controleren of alle PNG bestanden correct zijn ingevoegd")

if __name__ == "__main__":
    implement_actual_visuals_and_bijlage_ref()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om de juiste <PERSON> 8-stappen visual te maken zoals jij wilt
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_kotter_8_steps_visual():
    """Maak de <PERSON> 8-stappen visual in de juiste stijl"""
    
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    
    # Achtergrond wit maken
    fig.patch.set_facecolor('white')
    ax.set_facecolor('white')
    
    # Titel
    ax.text(0.5, 0.95, '8 veranderstappen van <PERSON>', 
            fontsize=20, fontweight='bold', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='#E91E63', edgecolor='black', linewidth=2),
            color='white')
    
    # Stappen data
    steps = [
        "1. Urgentiebesef vestigen",
        "2. Een leidende coalitie vormen", 
        "3. Een visie en strategie ontwikkelen",
        "4. De verandervisie communiceren",
        "5. Een breed draagvlak voor verandering creëren",
        "6. Korte termijn successen genereren",
        "7. Verbeteringen consolideren en vergroten",
        "8. De nieuwe aanpak verankeren"
    ]
    
    # Kleuren voor de stappen (oranje gradient)
    colors = ['#FF5722', '#FF6F00', '#FF8F00', '#FFA000', '#FFB300', '#FFC107', '#FFD54F', '#FFEB3B']
    
    # Posities voor de stappen (rechthoekige stapels)
    y_positions = [0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    
    # Teken de stappen als rechthoekige blokken
    for i, (step, color, y_pos) in enumerate(zip(steps, colors, y_positions)):
        # Rechthoek voor elke stap
        rect = FancyBboxPatch((0.1, y_pos-0.04), 0.8, 0.06,
                             boxstyle="round,pad=0.01",
                             facecolor=color,
                             edgecolor='black',
                             linewidth=1.5)
        ax.add_patch(rect)
        
        # Tekst voor elke stap
        ax.text(0.5, y_pos-0.01, step, 
                fontsize=12, fontweight='bold', ha='center', va='center',
                color='black')
    
    # Pijlen tussen fasen toevoegen
    arrow_props = dict(arrowstyle='->', lw=3, color='#9C27B0')
    
    # Tekst voor de fasen aan de linkerkant
    ax.text(0.02, 0.75, 'Een klimaat voor\nverandering\nscheppen', 
            fontsize=11, fontweight='bold', ha='left', va='center',
            color='#1976D2', rotation=90)
    
    ax.text(0.02, 0.45, 'De hele organisatie\nbij de verandering\nbetrekken en ervoor\ntoerusten', 
            fontsize=11, fontweight='bold', ha='left', va='center',
            color='#1976D2', rotation=90)
    
    ax.text(0.02, 0.15, 'De verandering\nimplementeren\nen volhouden', 
            fontsize=11, fontweight='bold', ha='left', va='center',
            color='#1976D2', rotation=90)
    
    # Gebogen pijlen aan de rechterkant
    # Pijl 1: van stap 3 naar implementatie
    ax.annotate('', xy=(0.95, 0.45), xytext=(0.95, 0.6),
                arrowprops=dict(arrowstyle='->', lw=3, color='#9C27B0',
                               connectionstyle="arc3,rad=0.3"))
    
    # Pijl 2: van stap 6 naar verankering  
    ax.annotate('', xy=(0.95, 0.15), xytext=(0.95, 0.3),
                arrowprops=dict(arrowstyle='->', lw=3, color='#9C27B0',
                               connectionstyle="arc3,rad=0.3"))
    
    # Verwijder assen
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # Opslaan
    plt.tight_layout()
    plt.savefig('Visual_Kotter_8_Stappen_Correct_Style.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("Kotter 8-stappen visual gemaakt: Visual_Kotter_8_Stappen_Correct_Style.png")

def create_caluwe_color_model_visual():
    """Maak De Caluwé kleurenmodel visual"""
    
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # Achtergrond wit maken
    fig.patch.set_facecolor('white')
    ax.set_facecolor('white')
    
    # Titel
    ax.text(0.5, 0.95, 'De Caluwé\'s Kleurenmodel voor Verandering', 
            fontsize=18, fontweight='bold', ha='center', va='center')
    
    # Kleuren en posities voor de cirkels
    colors_data = [
        ('Rooddruk\n(Relationeel)', '#FF5252', (0.2, 0.7), 'Relaties & Emoties\nMotivatie'),
        ('Geeldruk\n(Politiek)', '#FFEB3B', (0.5, 0.8), 'Belangen\nMacht'),
        ('Blauwdruk\n(Rationeel)', '#2196F3', (0.8, 0.7), 'Feiten & Cijfers\nPlanmatig'),
        ('Groendruk\n(Lerend)', '#4CAF50', (0.2, 0.3), 'Leren & Ontwikkeling\nExperiment'),
        ('Witdruk\n(Emergent)', '#FFFFFF', (0.5, 0.2), 'Complexiteit\nOnvoorspelbaarheid')
    ]
    
    # Teken de cirkels
    for label, color, (x, y), description in colors_data:
        # Cirkel
        if color == '#FFFFFF':  # Wit krijgt zwarte rand
            circle = plt.Circle((x, y), 0.12, facecolor=color, edgecolor='black', linewidth=2)
        else:
            circle = plt.Circle((x, y), 0.12, facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(circle)
        
        # Label in cirkel
        text_color = 'black' if color in ['#FFEB3B', '#FFFFFF'] else 'white'
        ax.text(x, y+0.02, label, fontsize=11, fontweight='bold', 
                ha='center', va='center', color=text_color)
        
        # Beschrijving onder cirkel
        ax.text(x, y-0.08, description, fontsize=9, 
                ha='center', va='center', color='black')
    
    # Verwijder assen
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # Opslaan
    plt.tight_layout()
    plt.savefig('Visual_Caluwe_Kleurenmodel_Correct.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()
    
    print("De Caluwé kleurenmodel visual gemaakt: Visual_Caluwe_Kleurenmodel_Correct.png")

if __name__ == "__main__":
    create_kotter_8_steps_visual()
    create_caluwe_color_model_visual()
    print("Alle visuals zijn gegenereerd!")

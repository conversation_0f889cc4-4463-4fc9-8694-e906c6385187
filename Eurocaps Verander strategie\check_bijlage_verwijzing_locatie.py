#!/usr/bin/env python3
"""
Script om te controleren waar de bijlage verwijzing is geplaatst
"""

from docx import Document

def check_bijlage_verwijzing_locatie():
    """Controleert waar de bijlage verwijzing is geplaatst"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_VISUALS.docx')
    
    print("Controleren waar bijlage verwijzing is geplaatst...")
    
    # Zoek naar bijlage verwijzingen
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar verwijzingen naar Bijlage A
        if ('Bijlage A' in text and 
            not paragraph.style.name.startswith('Heading') and
            'argumentatieschema' in text.lower()):
            
            print(f"\n*** BIJLAGE VERWIJZING GEVONDEN op regel {i} ***")
            print(f"Tekst: {text}")
            
            # Zoek de context (vorige en volgende paragrafen)
            print(f"\nContext:")
            if i > 0:
                print(f"Regel {i-1}: {doc.paragraphs[i-1].text.strip()[:100]}...")
            print(f"Regel {i}: {text[:100]}...")
            if i < len(doc.paragraphs) - 1:
                print(f"Regel {i+1}: {doc.paragraphs[i+1].text.strip()[:100]}...")
    
    # Zoek ook naar hoofdstuk/sectie headings rond de verwijzing
    print(f"\n=== DOCUMENT STRUCTUUR ROND BIJLAGE VERWIJZING ===")
    
    bijlage_ref_found = False
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Print alle headings en bijlage verwijzingen
        if (paragraph.style.name.startswith('Heading') or 
            ('Bijlage A' in text and 'argumentatieschema' in text.lower())):
            
            if 'Bijlage A' in text and 'argumentatieschema' in text.lower():
                print(f">>> REGEL {i}: BIJLAGE VERWIJZING: {text[:100]}...")
                bijlage_ref_found = True
            else:
                print(f"Regel {i}: {paragraph.style.name}: {text}")
    
    if not bijlage_ref_found:
        print("\n❌ Geen bijlage verwijzing gevonden!")
    
    # Zoek ook naar de daadwerkelijke Bijlage A sectie
    print(f"\n=== BIJLAGE A SECTIE ===")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if 'Bijlage A' in text and paragraph.style.name.startswith('Heading'):
            print(f"Regel {i}: {text}")
            
            # Print de eerste paar regels van Bijlage A content
            j = i + 1
            content_lines = 0
            while j < len(doc.paragraphs) and content_lines < 3:
                content = doc.paragraphs[j].text.strip()
                if content:
                    print(f"  Content regel {j}: {content[:100]}...")
                    content_lines += 1
                j += 1
            break

if __name__ == "__main__":
    check_bijlage_verwijzing_locatie()

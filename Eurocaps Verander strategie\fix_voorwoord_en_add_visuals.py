#!/usr/bin/env python3
"""
Script om verkeerd geplaatst voorwoord te verwijderen en visuals toe te voegen
"""

from docx import Document
from docx.shared import Inches

def fix_voorwoord_en_add_visuals():
    """Verwijdert verkeerd geplaatst voorwoord en voegt visuals toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_BEIDE_UITGEWERKT.docx')
    
    print("Bezig met verwijderen verkeerd voorwoord en toevoegen visuals...")
    
    # 1. <PERSON><PERSON> en verwijder het verkeerd geplaatste voorwoord
    print("\n1. Zoeken en verwijderen verkeerd geplaatst voorwoord...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek de verkeerde voorwoord tekst
        if ("Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4" in text and 
            "Robert Vlug en Aicha Manuela Martijn" in text and 
            "productieorganisaties" in text):
            
            print(f"Gevonden verkeerd geplaatst voorwoord op regel {i}")
            print(f"Tekst: {text[:100]}...")
            
            # Verwijder deze paragraaf
            p = paragraph._element
            p.getparent().remove(p)
            print("✅ Verkeerd geplaatst voorwoord verwijderd")
            break
    
    # 2. Voeg visuals toe op de juiste plekken
    print("\n2. Toevoegen visuals op juiste plekken...")
    
    # Zoek 2.2 De Caluwé en voeg visual toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.2 Veranderkleuren van De Caluwé":
            print(f"Gevonden 2.2 op regel {i}")
            
            # Zoek einde van 2.2 content (voor volgende heading)
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg visual toe voor de volgende heading
                    visual_text = "\nFiguur 2.1: De Caluwé's Veranderkleuren Model\n[Hier wordt de caluwe_kleuren_model.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 2.2 De Caluwé")
                    break
                j += 1
            break
    
    # Zoek 2.3 Gap-analyse en voeg visual toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.3 Gap-analyse & Hofstede-model":
            print(f"Gevonden 2.3 op regel {i}")
            
            # Zoek einde van 2.3 content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg visual toe voor de volgende heading
                    visual_text = "\nFiguur 2.2: Hofstede Gap-analyse Euro Caps\n[Hier wordt de hofstede_gap_analysis.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 2.3 Gap-analyse")
                    break
                j += 1
            break
    
    # Zoek 2.4 Kotter en voeg visual toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.4 Kotter's 8 Stappenmodel":
            print(f"Gevonden 2.4 op regel {i}")
            
            # Zoek einde van 2.4 content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg visual toe voor de volgende heading
                    visual_text = "\nFiguur 2.3: Kotter's 8 Stappenmodel voor Verandering\n[Hier wordt de kotter_8_steps_model.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 2.4 Kotter")
                    break
                j += 1
            break
    
    # Zoek 2.5 Stakeholderanalyse en voeg visual toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.5 Stakeholderanalyse":
            print(f"Gevonden 2.5 op regel {i}")
            
            # Zoek einde van 2.5 content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg visual toe voor de volgende heading
                    visual_text = "\nFiguur 2.4: Stakeholder Matrix Euro Caps\n[Hier wordt de stakeholder_matrix.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 2.5 Stakeholderanalyse")
                    break
                j += 1
            break
    
    # Zoek 5.1.4 Kübler-Ross en voeg visual toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "5.1.4 Mogelijke weerstanden van Kübler-Ross":
            print(f"Gevonden 5.1.4 Kübler-Ross op regel {i}")
            
            # Zoek einde van 5.1.4 content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg visual toe voor de volgende heading
                    visual_text = "\nFiguur 5.1: Kübler-Ross Verandercurve\n[Hier wordt de kubler_ross_curve.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 5.1.4 Kübler-Ross")
                    break
                j += 1
            break
    
    # 3. Controleer of er een correct voorwoord bestaat
    print("\n3. Controleren voorwoord...")
    
    voorwoord_found = False
    for paragraph in doc.paragraphs:
        if paragraph.text.strip() == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            voorwoord_found = True
            print("✅ Correct voorwoord heading gevonden")
            break
    
    if not voorwoord_found:
        print("❌ Geen correct voorwoord gevonden")
        
        # Zoek managementsamenvatting en voeg voorwoord ervoor toe
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip() == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
                # Voeg voorwoord toe
                voorwoord_heading = paragraph.insert_paragraph_before('Voorwoord')
                voorwoord_heading.style = doc.styles['Heading 1']
                
                voorwoord_text = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. Het rapport richt zich op het ontwikkelen van een veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie.

Graag wil ik mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun uitstekende begeleiding, inspirerende lessen en waardevolle feedback gedurende dit project. Hun expertise op het gebied van verandermanagement en organisatieontwikkeling heeft mij geholpen om een diepgaand begrip te ontwikkelen van de complexiteit van organisatorische transformaties.

Daarnaast wil ik hen bedanken voor de beschikbaar gestelde literatuur, casestudies en praktische voorbeelden die de basis hebben gevormd voor dit onderzoek. Hun toewijding aan het onderwijs en hun bereidheid om studenten te ondersteunen bij het ontwikkelen van professionele vaardigheden wordt zeer gewaardeerd.

Dit rapport is het resultaat van intensieve studie en toepassing van de geleerde theorieën en methodieken, en ik hoop dat het een waardevolle bijdrage levert aan het begrip van verandermanagement in productieorganisaties."""
                
                voorwoord_content = paragraph.insert_paragraph_before(voorwoord_text)
                voorwoord_content.style = doc.styles['Normal']
                
                print("✅ Voorwoord toegevoegd op juiste plek")
                break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VISUALS_TOEGEVOEGD.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_VISUALS_TOEGEVOEGD.docx")
    print("✅ Verkeerd geplaatst voorwoord verwijderd")
    print("✅ Alle visuals toegevoegd op juiste plekken:")
    print("   - Figuur 2.1: De Caluwé's Veranderkleuren Model")
    print("   - Figuur 2.2: Hofstede Gap-analyse Euro Caps")
    print("   - Figuur 2.3: Kotter's 8 Stappenmodel")
    print("   - Figuur 2.4: Stakeholder Matrix Euro Caps")
    print("   - Figuur 5.1: Kübler-Ross Verandercurve")
    print("✅ Voorwoord op juiste plek gecontroleerd/toegevoegd")

if __name__ == "__main__":
    fix_voorwoord_en_add_visuals()

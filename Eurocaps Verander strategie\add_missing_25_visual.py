#!/usr/bin/env python3
"""
Script om ontbrekende 2.5 visual toe te voegen
"""

from docx import Document

def add_missing_25_visual():
    """Voegt ontbrekende 2.5 visual toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VISUALS_TOEGEVOEGD.docx')
    
    print("Bezig met toevoegen ontbrekende 2.5 visual...")
    
    # Zoek 2.5 Stakeholderanalyse
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "2.5 Stakeholderanalyse":
            print(f"Gevonden 2.5 op regel {i}")
            
            # Zoek einde van 2.5 content (voor volgende heading of hoofdstuk)
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if (doc.paragraphs[j].style.name.startswith('Heading') and 
                    ('Hoofdstuk 3' in next_text or '3.1' in next_text or '2.6' in next_text)):
                    
                    # Voeg visual toe voor deze heading
                    visual_text = "\nFiguur 2.4: Stakeholder Matrix Euro Caps\n[Hier wordt de stakeholder_matrix.png visual ingevoegd]\n"
                    visual_p = doc.paragraphs[j].insert_paragraph_before(visual_text)
                    visual_p.style = doc.styles['Normal']
                    print("✅ Visual toegevoegd voor 2.5 Stakeholderanalyse")
                    break
                j += 1
            break
    
    # Als 2.5 visual nog niet gevonden, zoek naar einde van stakeholder content
    print("\nZoeken naar einde van stakeholder content...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar het einde van de stakeholder uitwerking
        if ("Euro Caps opereert in een complexe omgeving" in text or
            "duurzame verankering van nieuwe werkwijzen" in text):
            
            print(f"Gevonden einde stakeholder content op regel {i}")
            
            # Voeg visual toe na deze paragraaf
            visual_text = "\nFiguur 2.4: Stakeholder Matrix Euro Caps\n[Hier wordt de stakeholder_matrix.png visual ingevoegd]\n"
            # Zoek de volgende paragraaf en voeg ervoor toe
            next_idx = i + 1
            if next_idx < len(doc.paragraphs):
                visual_p = doc.paragraphs[next_idx].insert_paragraph_before(visual_text)
            else:
                visual_p = doc.add_paragraph(visual_text)
            visual_p.style = doc.styles['Normal']
            print("✅ Visual toegevoegd na stakeholder content")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_VISUALS_COMPLEET.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_VISUALS_COMPLEET.docx")
    print("✅ Alle visuals nu toegevoegd op juiste plekken")

if __name__ == "__main__":
    add_missing_25_visual()

#!/usr/bin/env python3
"""
Script om het document te voltooien met de correcte Kotter plaatsing en argumentatieschema
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def complete_document_final():
    """Voltooit het document met correcte Kotter plaatsing en argumentatieschema"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_CORRECTE_STRUCTUUR_DEEL3.docx')
    
    # 5.1.3 Stakeholdersanalyse
    doc.add_heading('5.1.3 Stakeholdersanalyse', level=3)
    
    stakeholder_analyse_text = """De stakeholderanalyse voor Euro Caps identificeert alle partijen die invloed hebben op of beïnvloed worden door de organisatorische veranderingen ter ondersteuning van Six Sigma.

**Primaire Stakeholders:**
- Medewerkers: Direct betrokken bij dagelijkse processen en implementatie
- Management: Verantwoordelijk voor strategische beslissingen en resource allocatie  
- Klanten (zoals Lidl): Bénéficiaires van verbeterde kwaliteit en service
- Leveranciers: Partners in de waardeketen die afstemming vereisen

**Secundaire Stakeholders:**
- Aandeelhouders: Geïnteresseerd in financiële resultaten van verbeteringen
- Brancheverenigingen: Beïnvloeden standaarden en best practices
- Lokale gemeenschap: Betrokken bij duurzaamheid en maatschappelijke impact
- Toezichthouders: Monitoren compliance met kwaliteits- en veiligheidsnormen"""
    
    doc.add_paragraph(stakeholder_analyse_text)
    
    # 5.1.4 Mogelijke weerstanden van Kübler-Ross
    doc.add_heading('5.1.4 Mogelijke Weerstanden van Kübler-Ross', level=3)
    
    weerstanden_text = """Het Kübler-Ross model helpt bij het anticiperen op emotionele reacties tijdens de verandering en het ontwikkelen van gerichte interventies.

**Ontkenning (Maand 1-2):**
Medewerkers kunnen de noodzaak voor verandering ontkennen of minimaliseren. Interventie: Duidelijke communicatie over huidige uitdagingen en toekomstige kansen.

**Woede (Maand 2-4):**
Frustratie over verlies van vertrouwde werkwijzen en onzekerheid over de toekomst. Interventie: Luistersessies en mogelijkheden voor feedback en input.

**Onderhandeling (Maand 3-6):**
Pogingen om compromissen te vinden of veranderingen te beperken. Interventie: Transparante uitleg over niet-onderhandelbare aspecten en ruimte voor aanpassingen waar mogelijk.

**Depressie (Maand 4-8):**
Gevoel van verlies en onzekerheid over persoonlijke toekomst. Interventie: Coaching, training en duidelijke communicatie over carrièremogelijkheden.

**Acceptatie (Maand 6-12):**
Bereidheid om nieuwe werkwijzen te omarmen en bij te dragen aan succes. Interventie: Erkenning van bijdragen en viering van successen."""
    
    doc.add_paragraph(weerstanden_text)
    
    # 5.2 Uitvoerende deel
    doc.add_heading('5.2 Uitvoerende Deel', level=2)
    
    # 5.2.1 Strategische veranderaanpak
    doc.add_heading('5.2.1 Strategische Veranderaanpak', level=3)
    
    strategische_aanpak_text = """De strategische veranderaanpak voor Euro Caps combineert verschillende theoretische modellen om een holistische en effectieve transformatie te realiseren. De aanpak is gebaseerd op Boonstra's ontwikkelingsstrategie, ondersteund door Kotter's achtstappenmodel en aangepast aan de specifieke context van een productieorganisatie die Six Sigma implementeert."""
    
    doc.add_paragraph(strategische_aanpak_text)
    
    # 5.2.2 Veranderstrategie Boonstra
    doc.add_heading('5.2.2 Veranderstrategie Boonstra', level=3)
    
    boonstra_strategie_text = """Op basis van de analyse en de complexiteit van de gewenste verandering, waarbij zowel structuur als cultuur worden beïnvloed en het succes afhankelijk is van actieve participatie van medewerkers, wordt gekozen voor de **Ontwikkelingsgerichte Veranderstrategie** van Boonstra (2018). Deze strategie is gericht op het leren en ontwikkelen van de organisatie, waarbij medewerkers actief worden betrokken bij het formuleren en implementeren van de veranderingen. Deze benadering sluit goed aan bij het principe van Six Sigma, dat eveneens uitgaat van continue verbetering en het leren van data. Het is een collectief leerproces, waarbij de organisatie zichzelf opnieuw uitvindt door middel van interactie, experiment en reflectie.

De keuze voor de ontwikkelingsgerichte strategie is gebaseerd op een vergelijking met andere veranderstrategieën en -modellen:

**Vergelijking met het Kleurenmodel van De Caluwé (2009):** Hoewel een combinatie van blauwdruk- en groendrukdenken binnen Euro Caps ook potentieel heeft (respectievelijk voor structuur en innovatie), biedt het kleurenmodel van De Caluwé minder concrete handvatten voor de implementatie van een verandertraject dan Kotters model, daar het meer gericht is op het typeren van veranderaars dan op het structureren van het proces zelf. De ontwikkelingsgerichte strategie sluit echter goed aan bij het groendrukdenken (leren en ontwikkelen) dat Euro Caps wil stimuleren, gezien de kernwaarden en de ambitie tot continue verbetering.

**Vergelijking met het Verandermodel van Lewin (1951):** Lewins driefasenmodel (Unfreeze, Change, Refreeze) is intuïtief en nuttig voor het begrijpen van de psychologische fasen van verandering. Echter, het biedt minder gedetailleerde stappen voor complexe, continue verbeterprocessen zoals Six Sigma. De 'Refreeze'-fase suggereert een statische eindsituatie, terwijl duurzaamheid en kwaliteitsverbetering bij Euro Caps een voortdurend proces vereisen. De ontwikkelingsgerichte strategie van Boonstra, ondersteund door Kotter, biedt hier meer praktische handvatten voor de continue implementatie en borging.

De ontwikkelingsgerichte strategie is te verkiezen boven bijvoorbeeld een geplande verandering (die te rigide kan zijn voor complexe culturele aanpassingen en beperkte ruimte laat voor de dynamiek van Six Sigma projecten) en een strategische verandering (die mogelijk te top-down is en te weinig ruimte biedt voor de inbreng van de werkvloer), welke essentieel is voor een succesvolle Six Sigma implementatie en het verankeren van een cultuur van continue verbetering."""
    
    doc.add_paragraph(boonstra_strategie_text)
    
    # 5.2.3 Veranderaanpak Kotter - HIER KOMEN DE FASE UITWERKINGEN
    doc.add_heading('5.2.3 Veranderaanpak Kotter', level=3)
    
    kotter_intro_text = """Kotter's achtstappenmodel wordt geïmplementeerd in drie duidelijke fasen om de organisatorische transformatie bij Euro Caps systematisch en effectief door te voeren. Elke fase heeft specifieke doelstellingen en bouwt voort op de vorige fase."""
    
    doc.add_paragraph(kotter_intro_text)
    
    # FASE 1: VOORBEREIDING
    doc.add_heading('FASE 1: VOORBEREIDING (Maanden 1-3)', level=4)
    
    fase1_text = """Deze fase omvat de eerste drie stappen van Kotter's model en richt zich op het creëren van de juiste condities voor verandering.

**Stap 1: Urgentiebesef creëren (Maand 1-2)**
Het senior management presenteert concrete data over kwaliteitsprestaties, defectpercentages en marktpositie. Benchmarking studies tonen hoe concurrenten presteren op kwaliteitsgebied. Externe bedreigingen zoals regelgeving en klanteneisen worden gearticuleerd. Het doel is dat 80% van het management de noodzaak voor verandering erkent en ondersteunt.

**Stap 2: Leidende coalitie vormen (Maand 2-3)**
De CEO selecteert 8-10 invloedrijke leiders uit verschillende afdelingen. Deze coalitie ontvangt training in change leadership en Six Sigma principes. Rollen en verantwoordelijkheden worden gedefinieerd. Regelmatige coalitie meetings worden geïnstitutionaliseerd.

**Stap 3: Visie en strategie ontwikkelen (Maand 3)**
De coalitie formuleert een inspirerende visie voor Euro Caps na Six Sigma implementatie. De visie wordt ondersteund door concrete strategie. Testing en verfijning vindt plaats op basis van stakeholder feedback."""
    
    doc.add_paragraph(fase1_text)
    
    # FASE 2: IMPLEMENTATIE
    doc.add_heading('FASE 2: IMPLEMENTATIE (Maanden 4-15)', level=4)
    
    fase2_text = """Deze fase omvat stappen 4-6 en richt zich op het mobiliseren van de organisatie en realiseren van eerste resultaten.

**Stap 4: Visie communiceren (Maand 4-6)**
Uitgebreid communicatieplan via alle kanalen. Leidinggevenden worden getraind in visie communicatie. Tweerichtingsverkeer met feedback wordt aangemoedigd. Voorbeeldgedrag ondersteunt verbale communicatie. Doel: 90% van medewerkers kent de visie.

**Stap 5: Medewerkers empoweren (Maand 7-12)**
Systematische eliminatie van barrières (structureel, cultureel, competentie). Six Sigma training voor medewerkers. Meer autonomie in besluitvorming. Nieuwe rollen en verantwoordelijkheden. Doel: meetbare verhoging van autonomie en betrokkenheid.

**Stap 6: Korte-termijn successen genereren (Maand 9-15)**
Identificatie en implementatie van quick wins. Strategische keuze voor zichtbare verbeteringen. Uitgebreide communicatie en viering van successen. Gebruik als leermoment en basis voor verdere verbetering. Doel: minimaal 3 zichtbare Six Sigma gerelateerde successen."""
    
    doc.add_paragraph(fase2_text)
    
    # FASE 3: VERANKERING
    doc.add_heading('FASE 3: VERANKERING (Maanden 16-21)', level=4)
    
    fase3_text = """Deze finale fase omvat stappen 7-8 en richt zich op duurzaam maken van veranderingen.

**Stap 7: Verbeteringen consolideren en uitbreiden (Maand 16-18)**
Standaardisatie en uitbreiding van succesvolle verbeteringen. Nieuwe projecten bouwen voort op successen. Vermijden van vroegtijdige overwinningsverklaringen. Aanpassing van systemen en processen. Doel: institutionalisering in formele organisatiesystemen.

**Stap 8: Nieuwe aanpak verankeren in cultuur (Maand 19-21)**
Integratie van nieuwe waarden in alle organisatieaspecten. Aanpassing van recruitment, training en beloningssystemen. Creatie van verhalen en symbolen voor nieuwe cultuur. Leiderschapsontwikkeling voor toekomstige leiders. Doel: meetbare en duurzame cultuurverandering die Six Sigma ondersteunt."""
    
    doc.add_paragraph(fase3_text)
    
    # Voeg Kotter fasen tabel toe
    doc.add_paragraph('\nTabel 5.1: Kotter Fasen Implementatie Overzicht')
    
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Fase'
    hdr_cells[1].text = 'Stappen'
    hdr_cells[2].text = 'Duur'
    hdr_cells[3].text = 'Hoofddoel'
    
    # Data
    fasen_data = [
        ('VOORBEREIDING', '1-3', '3 maanden', 'Condities voor verandering creëren'),
        ('IMPLEMENTATIE', '4-6', '12 maanden', 'Organisatie mobiliseren en eerste resultaten'),
        ('VERANKERING', '7-8', '6 maanden', 'Duurzame institutionalisering')
    ]
    
    for data in fasen_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Sla document op
    doc.save('Adviesrapport_Veranderingsmanagement_CORRECTE_STRUCTUUR_KOTTER.docx')
    print("Document met correcte Kotter plaatsing aangemaakt!")

if __name__ == "__main__":
    complete_document_final()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om het correcte argumentatieschema toe te voegen in tabel format
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
from docx.enum.table import WD_TABLE_ALIGNMENT
import os

def add_argumentatie_schema_table(doc):
    """Voeg het argumentatieschema toe in tabel format"""
    doc.add_page_break()
    doc.add_heading('Argumentatieschema', level=1)
    
    intro_text = """Dit argumentatieschema toont de logische onderbouwing van alle keuzes en aanbevelingen in dit adviesrapport volgens het Toulmin model. Het schema presenteert de hoofdclaim, onderbouwende data, warrants en backing in een gestructureerde format."""
    doc.add_paragraph(intro_text)
    
    # Maak een tabel voor het argumentatieschema
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header rij
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Claim'
    hdr_cells[1].text = 'Data'
    hdr_cells[2].text = 'Warrant'
    hdr_cells[3].text = 'Backing'
    
    # Maak header bold
    for cell in hdr_cells:
        for paragraph in cell.paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
    
    # Hoofdclaim rij
    row_cells = table.add_row().cells
    row_cells[0].text = 'HOOFDCLAIM: Euro Caps moet een integrale veranderstrategie implementeren die Kotter\'s 8-stappenmodel combineert met Six Sigma methodologie'
    row_cells[1].text = 'Huidige functionele structuur en traditionele cultuur beperken flexibiliteit en innovatie'
    row_cells[2].text = 'Organisaties die niet meeveranderen met marktdynamiek verliezen concurrentievoordeel'
    row_cells[3].text = 'Literatuur over organisatieverandering (Kotter, 2012; Boonstra, 2013)'
    
    # Subclaim 1
    row_cells = table.add_row().cells
    row_cells[0].text = 'SUBCLAIM 1: Kotter\'s model is de beste keuze voor Euro Caps'
    row_cells[1].text = 'Kotter\'s 8-stappenmodel heeft bewezen effectiviteit in vergelijkbare organisaties'
    row_cells[2].text = 'Een gestructureerde veranderaanpak verhoogt de kans op succesvolle implementatie'
    row_cells[3].text = 'Empirisch onderzoek toont hogere slaagkans bij gebruik van Kotter\'s model'
    
    # Subclaim 2
    row_cells = table.add_row().cells
    row_cells[0].text = 'SUBCLAIM 2: Six Sigma is de optimale kwaliteitsmanagementmethode'
    row_cells[1].text = 'Six Sigma scoorde hoogst (18 punten) in kwantitatieve beslissingsmatrix'
    row_cells[2].text = 'De methode met de hoogste score in objectieve evaluatie is de beste keuze'
    row_cells[3].text = 'Six Sigma\'s focus op precisieprocessen past bij koffiecapsule productie'
    
    # Subclaim 3
    row_cells = table.add_row().cells
    row_cells[0].text = 'SUBCLAIM 3: DMAIC en Kotter integratie is effectief'
    row_cells[1].text = 'DMAIC methodologie kan worden geïntegreerd in Kotter\'s 8 stappen'
    row_cells[2].text = 'Integratie van complementaire methodologieën versterkt effectiviteit'
    row_cells[3].text = 'Literatuur over Six Sigma implementatie (Pyzdek & Keller, 2014)'
    
    # Subclaim 4
    row_cells = table.add_row().cells
    row_cells[0].text = 'SUBCLAIM 4: Stakeholder-specifieke aanpak is noodzakelijk'
    row_cells[1].text = 'Stakeholderanalyse toont verschillende belangen en behoeften per groep'
    row_cells[2].text = 'Aangepaste interventies per stakeholdergroep verhogen acceptatie'
    row_cells[3].text = 'De Caluwé\'s kleurenmodel (2006) toont verschillende veranderingsvoorkeuren'
    
    # Subclaim 5
    row_cells = table.add_row().cells
    row_cells[0].text = 'SUBCLAIM 5: Gefaseerde implementatie minimaliseert risico\'s'
    row_cells[1].text = 'Pilotproject in productieafdeling toont haalbaarheid en effectiviteit'
    row_cells[2].text = 'Gefaseerde aanpak zorgt voor leren en aanpassen tijdens implementatie'
    row_cells[3].text = 'Change management literatuur bevestigt voordelen gefaseerde implementatie'
    
    # Stel kolombreedte in
    for row in table.rows:
        for i, cell in enumerate(row.cells):
            if i == 0:  # Claim kolom breder
                cell.width = Inches(2.5)
            else:
                cell.width = Inches(1.8)
            
            # Tekst formatting
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                for run in paragraph.runs:
                    run.font.name = 'Arial'
                    run.font.size = Pt(10)
    
    doc.add_paragraph()
    
    # Tegenargumenten sectie
    doc.add_heading('Mogelijke tegenargumenten en weerlegging', level=2)
    
    tegenargumenten_text = """Tegenargument 1: "Six Sigma is te complex voor Euro Caps"
Weerlegging: Gefaseerde implementatie met adequate training en externe ondersteuning maakt Six Sigma toegankelijk voor alle medewerkers. De methodologie wordt stap voor stap geïntroduceerd met praktische training en ondersteuning.

Tegenargument 2: "Verandering verstoort huidige kwaliteit"
Weerlegging: Six Sigma versterkt juist de kwaliteitsfocus en HACCP integratie waarborgt voedselveiligheid. De methodologie is specifiek ontworpen om kwaliteit te verbeteren, niet te verstoren.

Tegenargument 3: "Kosten van implementatie zijn te hoog"
Weerlegging: Business case toont positieve ROI binnen achttien maanden door procesverbeteringen en defectreductie. De investering wordt terugverdiend door verhoogde efficiëntie en verminderde verspilling.

Tegenargument 4: "Medewerkers zullen weerstand bieden"
Weerlegging: Stakeholder-specifieke interventies en communicatieplan minimaliseren weerstand en verhogen acceptatie. De aanpak is specifiek ontworpen om verschillende stakeholdergroepen mee te nemen in de verandering."""
    
    doc.add_paragraph(tegenargumenten_text)
    
    # Conclusie argumentatie
    doc.add_heading('Conclusie argumentatieschema', level=2)
    
    conclusie_text = """De argumentatie toont aan dat de aanbevolen veranderstrategie gebaseerd is op solide theoretische fundamenten, empirische data en bewezen methodologieën. Alle keuzes zijn logisch onderbouwd volgens het Toulmin model met duidelijke claims, ondersteunende data, warrants en backing. Mogelijke tegenargumenten zijn adequaat weerlegd met concrete argumenten en bewijs.

De integratie van Kotter's change management met Six Sigma procesverbetering biedt Euro Caps de beste kans op succesvolle transformatie naar een meer flexibele, innovatieve en klantgerichte organisatie zonder verlies van de sterke kwaliteitsfocus. Het argumentatieschema demonstreert de logische consistentie en wetenschappelijke onderbouwing van alle aanbevelingen in dit rapport."""
    
    doc.add_paragraph(conclusie_text)
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_GECORRIGEERD_COMPLEET.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Voeg argumentatieschema toe
    document = add_argumentatie_schema_table(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Correct argumentatieschema toegevoegd in tabel format: {doc_path}")
    print("- Toulmin model structuur geïmplementeerd")
    print("- Hoofdclaim en 5 subclaims met data, warrant en backing")
    print("- Tegenargumenten en weerlegging toegevoegd")
    print("- Conclusie argumentatieschema toegevoegd")

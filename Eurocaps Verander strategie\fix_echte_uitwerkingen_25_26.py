#!/usr/bin/env python3
"""
Script om echte uitwerkingen toe te voegen voor 2.5 en 2.6
"""

from docx import Document
from docx.shared import Inches
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_echte_uitwerkingen_25_26():
    """Voegt echte uitgebreide uitwerkingen toe voor 2.5 en 2.6"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOLLEDIG_UITGEWERKT.docx')
    
    print("Bezig met toevoegen van echte uitwerkingen voor 2.5 en 2.6...")
    
    # 1. Zoek 2.5 en vervang korte tekst met echte uitwerking
    print("\n1. Vervangen 2.5 met echte uitwerking...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.5 Stakeholderanalyse":
            # Zoe<PERSON> de volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            # Verwijder alle bestaande content tussen 2.5 en volgende heading
            content_to_remove = []
            j = i + 1
            while j < next_heading_idx:
                content_to_remove.append(j)
                j += 1
            
            # Verwijder van achteren naar voren
            for idx in reversed(content_to_remove):
                if idx < len(doc.paragraphs):
                    p = doc.paragraphs[idx]._element
                    p.getparent().remove(p)
            
            # Voeg echte uitgebreide uitwerking toe
            stakeholder_uitwerking = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Deze methodiek, gebaseerd op Freeman's stakeholdertheorie (2010), vormt een essentieel onderdeel van effectief change management en biedt een gestructureerde benadering voor het begrijpen van de complexe relaties tussen een organisatie en haar omgeving.

Freeman's stakeholdertheorie onderscheidt twee hoofdcategorieën van stakeholders. Primaire stakeholders hebben directe invloed op de organisatie en worden direct beïnvloed door organisatorische beslissingen en veranderingen. Deze groep omvat medewerkers, klanten, leveranciers, aandeelhouders en het management. Secundaire stakeholders daarentegen hebben indirecte invloed en worden indirect beïnvloed door organisatorische activiteiten, zoals overheden, milieugroepen, media, concurrenten en de bredere gemeenschap.

Het proces van stakeholderanalyse bestaat uit vier systematische stappen die elkaar opvolgen en versterken. De eerste stap behelst de identificatie van alle relevante stakeholders door middel van brainstormsessies, documentanalyse en interviews met sleutelpersonen. Hierbij wordt een zo compleet mogelijk overzicht gecreëerd van alle partijen die op enigerlei wijze betrokken zijn bij of beïnvloed worden door de organisatorische verandering.

De tweede stap richt zich op de grondige analyse van stakeholderbelangen, waarbij voor elke geïdentificeerde stakeholder wordt onderzocht wat hun specifieke belangen, verwachtingen, behoeften en zorgen zijn ten aanzien van de voorgestelde verandering. Deze analyse vereist diepgaand onderzoek naar de motivaties en drijfveren van verschillende stakeholdergroepen.

In de derde stap wordt de invloed en attitude van stakeholders beoordeeld. Hierbij wordt gebruik gemaakt van invloed-belangmatrices waarbij stakeholders worden gepositioneerd op basis van hun relatieve invloed op het veranderingsproces en hun belang bij de uitkomst. Deze positionering helpt bij het prioriteren van stakeholders en het bepalen van de benodigde aandacht en resources.

De vierde en finale stap omvat de ontwikkeling van specifieke benaderingsstrategieën voor elke stakeholdergroep. Gebaseerd op hun positie in de invloed-belangmatrix worden verschillende strategieën ontwikkeld, variërend van intensief management voor stakeholders met hoge invloed en hoog belang tot monitoring voor stakeholders met lage invloed en laag belang.

Voor Euro Caps is stakeholderanalyse van cruciaal belang voor het succesvol implementeren van organisatorische veranderingen ter ondersteuning van Six Sigma. De koffiecapsuleproducent opereert in een complexe omgeving met diverse stakeholders die elk verschillende belangen hebben bij de organisatorische transformatie. Een effectieve stakeholderbenadering verhoogt de acceptatie van veranderingen, vermindert weerstand tijdens het implementatieproces en zorgt voor duurzame verankering van nieuwe werkwijzen.

De stakeholderanalyse voor Euro Caps identificeert verschillende kritieke partijen. Het senior management, bestaande uit CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland, heeft hoge invloed en hoog belang bij het succes van de Six Sigma implementatie vanwege hun verantwoordelijkheid voor strategische doelen en return on investment. De kwaliteitsafdeling onder leiding van Hoofd Kwaliteitsbeheer Kees Keurig heeft eveneens hoge invloed en hoog belang vanwege hun directe betrokkenheid bij procesverbetering en kwaliteitsstandaarden.

Klanten, waaronder grote afnemers, hebben hoog belang bij verbeterde productkwaliteit en service maar variërende invloed op interne processen. Medewerkers in de productie hebben gemiddelde invloed maar hoog belang vanwege de directe impact op hun dagelijkse werkzaamheden. Leveranciers hebben gemiddelde invloed en belang vanwege hun rol in de waardeketen en het belang van lange termijn partnerships.

Secundaire stakeholders zoals aandeelhouders hebben hoge invloed vanwege financiële beslissingsbevoegdheid maar hun belang richt zich primair op financiële prestaties. Toezichthouders hebben gemiddelde invloed en belang gericht op compliance en veiligheid, terwijl vakbonden lagere invloed hebben maar wel belang bij arbeidsvoorwaarden en werkzekerheid.

Tabel 2.1: Stakeholderanalyse Euro Caps Six Sigma Implementatie"""
            
            # Voeg content toe
            if next_heading_idx <= len(doc.paragraphs):
                # Herbereken next_heading_idx na verwijdering
                new_next_idx = i + 1
                while new_next_idx < len(doc.paragraphs):
                    if doc.paragraphs[new_next_idx].style.name.startswith('Heading'):
                        break
                    new_next_idx += 1
                
                if new_next_idx < len(doc.paragraphs):
                    stakeholder_content = doc.paragraphs[new_next_idx].insert_paragraph_before(stakeholder_uitwerking)
                else:
                    stakeholder_content = doc.add_paragraph(stakeholder_uitwerking)
                stakeholder_content.style = doc.styles['Normal']
                
                # Voeg stakeholder tabel toe
                table = doc.add_table(rows=1, cols=4)
                table.style = 'Table Grid'
                table.alignment = WD_TABLE_ALIGNMENT.CENTER
                
                # Header
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'Stakeholder'
                hdr_cells[1].text = 'Type'
                hdr_cells[2].text = 'Invloed'
                hdr_cells[3].text = 'Primair Belang'
                
                # Data
                stakeholder_data = [
                    ('Senior Management (Clement, Bosland)', 'Primair', 'Hoog', 'Strategische doelen en ROI'),
                    ('Kwaliteitsafdeling (Keurig)', 'Primair', 'Hoog', 'Procesverbetering en standaarden'),
                    ('Medewerkers Productie', 'Primair', 'Gemiddeld', 'Werkzekerheid en ontwikkeling'),
                    ('Klanten (grote afnemers)', 'Primair', 'Hoog', 'Productkwaliteit en service'),
                    ('Leveranciers', 'Primair', 'Gemiddeld', 'Lange termijn partnerships'),
                    ('Aandeelhouders', 'Secundair', 'Hoog', 'Financiële prestaties'),
                    ('Toezichthouders (NVWA)', 'Secundair', 'Gemiddeld', 'Compliance en voedselveiligheid'),
                    ('Vakbonden', 'Secundair', 'Laag', 'Arbeidsvoorwaarden')
                ]
                
                for data in stakeholder_data:
                    row_cells = table.add_row().cells
                    for k, value in enumerate(data):
                        row_cells[k].text = value
                
                # Voeg tabel uitleg toe
                tabel_uitleg = """De stakeholdermatrix toont de verschillende posities van stakeholders en biedt concrete handvatten voor het ontwikkelen van stakeholder-specifieke strategieën. Partijen in het kwadrant met hoge invloed en hoog belang (senior management, kwaliteitsafdeling) vereisen intensief management en regelmatige communicatie. Stakeholders met hoog belang maar lagere invloed (medewerkers) moeten geïnformeerd en betrokken worden gehouden, terwijl partijen met hoge invloed maar lager belang (aandeelhouders) tevreden gehouden moeten worden door focus op financiële resultaten."""
                
                tabel_uitleg_p = doc.add_paragraph(tabel_uitleg)
                tabel_uitleg_p.style = doc.styles['Normal']
            
            print("Echte uitgebreide 2.5 uitwerking toegevoegd met tabel")
            break
    
    # 2. Zoek 2.6 en vervang korte tekst met echte uitwerking
    print("\n2. Vervangen 2.6 met echte uitwerking...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.6 Verandercurve van Kübler-Ross":
            # Zoek de volgende heading
            next_heading_idx = i + 1
            while next_heading_idx < len(doc.paragraphs):
                if doc.paragraphs[next_heading_idx].style.name.startswith('Heading'):
                    break
                next_heading_idx += 1
            
            # Verwijder alle bestaande content tussen 2.6 en volgende heading
            content_to_remove = []
            j = i + 1
            while j < next_heading_idx:
                content_to_remove.append(j)
                j += 1
            
            # Verwijder van achteren naar voren
            for idx in reversed(content_to_remove):
                if idx < len(doc.paragraphs):
                    p = doc.paragraphs[idx]._element
                    p.getparent().remove(p)
            
            # Voeg echte uitgebreide uitwerking toe
            kublerross_uitwerking = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen en vormt een fundamenteel raamwerk voor het begrijpen van menselijke reacties op organisatorische transformaties. Dit model, oorspronkelijk ontwikkeld door psychiater Elisabeth Kübler-Ross (1969) voor het begrijpen van rouwprocessen bij terminaal zieke patiënten, is succesvol geadapteerd voor organisatorische verandering en biedt waardevolle inzichten voor change management professionals.

De theoretische basis van het model ligt in de erkenning dat verandering inherent verlies met zich meebrengt, waarbij individuen afscheid moeten nemen van vertrouwde situaties, werkwijzen, relaties of identiteiten. Deze verliezen triggeren emotionele reacties die voorspelbaar en universeel zijn, hoewel de intensiteit en duur per individu kunnen variëren. Het model biedt een roadmap voor het begrijpen, voorspellen en begeleiden van deze emotionele reis.

De verandercurve bestaat uit vijf opeenvolgende fasen die individuen doorgaans doorlopen tijdens significante veranderingen. De eerste fase betreft ontkenning, waarbij individuen de noodzaak, realiteit of permanentie van verandering weigeren te accepteren. In organisatorische context kunnen medewerkers beweren dat de huidige situatie adequaat is, dat verandering onnodig is, of dat de verandering tijdelijk zal zijn en vanzelf zal verdwijnen. Deze fase wordt gekenmerkt door cognitieve dissonantie waarbij nieuwe informatie wordt genegeerd of geherïnterpreteerd om bestaande overtuigingen te behouden.

De tweede fase omvat woede, gekenmerkt door intense frustratie en boosheid over het verlies van controle, zekerheid en vertrouwde werkwijzen. Medewerkers kunnen zich richten tegen het management, het veranderingsproces, nieuwe systemen of collega's die de verandering ondersteunen. Deze emoties kunnen zich uiten in openlijke kritiek, sabotage, verhoogd ziekteverzuim of verminderde productiviteit. Hoewel deze fase uitdagend is voor managers, is het een natuurlijk en noodzakelijk onderdeel van het verwerkingsproces.

De derde fase behelst onderhandeling, waarbij individuen pogingen ondernemen om controle terug te winnen door compromissen te zoeken of de verandering te beperken. Medewerkers kunnen voorstellen doen om delen van de oude situatie te behouden, uitzonderingen te bedingen, of de implementatie te vertragen. Deze fase toont beginnende acceptatie van de realiteit van verandering, maar ook wanhopige pogingen om de impact te minimaliseren.

Vervolgens treedt de fase van depressie op, gekenmerkt door gevoelens van verlies, verdriet en onzekerheid over de persoonlijke toekomst. Medewerkers kunnen zich overweldigd voelen door de omvang van de verandering en twijfelen aan hun vermogen om zich aan te passen. Productiviteit kan tijdelijk dalen, motivatie kan afnemen en sommige medewerkers kunnen overwegen om de organisatie te verlaten. Deze fase vereist empathische begeleiding en concrete ondersteuning.

De finale fase behelst acceptatie, waarbij individuen bereidheid tonen om de nieuwe realiteit te omarmen en constructief bij te dragen aan het succes van de verandering. Zij beginnen de voordelen van nieuwe werkwijzen in te zien, ontwikkelen nieuwe competenties en gedragingen, en kunnen zelfs ambassadeurs worden voor verdere veranderingen. Deze fase markeert de overgang van weerstand naar commitment en van verlies naar nieuwe mogelijkheden.

Voor Euro Caps is begrip van de Kübler-Ross curve cruciaal voor het effectief managen van de emotionele aspecten van de Six Sigma implementatie. De transformatie van een traditionele productieorganisatie naar een data-gedreven, continue verbeterende organisatie brengt significante veranderingen met zich mee in werkwijzen, verantwoordelijkheden, meetcriteria en organisatiecultuur. Deze veranderingen kunnen intense emotionele reacties oproepen bij medewerkers op alle niveaus.

Door de verschillende fasen te herkennen en te anticiperen, kan het management van Euro Caps passende ondersteuning bieden die aansluit bij de emotionele behoeften van medewerkers. In de ontkenningsfase is het essentieel om duidelijke, feitelijke communicatie te bieden over de noodzaak voor verandering, ondersteund door concrete data over kwaliteitsprestaties en marktpositionering. Tijdens de woede-fase moeten managers luisteren naar zorgen, empathie tonen en constructieve kanalen bieden voor het uiten van frustraties.

In de onderhandelingsfase kunnen managers flexibiliteit tonen waar mogelijk, terwijl zij duidelijk blijven over niet-onderhandelbare aspecten van de Six Sigma implementatie. De depressie-fase vereist intensieve ondersteuning door coaching, training en duidelijke communicatie over carrièremogelijkheden binnen de nieuwe organisatiestructuur. Tenslotte kan de acceptatie-fase worden versterkt door erkenning van bijdragen, viering van successen en het bieden van leiderschapsmogelijkheden aan early adopters.

Het model helpt ook bij het ontwikkelen van realistische tijdlijnen voor de verandering, waarbij wordt erkend dat emotionele verwerking tijd kost en niet kan worden geforceerd. Voor Euro Caps betekent dit dat de Six Sigma implementatie moet worden gepland met voldoende tijd voor emotionele aanpassing, met specifieke interventies voor elke fase van de curve.

Daarnaast biedt het model inzichten voor communicatiestrategieën die aansluiten bij de emotionele behoeften van medewerkers in verschillende fasen. Communicatie tijdens ontkenning vereist een andere benadering dan communicatie tijdens acceptatie, waarbij de tone, inhoud en frequentie moeten worden aangepast aan de emotionele staat van de doelgroep."""
            
            # Voeg content toe
            if next_heading_idx <= len(doc.paragraphs):
                # Herbereken next_heading_idx na verwijdering
                new_next_idx = i + 1
                while new_next_idx < len(doc.paragraphs):
                    if doc.paragraphs[new_next_idx].style.name.startswith('Heading'):
                        break
                    new_next_idx += 1
                
                if new_next_idx < len(doc.paragraphs):
                    kublerross_content = doc.paragraphs[new_next_idx].insert_paragraph_before(kublerross_uitwerking)
                else:
                    kublerross_content = doc.add_paragraph(kublerross_uitwerking)
                kublerross_content.style = doc.styles['Normal']
            
            print("Echte uitgebreide 2.6 uitwerking toegevoegd")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_UITWERKINGEN.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_UITWERKINGEN.docx")
    print("✅ 2.5 Stakeholderanalyse: Volledige theoretische uitwerking + tabel toegevoegd")
    print("✅ 2.6 Kübler-Ross: Volledige theoretische uitwerking toegevoegd")
    print("✅ Beide secties nu academisch niveau met diepgaande analyse")

if __name__ == "__main__":
    fix_echte_uitwerkingen_25_26()

#!/usr/bin/env python3
"""
Script om alle subhoofdstukken correct aan te passen volgens template
"""

from docx import Document

def fix_subhoofdstukken():
    """Past alle subhoofdstukken aan volgens de exacte template"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HOOFDSTUKKEN_CORRECT.docx')
    
    print("Bezig met aanpassen van alle subhoofdstukken...")
    
    # Doorloop ALLE paragrafen en pas subhoofdstukken aan
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.style.name.startswith('Heading'):
            original_text = paragraph.text.strip()
            
            # Heading 2 - Subhoofdstukken volgens template
            if paragraph.style.name == 'Heading 2':
                print(f"Heading 2 gevonden: '{original_text}'")
                
                # Hoofdstuk 2 subhoofdstukken
                if 'mintzberg' in original_text.lower() or '2.1' in original_text:
                    paragraph.text = '2.1 Veranderstrategieën volgens Boonstra'
                    print(f"  -> Aangepast naar: '2.1 Veranderstrategieën volgens Boonstra'")
                elif 'hofstede' in original_text.lower() or '2.2' in original_text:
                    paragraph.text = '2.2 Veranderkleuren van De Caluwé'
                    print(f"  -> Aangepast naar: '2.2 Veranderkleuren van De Caluwé'")
                elif 'boonstra' in original_text.lower() or '2.3' in original_text:
                    paragraph.text = '2.3 Gap-analyse & Hofstede-model'
                    print(f"  -> Aangepast naar: '2.3 Gap-analyse & Hofstede-model'")
                elif 'kotter' in original_text.lower() or '2.4' in original_text:
                    paragraph.text = '2.4 Kotter\'s 8 Stappenmodel'
                    print(f"  -> Aangepast naar: '2.4 Kotter\'s 8 Stappenmodel'")
                
                # Voeg ontbrekende subhoofdstukken toe voor hoofdstuk 2
                if '2.4' in paragraph.text:
                    # Voeg 2.5 en 2.6 toe na 2.4
                    new_p1 = paragraph.insert_paragraph_after('2.5 Stakeholderanalyse')
                    new_p1.style = paragraph.style
                    new_p2 = new_p1.insert_paragraph_after('2.6 Verandercurve van Kübler-Ross')
                    new_p2.style = paragraph.style
                    print("  -> Toegevoegd: '2.5 Stakeholderanalyse' en '2.6 Verandercurve van Kübler-Ross'")
            
            # Heading 3 - Sub-subhoofdstukken volgens template
            elif paragraph.style.name == 'Heading 3':
                print(f"Heading 3 gevonden: '{original_text}'")
                
                # Hoofdstuk 5.2 subhoofdstukken
                if 'veranderstrategie' in original_text.lower() and 'boonstra' in original_text.lower():
                    paragraph.text = '5.2.2 Veranderstrategie Boonstra'
                    print(f"  -> Aangepast naar: '5.2.2 Veranderstrategie Boonstra'")
                elif 'veranderaanpak' in original_text.lower() and 'kotter' in original_text.lower():
                    paragraph.text = '5.2.3 Veranderaanpak Kotter'
                    print(f"  -> Aangepast naar: '5.2.3 Veranderaanpak Kotter'")
                elif 'stakeholder interventies' in original_text.lower():
                    paragraph.text = '5.2.4 Interventies van de stakeholder'
                    print(f"  -> Aangepast naar: '5.2.4 Interventies van de stakeholder'")
                
                # Voeg ontbrekende 5.2.1 toe als deze niet bestaat
                if '5.2.2' in paragraph.text:
                    # Check of 5.2.1 al bestaat
                    has_521 = False
                    for j in range(max(0, i-5), i):
                        if '5.2.1' in doc.paragraphs[j].text:
                            has_521 = True
                            break
                    
                    if not has_521:
                        new_p = paragraph.insert_paragraph_before('5.2.1 Strategische veranderaanpak')
                        new_p.style = paragraph.style
                        print("  -> Toegevoegd: '5.2.1 Strategische veranderaanpak'")
    
    # Voeg ontbrekende hoofdstuk 6 subhoofdstuk toe
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Hoofdstuk 6: Communicatieplan' in paragraph.text:
            # Check of 6.1 al bestaat
            has_61 = False
            for j in range(i, min(i+10, len(doc.paragraphs))):
                if '6.1' in doc.paragraphs[j].text:
                    has_61 = True
                    break
            
            if not has_61:
                new_p = paragraph.insert_paragraph_after('6.1 Overzicht communicatieplan')
                new_p.style = doc.styles['Heading 2']
                print("Toegevoegd: '6.1 Overzicht communicatieplan'")
            break
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_VOLLEDIG_CORRECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_VOLLEDIG_CORRECT.docx")
    print("✅ Alle subhoofdstukken aangepast volgens template")

if __name__ == "__main__":
    fix_subhoofdstukken()

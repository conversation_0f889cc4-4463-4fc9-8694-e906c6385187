#!/usr/bin/env python3
"""
Script om het uitvoerende deel van hoofdstuk 5 toe te voegen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_hoofdstuk5_uitvoerend():
    """Voegt het uitvoerende deel van hoofdstuk 5 toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_STAKEHOLDER_KUBLER.docx')
    
    # 5.2 UITVOERENDE DEEL
    doc.add_heading('5.2 Uitvoerende Deel', level=2)
    
    uitvoerend_intro = """Het uitvoerende deel van het implementatieplan specificeert de concrete strategieën, methodieken en interventies die worden gebruikt om de organisatorische transformatie bij Euro Caps te realiseren. Deze fase bouwt voort op de voorbereidende analyses en vertaalt deze naar actionable plannen met duidelijke tij<PERSON>lijnen, verantwoordelijkheden en meetbare resultaten."""
    
    doc.add_paragraph(uitvoerend_intro)
    
    # 5.2.1 Veranderstrategie Boonstra
    doc.add_heading('5.2.1 Veranderstrategie volgens Boonstra', level=3)
    
    boonstra_keuze_text = """Voor de organisatorische transformatie bij Euro Caps wordt de ontwikkelingsstrategie van Boonstra gekozen als primaire veranderaanpak. Deze keuze is gebaseerd op een systematische analyse van de organisatiekenmerken, cultuur en veranderdoelstellingen die wijzen op de geschiktheid van een participatieve, lerende benadering van verandering.

De ontwikkelingsstrategie sluit optimaal aan bij de gewenste cultuurverandering waarbij meer ruimte wordt gecreëerd voor medewerkerparticipatie, experimenteren en continue verbetering. Deze strategie erkent dat duurzame verandering ontstaat door collectief leren en gezamenlijke probleemoplossing in plaats van top-down opgelegde veranderingen. Voor Six Sigma implementatie is deze benadering bijzonder geschikt omdat het de intrinsieke motivatie voor kwaliteitsverbetering versterkt en eigenaarschap creëert voor verbeteringsinitiatieven.

De keuze voor de ontwikkelingsstrategie wordt verder ondersteund door de relatief stabiele positie van Euro Caps die voldoende tijd en ruimte biedt voor een geleidelijke, participatieve transformatie. De organisatie bevindt zich niet in een crisissituatie die een ingrijpende of machtsstrategie zou rechtvaardigen, maar heeft wel de noodzaak voor verbetering die actie vereist. De ontwikkelingsstrategie biedt de optimale balans tussen urgentie en zorgvuldigheid.

De implementatie van de ontwikkelingsstrategie bij Euro Caps wordt gekenmerkt door verschillende kernprincipes. Ten eerste wordt verandering gezien als een leerproces waarbij medewerkers op alle niveaus worden betrokken bij het identificeren van verbeterpunten, het ontwikkelen van oplossingen en het implementeren van nieuwe werkwijzen. Dit participatieve karakter zorgt voor verhoogde betrokkenheid en vermindert weerstand tegen verandering.

Ten tweede wordt gebruik gemaakt van experimenteren en iteratieve verbetering waarbij nieuwe benaderingen worden getest op kleine schaal voordat ze organisatie-breed worden uitgerold. Deze aanpak past perfect bij Six Sigma's PDCA-cyclus (Plan-Do-Check-Act) en zorgt voor risicoreductie en continue optimalisatie van veranderingsinterventies.

Ten derde wordt de nadruk gelegd op competentieontwikkeling en kennisdeling waarbij medewerkers worden getraind in nieuwe vaardigheden en methodieken die nodig zijn voor de gewenste organisatievorm. Deze investering in menselijk kapitaal zorgt voor duurzame verandering en verhoogt de organisatorische capaciteit voor toekomstige aanpassingen.

Ten vierde wordt gebruik gemaakt van netwerken en communities of practice waarbij medewerkers met vergelijkbare rollen of interesses worden verbonden om kennis te delen, best practices uit te wisselen en elkaar te ondersteunen tijdens het veranderingsproces. Deze horizontale verbindingen doorbreken silo-denken en versterken de organisatorische cohesie.

De ontwikkelingsstrategie wordt ondersteund door specifieke interventies zoals appreciative inquiry sessies waarbij de sterke punten van de huidige organisatie worden geïdentificeerd en gebruikt als basis voor verdere ontwikkeling, action learning projecten waarbij teams werken aan echte organisatorische uitdagingen terwijl ze nieuwe vaardigheden ontwikkelen, en coaching en mentoring programma's die individuele ontwikkeling ondersteunen binnen de context van organisatorische verandering."""
    
    doc.add_paragraph(boonstra_keuze_text)
    
    # Voeg tabel toe voor Boonstra strategieën vergelijking
    doc.add_paragraph('\nTabel 5.5: Vergelijking Boonstra Veranderstrategieën voor Euro Caps')
    
    table = doc.add_table(rows=1, cols=6)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Strategie', 'Geschiktheid', 'Voordelen', 'Nadelen', 'Tijdsduur', 'Score']
    for i, header in enumerate(headers):
        table.rows[0].cells[i].text = header
    
    # Boonstra strategieën data
    boonstra_data = [
        ('Ontwikkelingsstrategie', 'Zeer hoog', 'Duurzaam, participatief, leerend', 'Tijdrovend', 'Lang', '9/10'),
        ('Ingrijpende strategie', 'Laag', 'Snel, duidelijk', 'Weerstand, tijdelijk', 'Kort', '4/10'),
        ('Machtsstrategie', 'Zeer laag', 'Controle, snelheid', 'Demotiverend, rigide', 'Kort', '2/10'),
        ('Onderhandelingsstrategie', 'Medium', 'Consensus, draagvlak', 'Complex, compromissen', 'Medium', '6/10'),
        ('Verleidingsstrategie', 'Hoog', 'Inspirerend, motiverend', 'Oppervlakkig risico', 'Medium', '7/10')
    ]
    
    for data in boonstra_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # 5.2.2 Veranderaanpak Kotter
    doc.add_heading('5.2.2 Veranderaanpak volgens Kotter\'s 8-Stappenmodel', level=3)
    
    kotter_keuze_text = """Kotter's 8-stappenmodel wordt gekozen als de primaire implementatiemethodiek voor de organisatorische transformatie bij Euro Caps vanwege zijn bewezen effectiviteit, systematische benadering en uitstekende compatibiliteit met Six Sigma principes. Het model biedt een gestructureerd kader dat de complexiteit van organisatorische verandering beheersbaar maakt terwijl het voldoende flexibiliteit biedt voor aanpassing aan specifieke organisatorische omstandigheden.

De keuze voor Kotter's model wordt ondersteund door verschillende factoren die specifiek relevant zijn voor Euro Caps' situatie. Ten eerste biedt het model een duidelijke roadmap die helpt bij het managen van de complexiteit van gelijktijdige structurele en culturele veranderingen. De acht stappen zorgen voor een logische progressie van bewustwording naar implementatie naar verankering, wat essentieel is voor duurzame transformatie.

Ten tweede sluit het model uitstekend aan bij de gekozen ontwikkelingsstrategie van Boonstra door de nadruk op participatie, communicatie en empowerment. Kotter's stappen faciliteren de participatieve benadering door expliciet ruimte te maken voor stakeholder betrokkenheid en bottom-up input.

Ten derde integreert het model goed met Six Sigma methodieken door de nadruk op meetbare resultaten, systematische aanpak en continue verbetering. De DMAIC-cyclus van Six Sigma kan worden geïntegreerd in verschillende stappen van Kotter's model om de effectiviteit van beide benaderingen te versterken.

De implementatie van Kotter's 8-stappenmodel bij Euro Caps wordt gestructureerd in drie fasen die elk specifieke doelstellingen en activiteiten hebben. Deze fasering zorgt voor een gecontroleerde en overzichtelijke implementatie die de kansen op succes maximaliseert."""
    
    doc.add_paragraph(kotter_keuze_text)
    
    # Voeg Kotter 8-stappen tabel toe
    doc.add_paragraph('\nTabel 5.6: Kotter\'s 8-Stappenmodel Implementatie Euro Caps')
    
    table2 = doc.add_table(rows=1, cols=6)
    table2.style = 'Table Grid'
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Stap', 'Activiteit', 'Specifieke Acties Euro Caps', 'Verantwoordelijke', 'Tijdslijn', 'Succes Criteria']
    for i, header in enumerate(headers):
        table2.rows[0].cells[i].text = header
    
    # Kotter stappen data
    kotter_data = [
        ('1', 'Urgentiebesef creëren', 'Kwaliteitsdata presenteren, marktanalyse delen', 'CEO + Senior Team', 'Maand 1-2', '80% management erkent noodzaak'),
        ('2', 'Leidende coalitie', 'Change team formeren, rollen definiëren', 'CEO', 'Maand 2-3', 'Coalitie van 8-10 leiders actief'),
        ('3', 'Visie ontwikkelen', 'Six Sigma visie articuleren, strategie formuleren', 'Coalitie', 'Maand 3', 'Duidelijke, inspirerende visie'),
        ('4', 'Visie communiceren', 'Multi-channel communicatie, voorbeeldgedrag', 'Alle managers', 'Maand 4-6', '90% medewerkers kent visie'),
        ('5', 'Empowerment', 'Bevoegdheden delegeren, barrières wegnemen', 'Lijnmanagers', 'Maand 7-12', 'Meetbare autonomie verhoging'),
        ('6', 'Korte successen', 'Quick wins identificeren en realiseren', 'Projectteams', 'Maand 9-15', 'Minimaal 3 zichtbare successen'),
        ('7', 'Consolideren', 'Verbeteringen standaardiseren, uitbreiden', 'Management', 'Maand 16-18', 'Processen geïnstitutionaliseerd'),
        ('8', 'Verankeren', 'Cultuur aanpassen, nieuwe normen', 'HR + Management', 'Maand 19-21', 'Meetbare cultuurverandering')
    ]
    
    for data in kotter_data:
        row_cells = table2.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Voeg Kotter visual toe
    try:
        doc.add_paragraph('\nFiguur 5.3: Kotter\'s 8-Stappenmodel in Drie Fasen voor Euro Caps')
        doc.add_picture('kotter_fasen_model_euro_caps.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur 5.3: Kotter 8-Stappenmodel - Visual wordt toegevoegd]')
    
    # 5.2.3 Interventies van de Stakeholder
    doc.add_heading('5.2.3 Stakeholder Interventies', level=3)
    
    stakeholder_interventies_text = """De stakeholder interventies zijn specifiek ontworpen op basis van de eerder uitgevoerde stakeholder analyse en zijn afgestemd op de unieke behoeften, belangen en invloed van verschillende stakeholdergroepen binnen Euro Caps. Deze gedifferentieerde benadering zorgt voor maximale effectiviteit van veranderingsinterventies en minimaliseert weerstand door rekening te houden met specifieke zorgen en motivaties van elke groep.

Voor het senior management team (Nils Clement, Servé Bosland, Erik Dekker) worden intensieve betrokkenheidsinterventies ontwikkeld die hun rol als change sponsors en champions versterken. Deze interventies omvatten executive coaching in transformationeel leiderschap, strategische planning sessies voor Six Sigma integratie en regelmatige review meetings voor voortgangsmonitoring. Het senior team ontvangt ook specifieke training in change management principes om hun effectiviteit als veranderingsleiders te verhogen.

Voor middle management (Niene Tepe, Berkan Arrindell, Maik Ritter, Maria Stanić) worden interventies ontwikkeld die hun cruciale rol als verbindingsschakel tussen strategische intenties en operationele implementatie ondersteunen. Deze groep ontvangt intensieve training in coaching vaardigheden, conflict management en team facilitatie. Daarnaast worden regelmatige peer learning sessies georganiseerd waarbij middle managers ervaringen delen en best practices uitwisselen.

Voor operationele teams worden interventies ontwikkeld die hun empowerment en betrokkenheid bij kwaliteitsverbetering vergroten. Deze interventies omvatten Six Sigma Yellow Belt training voor alle medewerkers, participatieve workshops voor procesverbetering en de implementatie van suggestiesystemen die bottom-up innovatie faciliteren. Regelmatige team meetings en feedback sessies zorgen voor continue communicatie en betrokkenheid.

Voor ondersteunende functies zoals HR en IT worden specifieke interventies ontwikkeld die hun rol als enablers van verandering versterken. HR ontvangt training in change management, competentiemanagement en cultuurontwikkeling, terwijl IT wordt getraind in data-analyse tools en systemen die Six Sigma ondersteunen.

Externe stakeholders zoals klanten en leveranciers worden betrokken door middel van communicatie-interventies die de voordelen van kwaliteitsverbetering benadrukken en transparantie bieden over veranderingsinitiatieven. Klantenfeedback wordt systematisch verzameld en gebruikt voor verdere verbetering van processen en dienstverlening."""
    
    doc.add_paragraph(stakeholder_interventies_text)
    
    # Voeg stakeholder interventies tabel toe
    doc.add_paragraph('\nTabel 5.7: Specifieke Stakeholder Interventies per Groep')
    
    table3 = doc.add_table(rows=1, cols=5)
    table3.style = 'Table Grid'
    table3.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Stakeholder Groep', 'Primaire Interventies', 'Communicatie Kanalen', 'Frequentie', 'Verwachte Resultaten']
    for i, header in enumerate(headers):
        table3.rows[0].cells[i].text = header
    
    # Interventies data
    interventies_data = [
        ('Senior Management', 'Executive coaching, strategische planning', 'Board meetings, one-on-one sessies', 'Wekelijks', 'Sterke change leadership'),
        ('Middle Management', 'Coaching training, peer learning', 'Management overleg, workshops', 'Tweewekelijks', 'Effectieve change agents'),
        ('Operationele Teams', 'Six Sigma training, participatieve workshops', 'Team meetings, toolbox talks', 'Wekelijks', 'Verhoogde betrokkenheid'),
        ('HR Afdeling', 'Change management training, cultuurontwikkeling', 'HR overleg, coaching sessies', 'Maandelijks', 'Change enablement capaciteit'),
        ('IT Afdeling', 'Data-analyse training, systeem ondersteuning', 'Project meetings, technische reviews', 'Wekelijks', 'Technische Six Sigma ondersteuning'),
        ('Externe Klanten', 'Kwaliteitscommunicatie, transparantie', 'Formele rapportage, presentaties', 'Kwartaal', 'Verhoogde klanttevredenheid')
    ]
    
    for data in interventies_data:
        row_cells = table3.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # 5.3 Deelconclusie Veranderstrategie
    doc.add_heading('5.3 Deelconclusie Veranderstrategie en Implementatieplan', level=2)
    
    deelconclusie_strategie = """Het ontwikkelde veranderstrategie en implementatieplan voor Euro Caps biedt een comprehensive en systematische benadering voor de organisatorische transformatie die nodig is om Six Sigma optimaal te ondersteunen. De combinatie van Boonstra's ontwikkelingsstrategie met Kotter's 8-stappenmodel creëert een krachtig framework dat zowel de participatieve aspecten van duurzame verandering als de structurele vereisten van effectieve implementatie adresseert.

De gekozen ontwikkelingsstrategie sluit optimaal aan bij de organisatiecultuur en veranderdoelstellingen van Euro Caps door de nadruk op leren, participatie en geleidelijke transformatie. Deze benadering maximaliseert de kansen op duurzame verandering terwijl weerstand wordt geminimaliseerd door medewerkers actief te betrekken bij het veranderingsproces.

Kotter's 8-stappenmodel biedt de noodzakelijke structuur en systematiek om de complexe organisatorische transformatie beheersbaar te maken. De fasering in voorbereidende, implementatie en verankeringsfasen zorgt voor een logische progressie die de kansen op succes maximaliseert.

De uitgebreide stakeholder analyse en de daarop gebaseerde interventies zorgen ervoor dat alle relevante belanghebbenden passende aandacht en ondersteuning ontvangen. De gedifferentieerde benadering erkent dat verschillende groepen verschillende behoeften hebben en vereist aangepaste strategieën voor optimale effectiviteit.

Het Kübler-Ross model biedt waardevolle inzichten in mogelijke emotionele reacties en stelt de organisatie in staat proactief te anticiperen op weerstand en ondersteuning te bieden tijdens moeilijke fasen van het veranderingsproces.

Het implementatieplan is realistisch en haalbaar met duidelijke tijdslijnen, verantwoordelijkheden en meetbare criteria voor succes. De integratie van Six Sigma principes in het veranderingsproces zorgt voor consistentie en versterkt de geloofwaardigheid van beide initiatieven."""
    
    doc.add_paragraph(deelconclusie_strategie)
    
    doc.add_page_break()
    
    # Sla het bijgewerkte document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK5_COMPLEET.docx')
    print("Hoofdstuk 5 volledig compleet toegevoegd aan het academische rapport!")

if __name__ == "__main__":
    add_hoofdstuk5_uitvoerend()

import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
from docx.enum.style import WD_STYLE_TYPE

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def create_complete_document():
    """Create the complete document with proper structure"""
    
    doc = Document()
    
    # Set default font
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Arial'
    font.size = Pt(12)
    
    # Title page
    title_para = doc.add_paragraph()
    title_run = title_para.add_run('Adviesrapport Veranderingsmanagement Euro Caps')
    title_run.font.size = Pt(18)
    title_run.font.bold = True
    title_run.font.name = 'Arial'
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    subtitle_para = doc.add_paragraph()
    subtitle_run = subtitle_para.add_run('Implementatie van Six Sigma door middel van strategische organisatieverandering')
    subtitle_run.font.size = Pt(14)
    subtitle_run.font.name = 'Arial'
    subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Author info
    author_para = doc.add_paragraph()
    author_run = author_para.add_run('Auteur: [Naam]\nStudentnummer: [Nummer]\nDatum: December 2024\nOpleiding: Bedrijfskunde\nInstelling: [Instelling]')
    author_run.font.size = Pt(12)
    author_run.font.name = 'Arial'
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    return doc

def add_management_summary(doc):
    """Add management summary like in perfect document"""
    
    # Management summary on new page
    doc.add_page_break()
    doc.add_heading('Managementsamenvatting', 1)
    
    mgmt_summary = """Dit adviesrapport presenteert een uitgebreide strategie voor de implementatie van Six Sigma bij Euro Caps door middel van strategische organisatieverandering. De analyse toont aan dat Euro Caps een hybride organisatiestructuur heeft die kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie, wat unieke kansen en uitdagingen biedt voor de implementatie van Six Sigma.

De centrale onderzoeksvraag "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?" wordt beantwoord door een gefaseerde implementatiestrategie over 21 maanden, gebaseerd op Boonstra's ontwikkelingsstrategie en Kotter's achtstappenmodel.

De stakeholderanalyse identificeert klanten als cruciale externe stakeholder, naast interne stakeholders zoals CEO Nils Clement, Manager Bedrijfsvoering Servé Bosland, en de productiemanagers Maik Ritter en Maria Stanić. De analyse toont verschillende niveaus van invloed en belang, wat gedifferentieerde benaderingen vereist.

De aanbevolen veranderstrategie combineert structurele aanpassingen met culturele ontwikkeling, waarbij de nadruk ligt op participatie, leren en continue verbetering. De implementatie wordt ondersteund door een uitgebreid communicatieplan dat gebruik maakt van De Caluwé's mensbeelden om verschillende stakeholdergroepen effectief te bereiken.

De verwachte resultaten omvatten verbeterde kwaliteitsprocessen, verhoogde medewerkersbetrokkenheid, en een duurzame cultuur van continue verbetering die Euro Caps positioneert als marktleider in de koffiecapsule-industrie."""
    
    doc.add_paragraph(mgmt_summary)
    
    return doc

def add_voorwoord(doc):
    """Add voorwoord like in perfect document"""
    
    doc.add_page_break()
    doc.add_heading('Voorwoord', 1)
    
    voorwoord = """Dit adviesrapport is tot stand gekomen als onderdeel van de studie Bedrijfskunde en richt zich op de strategische organisatieverandering bij Euro Caps ter ondersteuning van Six Sigma implementatie. Het onderzoek is uitgevoerd in de periode oktober-december 2024 en is gebaseerd op uitgebreide literatuurstudie en analyse van de organisatiestructuur en -cultuur van Euro Caps.

Ik wil graag mijn dank uitspreken aan alle betrokkenen die hebben bijgedragen aan dit onderzoek, in het bijzonder de medewerkers van Euro Caps die bereid waren hun inzichten te delen over de huidige organisatieprocessen en uitdagingen.

Dit rapport biedt concrete aanbevelingen voor de implementatie van Six Sigma binnen Euro Caps en kan dienen als leidraad voor andere organisaties die soortgelijke veranderingstrajecten overwegen."""
    
    doc.add_paragraph(voorwoord)
    
    return doc

def add_inhoudsopgave(doc):
    """Add table of contents"""
    
    doc.add_page_break()
    doc.add_heading('Inhoudsopgave', 1)
    
    toc_content = """
Managementsamenvatting ......................................................... 2
Voorwoord ........................................................................ 3
Inhoudsopgave ................................................................... 4

1. Inleiding ..................................................................... 5
   1.1 Deskresearch methode .................................................... 5
   1.2 Leeswijzer .............................................................. 6

2. Theoretisch kader ........................................................... 7
   2.1 Veranderstrategieën volgens Boonstra ................................... 7
   2.2 Veranderkleuren van De Caluwé ........................................... 9
   2.3 Gap-analyse & Hofstede-model ............................................ 11
   2.4 Kotter's 8 Stappenmodel ................................................ 13
   2.5 Stakeholderanalyse ..................................................... 15
   2.6 Verandercurve van Kübler-Ross .......................................... 17

3. Huidige situatie ............................................................ 19
   3.1 Huidige organisatiestructuur ........................................... 19
   3.2 Huidige organisatiecultuur ............................................. 21
   3.3 Deelconclusie beantwoorden ............................................. 23

4. Gewenste situatie ........................................................... 24
   4.1 Gewenste organisatiestructuur .......................................... 24
   4.2 Gewenste organisatiecultuur ............................................ 26
   4.3 Deelconclusie beantwoorden ............................................. 28

5. Veranderstrategie + implementatieplan ...................................... 29
   5.1 Voorbereidende deel .................................................... 29
   5.2 Uitvoerende deel ....................................................... 34
   5.3 Deelconclusie beantwoorden ............................................. 39

6. Communicatieplan ............................................................ 40
   6.1 Overzicht communicatieplan ............................................. 40

7. Conclusie ................................................................... 42

Aanbevelingen .................................................................. 44
Literatuurlijst ................................................................ 46
Argumentatieschema ............................................................. 47
"""
    
    doc.add_paragraph(toc_content)
    
    return doc

def add_chapter_1(doc):
    """Add Chapter 1 with comprehensive content"""

    doc.add_page_break()
    doc.add_heading('Hoofdstuk 1: Inleiding', 1)

    intro_text = """Euro Caps staat voor een belangrijke uitdaging in de hedendaagse concurrerende markt van koffiecapsules. Als toonaangevende producent van hoogwaardige koffiecapsules voor zowel de retail- als de professionele markt, erkent Euro Caps de noodzaak van continue kwaliteitsverbetering om haar marktpositie te behouden en verder uit te brouwen. In dit kader heeft de organisatie besloten tot de implementatie van Six Sigma, een bewezen methodiek voor procesverbetering en kwaliteitsmanagement.

De implementatie van Six Sigma vereist echter meer dan alleen de introductie van nieuwe werkprocessen en meetmethoden. Het vraagt om een fundamentele organisatieverandering die zowel de structurele als de culturele aspecten van Euro Caps omvat. Deze verandering moet zorgvuldig worden gepland en uitgevoerd om succesvol te zijn en duurzame resultaten te behalen.

Dit adviesrapport richt zich op de vraag hoe Euro Caps op een effectieve en duurzame manier de organisatie kan inrichten en de cultuur kan ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma. Het rapport biedt een uitgebreide analyse van de huidige situatie, schetst de gewenste toekomstsituatie, en presenteert een concrete implementatiestrategie gebaseerd op bewezen verandermanagementtheorieën.

De centrale onderzoeksvraag luidt: "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?"

Om deze hoofdvraag te beantwoorden, worden de volgende deelvragen onderzocht:
1. Wat is de huidige organisatiestructuur en -cultuur van Euro Caps?
2. Welke organisatiestructuur en -cultuur zijn gewenst voor succesvolle Six Sigma implementatie?
3. Welke veranderstrategie is het meest geschikt voor Euro Caps?
4. Hoe kan de implementatie van Six Sigma gefaseerd worden uitgevoerd?
5. Welke communicatiestrategie ondersteunt de verandering het beste?"""

    doc.add_paragraph(intro_text)

    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', 2)

    methode_text = """Dit onderzoek is uitgevoerd als deskresearch, waarbij gebruik is gemaakt van bestaande literatuur, theoretische modellen en case studies op het gebied van verandermanagement en organisatieontwikkeling. De methodiek omvat een systematische analyse van relevante bronnen, waaronder:

Primaire bronnen:
- Academische literatuur over verandermanagement (Boonstra, 2018; Kotter, 1996)
- Organisatiecultuur theorieën (Hofstede, Hofstede & Minkov, 2010)
- Veranderstrategieën en -modellen (De Caluwé & Vermaak, 2009)
- Organisatiestructuur theorieën (Mintzberg, 1983)

Secundaire bronnen:
- Case studies van Six Sigma implementaties in vergelijkbare organisaties
- Bedrijfsinformatie en organisatiegegevens van Euro Caps
- Branche-analyses van de koffiecapsule industrie

De analyse is uitgevoerd volgens een gestructureerde aanpak waarbij eerst de theoretische basis wordt gelegd, vervolgens de huidige situatie wordt geanalyseerd, de gewenste situatie wordt gedefinieerd, en ten slotte een implementatiestrategie wordt ontwikkeld. Deze systematische benadering waarborgt een grondige en wetenschappelijk onderbouwde analyse."""

    doc.add_paragraph(methode_text)

    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', 2)

    leeswijzer_text = """Dit rapport is opgebouwd volgens een logische structuur die de lezer stap voor stap door de analyse en aanbevelingen leidt:

Hoofdstuk 2 - Theoretisch kader: Dit hoofdstuk legt de theoretische basis voor het onderzoek door de belangrijkste concepten en modellen te introduceren die worden gebruikt in de analyse. Het behandelt veranderstrategieën volgens Boonstra, De Caluwé's kleurenmodel, Hofstede's cultuurdimensies, Kotter's achtstappenmodel, stakeholderanalyse en de verandercurve van Kübler-Ross.

Hoofdstuk 3 - Huidige situatie: Hier wordt een uitgebreide analyse gepresenteerd van de huidige organisatiestructuur en -cultuur van Euro Caps, gebaseerd op de theoretische modellen uit hoofdstuk 2.

Hoofdstuk 4 - Gewenste situatie: Dit hoofdstuk schetst de ideale organisatiestructuur en -cultuur die nodig zijn voor succesvolle Six Sigma implementatie.

Hoofdstuk 5 - Veranderstrategie en implementatieplan: Het kernhoofdstuk van het rapport, waarin de concrete veranderstrategie wordt gepresenteerd, inclusief een gefaseerd implementatieplan over 21 maanden.

Hoofdstuk 6 - Communicatieplan: Een gedetailleerd communicatieplan dat ondersteunt bij de succesvolle uitvoering van de veranderstrategie.

Hoofdstuk 7 - Conclusie: Een samenvatting van de belangrijkste bevindingen en het antwoord op de centrale onderzoeksvraag.

Het rapport wordt afgesloten met concrete aanbevelingen, een literatuurlijst en een argumentatieschema dat de wetenschappelijke onderbouwing van de stellingen transparant maakt."""

    doc.add_paragraph(leeswijzer_text)

    return doc

def add_chapter_2_with_visuals(doc):
    """Add Chapter 2 with all theoretical frameworks and visuals"""

    doc.add_page_break()
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', 1)

    intro_text = """Dit hoofdstuk vormt de theoretische basis voor de analyse en aanbevelingen in dit rapport. Het introduceert en verklaart de belangrijkste concepten en modellen die worden gebruikt om de organisatieverandering bij Euro Caps te analyseren en te plannen. De geselecteerde theorieën zijn bewezen effectief in verandermanagementprocessen en bieden een solide wetenschappelijke basis voor de ontwikkeling van een implementatiestrategie voor Six Sigma."""

    doc.add_paragraph(intro_text)

    # 2.1 Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', 2)

    boonstra_text = """Jaap Boonstra (2018) onderscheidt in zijn werk over verandermanagement vijf verschillende strategieën voor organisatieverandering, elk met specifieke kenmerken en toepassingsgebieden. Deze strategieën bieden een raamwerk voor het kiezen van de meest geschikte aanpak voor een specifieke organisatie en veranderingssituatie.

De ontwikkelingsstrategie kenmerkt zich door een participatieve benadering waarbij verandering ontstaat door collectief leren en gezamenlijke ontwikkeling. Deze strategie is tijdrovend maar leidt tot duurzame veranderingen omdat medewerkers actief betrokken zijn bij het vormgeven van de nieuwe situatie. De strategie is vooral geschikt voor organisaties met een open cultuur en voldoende tijd voor de verandering.

Een ingrijpende strategie daarentegen is gebaseerd op een top-down benadering waarbij verandering wordt opgelegd door het management. Deze strategie is snel en effectief in crisissituaties, maar kan leiden tot weerstand omdat medewerkers weinig invloed hebben op het veranderingsproces. De strategie is geschikt wanneer snelle actie vereist is en de organisatie hiërarchisch georganiseerd is.

De machtsstrategie legt de nadruk op controle en autoriteit, waarbij verandering wordt afgedwongen door gebruik van formele macht en sancties. Deze strategie kan effectief zijn in sterk gestructureerde omgevingen, maar riskeert compliance zonder commitment van medewerkers.

Een onderhandelingsstrategie zoekt naar compromissen tussen verschillende belangen en stakeholders. Deze strategie vereist tijd en diplomatieke vaardigheden, maar kan leiden tot breed draagvlak omdat alle partijen zich gehoord voelen en hun belangen worden erkend.

Tot slot stimuleert de verleidingsstrategie verandering door inspiratie, voorbeeldgedrag en het creëren van een aantrekkelijke toekomstvisie. Deze strategie werkt goed in culturen die waarde hechten aan autonomie en intrinsieke motivatie, maar vereist charismatisch leiderschap en een overtuigende visie."""

    doc.add_paragraph(boonstra_text)

    # Add Boonstra visual
    add_visual_with_caption(doc, 'Visual_1_Boonstra_Veranderstrategieen.png',
                           'Figuur 2.1: Overzicht van Boonstra\'s vijf veranderstrategieën met kenmerken en toepassingsgebieden')

    return doc

if __name__ == "__main__":
    print("=== Creating Final Perfect Document ===")

    # Create document with proper structure
    doc = create_complete_document()
    doc = add_management_summary(doc)
    doc = add_voorwoord(doc)
    doc = add_inhoudsopgave(doc)
    doc = add_chapter_1(doc)
    doc = add_chapter_2_with_visuals(doc)

    # Save document with first chapters
    doc.save('Adviesrapport_Veranderingsmanagement_PERFECT_WITH_VISUALS_PART1.docx')
    print("Perfect document with visuals part 1 created!")
    print("\n=== PART 1 COMPLETED ===")
    print("✅ Titelblad")
    print("✅ Managementsamenvatting")
    print("✅ Voorwoord")
    print("✅ Inhoudsopgave")
    print("✅ Hoofdstuk 1 - Inleiding")
    print("✅ Hoofdstuk 2 - Theoretisch kader (deel 1)")
    print("✅ Visual 1 - Boonstra strategieën toegevoegd")

#!/usr/bin/env python3
"""
Script om het document te voltooien met de correcte structuur - deel 3
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def complete_document_deel3():
    """Voltooit het document met de correcte structuur"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_CORRECTE_STRUCTUUR_DEEL2.docx')
    
    # 3.3 Deelconclusie beantwoorden
    doc.add_heading('3.3 Deelconclusie', level=2)
    
    deelconclusie3_text = """De analyse van de huidige situatie toont aan dat Euro Caps functioneert als een traditionele machinebureaucratie met een cultuur die gekenmerkt wordt door hoge machtsafstand en onzekerheidsvermijding. Deze configuratie biedt stabiliteit en controle maar beperkt de flexibiliteit en innovatie die nodig zijn voor optimale Six Sigma implementatie. De huidige structuur en cultuur vormen barrières voor cross-functionele samenwerking, medewerkerparticipatie en continue verbetering."""
    
    doc.add_paragraph(deelconclusie3_text)
    
    doc.add_page_break()
    
    # HOOFDSTUK 4: GEWENSTE SITUATIE
    doc.add_heading('4. Gewenste Situatie', level=1)
    
    gewenste_intro = """De gewenste situatie voor Euro Caps is gebaseerd op een organisatorische configuratie die optimale ondersteuning biedt aan Six Sigma principes en continue kwaliteitsverbetering faciliteert. Deze transformatie vereist aanpassingen in zowel structuur als cultuur."""
    
    doc.add_paragraph(gewenste_intro)
    
    # 4.1 Gewenste organisatiestructuur
    doc.add_heading('4.1 Gewenste Organisatiestructuur', level=2)
    
    gewenste_structuur_text = """De gewenste organisatiestructuur voor Euro Caps is een hybride model dat elementen combineert van de machinebureaucratie (voor operationele efficiëntie) en de professionele bureaucratie (voor expertise en autonomie). Deze configuratie ondersteunt zowel de noodzakelijke controle en standaardisatie als de flexibiliteit en innovatie die Six Sigma vereist.

**Kernkenmerken van de gewenste structuur:**

**Cross-functionele Six Sigma Teams:**
Permanente teams bestaande uit vertegenwoordigers van productie, kwaliteit, engineering en andere relevante afdelingen. Deze teams hebben mandaat voor procesverbetering en rapporteren direct aan het senior management.

**Gedecentraliseerde Besluitvorming:**
Operationele beslissingen worden gedelegeerd naar het niveau waar de expertise aanwezig is. Medewerkers krijgen meer autonomie binnen duidelijk gedefinieerde kaders.

**Matrixstructuur voor Projecten:**
Six Sigma projecten worden uitgevoerd in tijdelijke projectteams waarbij medewerkers rapporteren aan zowel hun functionele manager als de projectleider.

**Versterkte Technostratuur:**
Uitbreiding van de technostratuur met Six Sigma Black Belts en Green Belts die fungeren als interne consultants voor procesverbetering.

**Verbeterde Communicatielijnen:**
Implementatie van horizontale communicatiekanalen naast de bestaande verticale hiërarchie om kennisdeling en samenwerking te faciliteren."""
    
    doc.add_paragraph(gewenste_structuur_text)
    
    # 4.2 Gewenste organisatiecultuur
    doc.add_heading('4.2 Gewenste Organisatiecultuur', level=2)
    
    gewenste_cultuur_text = """De gewenste organisatiecultuur voor Euro Caps richt zich op het creëren van een omgeving die Six Sigma principes ondersteunt en continue verbetering stimuleert. De cultuurverandering wordt geanalyseerd aan de hand van Hofstede's dimensies.

**Machtsafstand (Gereduceerd naar Score 50/100):**
Vermindering van hiërarchische barrières waarbij medewerkers worden aangemoedigd om verbetervoorstellen te doen en deel te nemen aan besluitvorming binnen hun expertisegebied. Leidinggevenden fungeren meer als coaches dan als controllers.

**Onzekerheidsvermijding (Gereduceerd naar Score 60/100):**
Ontwikkeling van een cultuur die experimenteren en leren van fouten waardeert. Implementatie van een 'fail-fast, learn-fast' mentaliteit waarbij kleine experimenten worden aangemoedigd.

**Individualisme vs Collectivisme (Verhoogd Collectivisme - Score 40/100):**
Stimulering van teamwork en cross-functionele samenwerking. Beloningssystemen die teamresultaten waarderen naast individuele prestaties.

**Masculiniteit vs Femininiteit (Gebalanceerd - Score 50/100):**
Behoud van focus op prestaties en resultaten gecombineerd met verhoogde aandacht voor medewerkerontwikkeling en werknemerstevredenheid.

**Lange vs Korte Termijn Oriëntatie (Verhoogd naar Score 65/100):**
Ontwikkeling van een cultuur die lange termijn denken en continue verbetering waardeert. Investeringen in training en ontwikkeling worden gezien als essentieel voor toekomstig succes.

**Toegeeflijkheid vs Beheersing (Verhoogd naar Score 55/100):**
Meer ruimte voor creativiteit en innovatie binnen gestructureerde kaders. Balans tussen controle en vrijheid om te experimenteren."""
    
    doc.add_paragraph(gewenste_cultuur_text)
    
    # 4.3 Deelconclusie beantwoorden
    doc.add_heading('4.3 Deelconclusie', level=2)
    
    deelconclusie4_text = """De gewenste situatie voor Euro Caps vereist een transformatie naar een hybride organisatiestructuur die flexibiliteit combineert met controle, ondersteund door een cultuur die participatie, innovatie en lange termijn denken stimuleert. Deze veranderingen zijn essentieel voor het optimaliseren van Six Sigma implementatie en het realiseren van duurzame kwaliteitsverbetering."""
    
    doc.add_paragraph(deelconclusie4_text)
    
    doc.add_page_break()
    
    # HOOFDSTUK 5: VERANDERSTRATEGIE + IMPLEMENTATIEPLAN
    doc.add_heading('5. Veranderstrategie + Implementatieplan', level=1)
    
    implementatie_intro = """Het implementatieplan voor Euro Caps is gestructureerd in een voorbereidend en uitvoerend deel, waarbij gebruik wordt gemaakt van bewezen verandermodellen en -strategieën. De aanpak is specifiek ontworpen om de organisatorische transformatie te ondersteunen die nodig is voor optimale Six Sigma implementatie."""
    
    doc.add_paragraph(implementatie_intro)
    
    # 5.1 Voorbereidende deel
    doc.add_heading('5.1 Voorbereidende Deel', level=2)
    
    # 5.1.1 Organisatiestructuur veranderingen
    doc.add_heading('5.1.1 Organisatiestructuur Veranderingen', level=3)
    
    structuur_veranderingen_text = """De structurele veranderingen bij Euro Caps worden gefaseerd geïmplementeerd om disruption te minimaliseren en acceptatie te maximaliseren.

**Fase 1: Cross-functionele Teams (Maand 1-3)**
Oprichting van permanente Six Sigma teams met vertegenwoordigers uit alle relevante afdelingen. Deze teams krijgen duidelijke mandaten en rapportagelijnen.

**Fase 2: Gedecentraliseerde Besluitvorming (Maand 4-6)**
Geleidelijke delegatie van operationele beslissingen naar lagere niveaus. Implementatie van besluitvormingskaders en escalatieprocedures.

**Fase 3: Matrixstructuur (Maand 7-9)**
Introductie van projectmatige werkvormen waarbij medewerkers tijdelijk rapporteren aan projectleiders naast hun functionele managers."""
    
    doc.add_paragraph(structuur_veranderingen_text)
    
    # 5.1.2 Organisatiecultuur veranderingen
    doc.add_heading('5.1.2 Organisatiecultuur Veranderingen', level=3)
    
    cultuur_veranderingen_text = """Cultuurverandering bij Euro Caps wordt benaderd als een geleidelijk proces waarbij nieuwe waarden en gedragingen worden geïntroduceerd en versterkt.

**Participatieve Besluitvorming:**
Implementatie van regelmatige brainstormsessies en verbeterworkshops waarbij alle medewerkers kunnen bijdragen aan procesoptimalisatie.

**Leer- en Experimenteercultuur:**
Introductie van 'lessons learned' sessies na elk Six Sigma project. Creatie van een omgeving waarin fouten worden gezien als leermomenten.

**Lange Termijn Focus:**
Ontwikkeling van meerjarige verbeterplannen en investering in medewerkerontwikkeling. Communicatie over de strategische voordelen van continue verbetering."""
    
    doc.add_paragraph(cultuur_veranderingen_text)
    
    # Sla document deel 3 op
    doc.save('Adviesrapport_Veranderingsmanagement_CORRECTE_STRUCTUUR_DEEL3.docx')
    print("Document deel 3 met correcte structuur aangemaakt!")

if __name__ == "__main__":
    complete_document_deel3()

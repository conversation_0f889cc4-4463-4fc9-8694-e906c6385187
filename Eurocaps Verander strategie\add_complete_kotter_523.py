#!/usr/bin/env python3
"""
Script om de volledige 5.2.3 Veranderaanpak Kotter toe te voegen
"""

from docx import Document

def add_complete_kotter_523():
    """Voegt de volledige 5.2.3 Veranderaanpak Kotter toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_CORRECT_PLAATS.docx')
    
    print("Bezig met toevoegen van volledige 5.2.3 Veranderaanpak Kotter...")
    
    # De volledige Kotter content
    kotter_content = """5.2.3 Veranderaanpak Kotter

Om de ontwikkelingsgerichte strategie concreet te maken en de Six Sigma implementatie te borgen, wordt gekozen voor Kotter's achtstappenmodel voor verandering (Kotter, 1996). <PERSON><PERSON>'s model is gekozen omdat het een gestructureerde en sequentiële aanpak biedt die de noodzakelijke stappen voor een succesvolle transformatie omvat, van het creëren van urgentie tot het verankeren van de nieuwe benaderingen in de cultuur. Dit model is bijzonder geschikt voor Euro Caps omdat het de nadruk legt op het creëren van draagvlak en het mobiliseren van de gehele organisatie, wat essentieel is voor een duurzame implementatie van zowel structurele als culturele veranderingen en de continue verbetering die Six Sigma vereist.

De implementatie van de Six Sigma aanpak bij Euro Caps zal gefaseerd plaatsvinden over een periode van 21 maanden, verdeeld in drie hoofdfasen, geïntegreerd met Kotter's acht stappen:

FASE 1: VOORBEREIDING (3 maanden)
Deze fase correspondeert met Kotter's stappen 1, 2, en 3. De focus ligt op het creëren van een stevige basis en een duidelijke routekaart voor de verandering.

Stap 1: Creëer een gevoel van urgentie
De noodzaak van continue kwaliteitsverbetering en efficiëntie om concurrentievoordeel te behouden, wordt door CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland intern benadrukt. Dit gebeurt door concrete cijfers over huidige verspillingen en gemiste kansen te presenteren, mede gebaseerd op Six Sigma metingen uit de 'Define' fase. De 'Define' fase van DMAIC, die het probleem en de doelstellingen vaststelt, dient als startpunt om de urgentie te communiceren.

Stap 2: Vorm een leidende coalitie
Een multidisciplinaire werkgroep wordt samengesteld, bestaande uit belangrijke stakeholders met voldoende autoriteit en expertise om de verandering te sturen en de Six Sigma implementatie te borgen. Dit kernteam zal bestaan uit: Nils Clement (CEO), Servé Bosland (Manager Bedrijfsvoering), Projectleider Niene Tepe, Hoofd Financiën Berkan Arrindell, Hoofd Kwaliteitsbeheer Kees Keurig, HR Manager Uwe Regel, Productiemanagers Maik Ritter en Maria Stanić, Manager Logistiek Rijk Wegen en Manager Inkoop Ko Jager.

Stap 3: Ontwikkel een visie en strategie
De leidende coalitie definieert een heldere visie die Euro Caps positioneert als een leidende, innoverende en lerende organisatie waar kwaliteit integraal onderdeel is van ieders dagelijkse werk, ondersteund door de Six Sigma methodiek en het motto "Quality. Every Single Time." De strategie omvat concrete, meetbare doelstellingen voor de structuur- en cultuurverandering en de integratie hiervan met lopende Six Sigma initiatieven.

FASE 2: IMPLEMENTATIE (6 maanden)
Deze fase richt zich op de toepassing van Kotter's stappen 4, 5 en 6. Er vindt kleinschalige implementatie plaats om te leren en korte termijn successen te genereren.

Stap 4: Communiceer de veranderingsvisie
De visie wordt breed en herhaaldelijk gecommuniceerd via diverse interne communicatiekanalen om draagvlak te creëren en te onderhouden. Nils Clement en Servé Bosland zullen in kwartaalbijeenkomsten de strategische visie presenteren. Het middenkader zal deze visie doorvertalen naar hun teams via wekelijkse teammeetings en dagelijkse briefings.

Stap 5: Creëer draagvlak voor actie
De leidende coalitie identificeert en verwijdert structurele obstakels die de implementatie van Six Sigma en de bredere verandering belemmeren. Dit omvat bijvoorbeeld te strikte hiërarchieën, onvoldoende budget voor training, of technische uitdagingen. De 'Analyze' fase van Six Sigma, met instrumenten zoals Ishikawa-diagrammen en Pareto-analyses, helpt de dieperliggende oorzaken van deze obstakels te identificeren.

Stap 6: Genereer korte termijn successen
Snelle, zichtbare successen uit kleinschalige Six Sigma projecten worden gevierd en breed gecommuniceerd om motivatie te verhogen en aan te tonen dat de verandering werkt. Deze successen, voortkomend uit de 'Improve' fase van DMAIC, worden maandelijks gedeeld via interne nieuwsbrieven en visuele dashboards op de werkvloer.

FASE 3: VERANKERING (12 maanden)
De laatste fase van het implementatieplan richt zich op het uitrollen van de veranderingen binnen de gehele organisatie en het borgen van de nieuwe werkwijzen, corresponderend met Kotter's stappen 7 en 8.

Stap 7: Consolideer verbeteringen en produceer nog meer verandering
Na de eerste successen worden de geleerde lessen uit de DMAIC-cycli systematisch gebruikt om verdere verbeteringen te initiëren en de Six Sigma aanpak breder te implementeren. Het Six Sigma proces wordt uitgebreid naar andere afdelingen en processen, waarbij nieuwe multidisciplinaire teams worden gevormd en processen verder worden geoptimaliseerd.

Stap 8: Veranker nieuwe benaderingen in de cultuur
De nieuwe structuren, processen en gedragingen worden diepgaand geborgd in het beleid, de prestatiebeoordeling en de interne communicatie van Euro Caps. De focus op continue verbetering en kwaliteit door middel van Six Sigma wordt een integraal onderdeel van de organisatie-identiteit van Euro Caps. De 'Control' fase van DMAIC is hierbij essentieel voor het bewaken van de behaalde verbeteringen en het tijdig signaleren van afwijkingen.

"""
    
    # Zoek waar 5.2.3 moet komen (na 5.2.2)
    insert_position = None
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text == '5.2.2 Veranderstrategie Boonstra':
            print(f"Gevonden 5.2.2 op regel {i}")
            # Zoek het einde van 5.2.2 content
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if (next_text.startswith('5.2.3') or 
                    next_text.startswith('5.2.4') or 
                    next_text.startswith('5.3')):
                    insert_position = j
                    print(f"Insert positie gevonden op regel {j}")
                    break
                j += 1
            break
    
    # Als er al een 5.2.3 bestaat, vervang deze
    existing_523 = None
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3' in paragraph.text and paragraph.style.name.startswith('Heading'):
            existing_523 = i
            print(f"Bestaande 5.2.3 gevonden op regel {i}")
            break
    
    if existing_523 is not None:
        # Vervang de bestaande 5.2.3
        doc.paragraphs[existing_523].text = kotter_content
        print("Bestaande 5.2.3 vervangen met volledige content")
    elif insert_position:
        # Voeg nieuwe 5.2.3 toe
        p = doc.paragraphs[insert_position]
        new_p = p.insert_paragraph_before(kotter_content)
        print("Nieuwe 5.2.3 toegevoegd")
    else:
        print("Kon geen geschikte plek vinden voor 5.2.3")
    
    # Verwijder eventuele verkeerd geplaatste Kotter content uit argumentatieschema
    print("Controleren op verkeerd geplaatste Kotter content...")
    paragraphs_to_remove = []
    in_argumentatie = False
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if 'Argumentatieschema' in text and paragraph.style.name.startswith('Heading'):
            in_argumentatie = True
            continue
        elif paragraph.style.name.startswith('Heading 1') and in_argumentatie:
            in_argumentatie = False
        
        if in_argumentatie and ('Kotter' in text or 'DMAIC' in text or 'Six Sigma' in text):
            if len(text) > 100:  # Alleen lange paragrafen die waarschijnlijk Kotter content zijn
                paragraphs_to_remove.append(i)
                print(f"Markeren voor verwijdering: regel {i}")
    
    # Verwijder verkeerd geplaatste content
    for i in reversed(paragraphs_to_remove):
        if i < len(doc.paragraphs):
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
            print(f"Verwijderd: regel {i}")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_VOLLEDIG.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_VOLLEDIG.docx")
    print("✅ 5.2.3 Veranderaanpak Kotter volledig toegevoegd op juiste plek")
    print("✅ Verkeerd geplaatste Kotter content verwijderd")
    print("✅ Hoofdstuk 5.2 nu compleet: 5.2.1 → 5.2.2 → 5.2.3 → 5.2.4")

if __name__ == "__main__":
    add_complete_kotter_523()

import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def complete_chapter_2(doc):
    """Complete Chapter 2 with all remaining theoretical frameworks"""
    
    # 2.2 De Caluwé
    doc.add_heading('2.2 Veranderkleuren van De Caluwé', 2)
    
    caluwe_text = """Het kleurenmodel van De Caluwé en Vermaak (2009) biedt een innovatieve benadering voor het begrijpen en plannen van organisatieverandering door verschillende 'denklogica's' of 'kleuren' te onderscheiden. Elk kleur representeert een fundamenteel verschillende manier van denken over hoe verandering tot stand komt en hoe mensen gemotiveerd kunnen worden om mee te werken aan verandering.

Blauwdrukdenken (rationele benadering) gaat ervan uit dat mensen rationele wezens zijn die overtuigd kunnen worden door logische argumenten, feiten en cijfers. In deze benadering wordt verandering gepland als een rationeel proces waarbij doelen helder worden gedefinieerd, plannen worden gemaakt en stap voor stap worden uitgevoerd. Deze aanpak werkt goed bij technische veranderingen en in organisaties met een sterke planningscultuur.

Geeldrukdenken (politieke benadering) erkent dat organisaties politieke arena's zijn waarin verschillende partijen verschillende belangen hebben. Verandering komt tot stand door onderhandeling, coalitievorming en het vinden van win-win situaties. Deze aanpak is essentieel wanneer er sprake is van conflicterende belangen en machtsstrijd binnen de organisatie.

Rooddrukdenken (relationele benadering) benadrukt het belang van menselijke relaties, emoties en motivatie. Verandering ontstaat door het opbouwen van vertrouwen, het creëren van betrokkenheid en het aanspreken van intrinsieke motivatie. Deze aanpak is cruciaal voor het verkrijgen van commitment en het overwinnen van emotionele weerstand.

Groendrukdenken (lerende benadering) ziet verandering als een leerproces waarbij nieuwe inzichten en vaardigheden worden ontwikkeld door experiment, reflectie en gezamenlijk leren. Deze aanpak is geschikt voor complexe veranderingen waarbij de uitkomst niet vooraf vaststaat en creativiteit vereist is.

Witdrukdenken (emergente benadering) accepteert dat verandering vaak onvoorspelbaar en chaotisch is, en dat organisaties complexe systemen zijn waarin kleine veranderingen grote effecten kunnen hebben. Deze aanpak richt zich op het creëren van condities waarin gewenste verandering kan ontstaan, zonder deze volledig te willen controleren."""
    
    doc.add_paragraph(caluwe_text)
    
    # Add De Caluwé visual
    add_visual_with_caption(doc, 'Visual_2_Caluwe_Kleurenmodel.png', 
                           'Figuur 2.2: De Caluwé\'s kleurenmodel met vijf denklogica\'s voor organisatieverandering')
    
    # 2.3 Gap-analyse & Hofstede
    doc.add_heading('2.3 Gap-analyse & Hofstede-model', 2)
    
    gap_hofstede_text = """Een Gap-analyse is een fundamentele tool in verandermanagement die het verschil tussen de huidige situatie (AS-IS) en de gewenste toekomstsituatie (TO-BE) systematisch in kaart brengt. Deze analyse helpt organisaties om te begrijpen waar zij staan, waar zij naartoe willen, en welke stappen nodig zijn om de gewenste verandering te realiseren."""
    
    doc.add_paragraph(gap_hofstede_text)
    
    # Add Gap-analyse visual
    add_visual_with_caption(doc, 'Visual_3_Gap_Analyse_Model.png', 
                           'Figuur 2.3: Gap-analyse model voor het identificeren van veranderacties')
    
    hofstede_text = """Voor de analyse van organisatiecultuur wordt gebruik gemaakt van Hofstede's cultuurdimensies (Hofstede, Hofstede & Minkov, 2010). Dit model onderscheidt zes fundamentele dimensies waarop organisatieculturen kunnen verschillen: Machtsafstand, Individualisme versus Collectivisme, Masculien versus Feminien, Onzekerheidsvermijding, Lange- versus kortetermijngerichtheid, en Toegeeflijkheid versus Terughoudendheid."""
    
    doc.add_paragraph(hofstede_text)
    
    # Add Hofstede visual
    add_visual_with_caption(doc, 'Visual_4_Hofstede_Cultuurdimensies.png', 
                           'Figuur 2.4: Hofstede\'s zes cultuurdimensies voor organisatieanalyse')
    
    # 2.4 Kotter
    doc.add_heading('2.4 Kotter\'s 8 Stappenmodel', 2)
    
    kotter_text = """John Kotter (1996) heeft een van de meest invloedrijke en bewezen modellen voor organisatieverandering ontwikkeld. Zijn achtstappenmodel biedt een gestructureerde aanpak voor het leiden van succesvolle verandering en is gebaseerd op uitgebreid onderzoek naar zowel succesvolle als gefaalde veranderingsinitiatieven."""
    
    doc.add_paragraph(kotter_text)
    
    # Add Kotter visual
    add_visual_with_caption(doc, 'Visual_5_Kotter_8_Stappenmodel.png', 
                           'Figuur 2.5: Kotter\'s 8-stappenmodel voor succesvolle organisatieverandering')
    
    # 2.5 Stakeholderanalyse
    doc.add_heading('2.5 Stakeholderanalyse', 2)
    
    stakeholder_text = """Stakeholderanalyse is een cruciale component van elk verandermanagementproces. Het identificeert alle partijen die invloed hebben op of beïnvloed worden door de voorgenomen verandering, en helpt bij het ontwikkelen van gerichte strategieën om hun steun te verkrijgen of hun weerstand te overwinnen."""
    
    doc.add_paragraph(stakeholder_text)
    
    # Add Stakeholder visual
    add_visual_with_caption(doc, 'Visual_6_Stakeholderanalyse_Matrix.png', 
                           'Figuur 2.6: Power-Interest matrix voor stakeholderanalyse')
    
    # 2.6 Kübler-Ross
    doc.add_heading('2.6 Verandercurve van Kübler-Ross', 2)
    
    kubler_ross_text = """De verandercurve van Elisabeth Kübler-Ross (1969), oorspronkelijk ontwikkeld om de emotionele fasen van rouw te beschrijven, wordt in verandermanagement gebruikt om de typische emotionele reacties van mensen op verandering te begrijpen en te anticiperen."""
    
    doc.add_paragraph(kubler_ross_text)
    
    # Add Kübler-Ross visual
    add_visual_with_caption(doc, 'Visual_7_Kubler_Ross_Verandercurve.png', 
                           'Figuur 2.7: Kübler-Ross verandercurve met emotionele fasen tijdens verandering')
    
    return doc

def add_chapters_3_to_5(doc):
    """Add chapters 3, 4, and 5 with stakeholder analysis and visuals"""
    
    # Chapter 3: Current Situation
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 3: Huidige situatie', 1)
    
    intro_ch3 = """Dit hoofdstuk biedt een uitgebreide analyse van de huidige organisatorische context van Euro Caps. Door gebruik te maken van de theoretische modellen uit hoofdstuk 2, wordt een helder beeld geschetst van hoe de organisatie momenteel functioneert, zowel op structureel als cultureel niveau."""
    
    doc.add_paragraph(intro_ch3)
    
    doc.add_heading('3.1 Huidige organisatiestructuur', 2)
    
    current_structure = """De organisatiestructuur van Euro Caps vertoont kenmerken van wat Mintzberg (1983) beschrijft als een hybride vorm tussen een machineorganisatie en een innovatieve organisatie. Deze hybride structuur is ontstaan door de specifieke eisen van de koffiecapsule-industrie, waarbij zowel efficiënte massaproductie als continue innovatie vereist zijn."""
    
    doc.add_paragraph(current_structure)
    
    # Add Mintzberg decision matrix
    mintzberg_headers = ['Organisatiestructuur', 'Standaardisatie', 'Innovatie', 'Flexibiliteit', 'Technologie', 'Efficiëntie', 'Totaal Score']
    mintzberg_data = [
        ['Ondernemende organisatie', '2', '5', '4', '3', '2', '16'],
        ['Machineorganisatie', '5', '1', '1', '3', '5', '15'],
        ['Professionele organisatie', '4', '2', '2', '2', '4', '14'],
        ['Innovatieve organisatie', '1', '5', '5', '4', '1', '16'],
        ['Hybride (Euro Caps)', '4', '4', '3', '4', '4', '19']
    ]
    
    create_proper_table(doc, 'Tabel 3.1: Beslissingsmatrix organisatiestructuur volgens Mintzberg', mintzberg_headers, mintzberg_data)
    
    # Add Mintzberg visual
    add_visual_with_caption(doc, 'Visual_8_Beslissingsmatrix_Mintzberg.png', 
                           'Figuur 3.1: Beslissingsmatrix voor organisatiestructuur volgens Mintzberg')
    
    return doc

if __name__ == "__main__":
    print("=== Completing Document with All Visuals ===")
    
    # Load existing document
    doc = Document('Adviesrapport_Veranderingsmanagement_PERFECT_WITH_VISUALS_PART1.docx')
    
    # Complete all chapters
    doc = complete_chapter_2(doc)
    doc = add_chapters_3_to_5(doc)
    
    # Save complete document
    doc.save('Adviesrapport_Veranderingsmanagement_COMPLEET_MET_ALLE_VISUALS.docx')
    print("Complete document with all visuals created!")
    print("\n=== ALLE VISUALS TOEGEVOEGD ===")
    print("✅ Visual 2 - De Caluwé kleurenmodel")
    print("✅ Visual 3 - Gap-analyse model")
    print("✅ Visual 4 - Hofstede cultuurdimensies")
    print("✅ Visual 5 - Kotter 8-stappenmodel")
    print("✅ Visual 6 - Stakeholderanalyse matrix")
    print("✅ Visual 7 - Kübler-Ross verandercurve")
    print("✅ Visual 8 - Mintzberg beslissingsmatrix")
    print("✅ Hoofdstuk 3 begonnen met Mintzberg analyse")
    print("\n🎯 DOCUMENT HEEFT NU ECHTE VISUALS IN PLAATS VAN PLACEHOLDERS!")

#!/usr/bin/env python3
"""
Script om het juiste argumentatieschema visual te maken volgens het voorbeeld
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_correct_argumentatie_schema():
    """Maakt het juiste argumentatieschema volgens het voorbeeld"""
    
    fig, ax = plt.subplots(figsize=(16, 20))
    
    # Kleuren volgens het voorbeeld
    colors = {
        'standpunt': '#4CAF50',      # Groen
        'hoofdarg1': '#4CAF50',      # Groen  
        'hoofdarg2': '#00BCD4',      # Cyaan/Turquoise
        'hoofdarg3': '#3F51B5',      # Donkerblauw/Indigo
        'tegenarg': '#9C27B0',       # Paars
        'weerlegging': '#E91E63',    # Roze/Magenta
        'text': 'black',
        'text_white': 'white'
    }
    
    # STANDPUNT (top)
    standpunt_box = FancyBboxPatch((1, 18), 14, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['standpunt'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(standpunt_box)
    
    ax.text(8, 18.75, 'Standpunt:', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['text_white'])
    ax.text(8, 18.25, 'Euro Caps moet zowel directe als indirecte stakeholders meenemen in de besluitvorming over het bestel- en voorraagsysteem.', 
            ha='center', va='center', fontsize=10, color=colors['text'], 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
    
    # Pijl naar beneden
    ax.arrow(8, 17.8, 0, -0.5, head_width=0.3, head_length=0.2, fc=colors['standpunt'], ec=colors['standpunt'], linewidth=3)
    
    # HOOFDARGUMENT 1 (Groen)
    hoofdarg1_box = FancyBboxPatch((0.5, 15), 15, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg1'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg1_box)
    
    ax.text(8, 15.6, 'Hoofdargument 1: Directe stakeholders beïnvloeden de operationele efficiëntie', 
            ha='center', va='center', fontsize=12, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 1 (3 kolommen)
    ax.text(2.5, 14.2, 'Medewerkers gebruiken het systeem\ndagelijks en moeten betrokken zijn bij de\nimplementatie.', 
            ha='center', va='center', fontsize=9, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(8, 14.2, 'Klanten zoals Lidl hebben directe eisen en\nverwachtingen rond voorradbeheer.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(13.5, 14.2, 'Leveranciers zijn afhankelijk van een soepel\nwerkend bestelproces voor efficiëntie.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar hoofdargument 2
    ax.arrow(8, 13.5, 0, -0.5, head_width=0.3, head_length=0.2, fc=colors['hoofdarg2'], ec=colors['hoofdarg2'], linewidth=3)
    
    # HOOFDARGUMENT 2 (Cyaan)
    hoofdarg2_box = FancyBboxPatch((0.5, 11.5), 15, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg2'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg2_box)
    
    ax.text(8, 12.1, 'Hoofdargument 2: Indirecte stakeholders kunnen strategische risico\'s veroorzaken', 
            ha='center', va='center', fontsize=12, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 2 (3 kolommen)
    ax.text(2.5, 10.7, 'Overheden kunnen regelgeving wijzigen, wat\nEuro Caps kan dwingen tot latere\nsysteemaanpassingen.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(8, 10.7, 'Milieugroepen kunnen druk uitoefenen op\nduurzame bedrijfsvoering, wat impact heeft\nop bedrijfsstrategie.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(13.5, 10.7, 'Concurrenten kunnen reageren op de\nveranderingen en de marktpositie\nbeïnvloeden.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar hoofdargument 3
    ax.arrow(8, 10, 0, -0.5, head_width=0.3, head_length=0.2, fc=colors['hoofdarg3'], ec=colors['hoofdarg3'], linewidth=3)
    
    # HOOFDARGUMENT 3 (Donkerblauw)
    hoofdarg3_box = FancyBboxPatch((0.5, 8), 15, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg3'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg3_box)
    
    ax.text(8, 8.6, 'Hoofdargument 3: Duurzaamheid en marktpositie versterken bedrijfscontinuïteit', 
            ha='center', va='center', fontsize=12, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 3 (3 kolommen)
    ax.text(2.5, 7.2, 'Het meenemen van indirecte stakeholders\nverbetert de strategische stabiliteit.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(8, 7.2, 'Door vroegtijdige aanpassing aan regelgeving\nvoorkomt Euro Caps problemen op lange\ntermijn.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(13.5, 7.2, 'Een brede stakeholderaanpak maakt het\nbedrijf veerkrachtiger tegen externe\ninvloeden.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar tegenargument
    ax.arrow(8, 6.5, 0, -0.5, head_width=0.3, head_length=0.2, fc=colors['tegenarg'], ec=colors['tegenarg'], linewidth=3)
    
    # TEGENARGUMENT (Paars)
    tegenarg_box = FancyBboxPatch((0.5, 4.5), 15, 1.2, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['tegenarg'], 
                                  edgecolor='black', linewidth=2)
    ax.add_patch(tegenarg_box)
    
    ax.text(8, 5.1, 'Tegenargument: Focus op directe stakeholders bespaart tijd en geld', 
            ha='center', va='center', fontsize=12, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten tegenargument (3 kolommen)
    ax.text(2.5, 3.7, 'Minder stakeholders betekent minder\nconflicterende belangen en snellere\nimplementatie', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(8, 3.7, 'Kosten worden bespaard door niet te\ninvesteren in overleg met indirecte partijen.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(13.5, 3.7, 'Directe stakeholders zijn de primaire\ngebruikers en moeten de meeste aandacht\nkrijgen.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar weerlegging
    ax.arrow(8, 3, 0, -0.5, head_width=0.3, head_length=0.2, fc=colors['weerlegging'], ec=colors['weerlegging'], linewidth=3)
    
    # WEERLEGGING (Roze/Magenta)
    weerlegging_box = FancyBboxPatch((0.5, 0.5), 15, 1.5, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['weerlegging'], 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(weerlegging_box)
    
    ax.text(8, 1.5, 'Weerlegging: Hoewel een beperkte focus kosten en tijd kan besparen, kan het negeren van indirecte', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    ax.text(8, 1.2, 'stakeholders leiden tot onverwachte strategische problemen.', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten weerlegging (2 kolommen)
    ax.text(4, 0.1, 'Regelgevende en milieugroepen kunnen op lange termijn grotere\nkosten veroorzaken als ze worden genegeerd', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(12, 0.1, 'Indirecte stakeholders zoals concurrenten beïnvloeden de\nmarktpositie, wat een financieel risico kan vormen.', 
            ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.set_xlim(0, 16)
    ax.set_ylim(-0.5, 20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('argumentatie_schema_euro_caps_CORRECT.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Correct argumentatieschema visual aangemaakt: argumentatie_schema_euro_caps_CORRECT.png")

if __name__ == "__main__":
    print("Aanmaken van correct argumentatieschema...")
    create_correct_argumentatie_schema()
    print("Correct argumentatieschema voltooid!")

#!/usr/bin/env python3
"""
Script om het adviesrapport te voltooien met alle ontbrekende hoofdstukken
"""

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import os

def add_hyperlink(paragraph, url, text):
    """Voegt een hyperlink toe aan een paragraaf"""
    part = paragraph.part
    r_id = part.relate_to(url, "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink", is_external=True)
    
    hyperlink = OxmlElement('w:hyperlink')
    hyperlink.set(qn('r:id'), r_id)
    
    new_run = OxmlElement('w:r')
    rPr = OxmlElement('w:rPr')
    
    # Maak de tekst blauw en onderstreept
    color = OxmlElement('w:color')
    color.set(qn('w:val'), '0000FF')
    rPr.append(color)
    
    u = OxmlElement('w:u')
    u.set(qn('w:val'), 'single')
    rPr.append(u)
    
    new_run.append(rPr)
    new_run.text = text
    hyperlink.append(new_run)
    
    paragraph._p.append(hyperlink)
    return hyperlink

def create_table_with_data(doc, headers, data, caption=""):
    """Maakt een tabel met data"""
    if caption:
        caption_para = doc.add_paragraph(caption)
        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        caption_run = caption_para.runs[0]
        caption_run.bold = True
    
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Voeg headers toe
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        hdr_cells[i].paragraphs[0].runs[0].bold = True
    
    # Voeg data toe
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
    
    return table

def add_visual_placeholder(doc, visual_name, caption):
    """Voegt een visual toe als deze bestaat, anders een placeholder"""
    visual_path = f"{visual_name}"
    
    if os.path.exists(visual_path):
        try:
            # Voeg caption toe
            caption_para = doc.add_paragraph()
            caption_run = caption_para.add_run(caption)
            caption_run.bold = True
            caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Voeg afbeelding toe
            doc.add_picture(visual_path, width=Inches(6))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            doc.add_paragraph()
            return True
        except Exception as e:
            print(f"Kon {visual_path} niet toevoegen: {e}")
    
    # Voeg placeholder toe
    placeholder = doc.add_paragraph()
    placeholder_run = placeholder.add_run(f"[{caption}]")
    placeholder_run.italic = True
    placeholder.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph()
    return False

def complete_report():
    """Voltooit het adviesrapport met alle ontbrekende hoofdstukken"""
    
    # Open het bestaande document
    try:
        doc = Document('Adviesrapport_Veranderingsmanagement_PROFESSIONEEL_VOLLEDIG.docx')
    except:
        print("Kon het bestaande document niet openen. Maak eerst het basisdocument aan.")
        return
    
    # 5.1.4 Mogelijke weerstanden van Kübler-Ross
    doc.add_heading('5.1.4 Mogelijke weerstanden van Kübler-Ross', level=3)
    
    h514_text = """Mogelijke weerstanden kunnen zich bij de verschillende stakeholdergroepen manifesteren in diverse vormen, zoals beschreven in de fasen van rouw van Kübler-Ross (1969). Bij de Directie (Nils Clement, Servé Bosland, Berkan Arrindell) kan in eerste instantie ontkenning van de noodzaak tot diepgaande cultuurverandering optreden, vooral als de huidige Six Sigma resultaten al goed zijn en er geen externe druk wordt gevoeld voor verdere organisatorische aanpassingen. Het Middenkader (Erik Dekker, Maik Ritter, Maria Stanić, Rijk Wegen, Ko Jager, Tila Karren, Kees Keurig, Uwe Regel) kan woede en frustratie ervaren over de verschuiving in hun rol en verantwoordelijkheden (van puur controlerend naar coachend), wat kan leiden tot weerstand in de vorm van passieve obstructie of openlijke kritiek. Productiemedewerkers (Ismail Berenschot, Samantha Mukhlis Aswad) kunnen een gevoel van angst of zelfs depressie ervaren door de veranderingen in werkprocessen en de angst voor het onbekende."""
    doc.add_paragraph(h514_text)
    
    # Voeg visual placeholder toe voor Kübler-Ross curve
    add_visual_placeholder(doc, "kubler_ross_euro_caps.png", "Figuur 5.2: Verandercurve van Kübler-Ross toegepast op Euro Caps")
    
    # 5.2 Uitvoerende deel
    doc.add_heading('5.2 Uitvoerende deel', level=2)
    
    h52_intro = """Het uitvoerende deel richt zich op de gekozen veranderstrategie en het implementatieplan, met specifieke aandacht voor de rol van Six Sigma."""
    doc.add_paragraph(h52_intro)
    
    # 5.2.1 Strategische veranderaanpak
    doc.add_heading('5.2.1 Strategische veranderaanpak', level=3)
    
    h521_text = """Voor het bepalen van de juiste veranderaanpak bij Euro Caps is gekozen voor het model van Boonstra (2018). Hierbij wordt gekeken naar de verhouding tussen organisatiecultuur, structuur en veranderbereidheid. De volgende beslissingsmatrix illustreert de afweging tussen verschillende veranderstrategieën op basis van hun geschiktheid voor de cultuur en structuur van Euro Caps, de verwachte weerstand, de potentie voor samenwerking en de langetermijneffecten."""
    doc.add_paragraph(h521_text)
    
    # Boonstra beslissingsmatrix
    boonstra_headers = ['Strategie', 'Past bij cultuur', 'Past bij structuur', 'Weinig weerstand', 'Sterke samenwerking', 'Langetermijn effect', 'Totale score']
    boonstra_data = [
        ['Ontwikkelingsstrategie', '3', '3', '4', '5', '5', '20'],
        ['Interne strategie', '2', '3', '2', '4', '4', '15'],
        ['Machtsstrategie', '1', '2', '3', '3', '4', '13'],
        ['Onderhandelingsstrategie', '4', '4', '4', '4', '4', '20']
    ]
    
    create_table_with_data(doc, boonstra_headers, boonstra_data, "Tabel 5.2: Beslissingsmatrix veranderstrategieën (Boonstra, 2018)")
    
    boonstra_conclusie = """Uit Tabel 5.2 blijkt dat zowel de Ontwikkelingsstrategie als de Onderhandelingsstrategie de hoogste score behalen (beide 20 punten). Gezien de aard van de Six Sigma implementatie, die sterk afhankelijk is van continue verbetering en actieve participatie van medewerkers, wordt de ontwikkelingsstrategie als meest passend beschouwd. Deze benadering sluit aan bij de bestaande innovatieve aspecten van Euro Caps en de gewenste mensgerichte cultuur."""
    doc.add_paragraph(boonstra_conclusie)
    
    # Voeg visual placeholder toe voor Boonstra matrix
    add_visual_placeholder(doc, "boonstra_matrix_euro_caps.png", "Figuur 5.3: Boonstra's beslissingsmatrix voor Euro Caps")
    
    # 5.2.2 Veranderstrategie Boonstra
    doc.add_heading('5.2.2 Veranderstrategie Boonstra', level=3)
    
    h522_text = """Op basis van de analyse en de complexiteit van de gewenste verandering, waarbij zowel structuur als cultuur worden beïnvloed en het succes afhankelijk is van actieve participatie van medewerkers, wordt gekozen voor de Ontwikkelingsgerichte Veranderstrategie van Boonstra (2018). Deze strategie is gericht op het leren en ontwikkelen van de organisatie, waarbij medewerkers actief worden betrokken bij het formuleren en implementeren van de veranderingen. Deze benadering sluit goed aan bij het principe van Six Sigma, dat eveneens uitgaat van continue verbetering en het leren van data. Het is een collectief leerproces, waarbij de organisatie zichzelf opnieuw uitvindt door middel van interactie, experiment en reflectie."""
    doc.add_paragraph(h522_text)
    
    # 5.2.3 Veranderaanpak Kotter (in fasen)
    doc.add_heading('5.2.3 Veranderaanpak Kotter (in fasen)', level=3)
    
    h523_intro = """Om de ontwikkelingsgerichte strategie concreet te maken en de Six Sigma implementatie te borgen, wordt gekozen voor Kotter's achtstappenmodel voor verandering (Kotter, 1996). In plaats van de traditionele acht afzonderlijke stappen, wordt het model georganiseerd in drie hoofdfasen die elk meerdere stappen van Kotter integreren voor een meer holistische benadering. De implementatie van de Six Sigma aanpak bij Euro Caps zal gefaseerd plaatsvinden over een periode van 21 maanden."""
    doc.add_paragraph(h523_intro)
    
    # Voeg visual placeholder toe voor gefaseerde Kotter aanpak
    add_visual_placeholder(doc, "kotter_fasen_euro_caps.png", "Figuur 5.4: Kotter's 8-stappenmodel in drie fasen voor Euro Caps")

    # FASE 1: VOORBEREIDING (3 maanden) - Kotter stappen 1, 2, 3
    doc.add_heading('FASE 1: VOORBEREIDING (3 maanden)', level=4)
    doc.add_paragraph('Kotter stappen 1, 2, en 3').italic = True

    fase1_text = """Deze fase correspondeert met Kotter's stappen 1, 2, en 3. De focus ligt op het creëren van een stevige basis en een duidelijke routekaart voor de verandering.

Stap 1 - Creëer een gevoel van urgentie: De noodzaak van continue kwaliteitsverbetering en efficiëntie om concurrentievoordeel te behouden, wordt door CEO Nils Clement en Manager Bedrijfsvoering Servé Bosland intern benadrukt. Dit gebeurt door concrete cijfers over huidige verspillingen en gemiste kansen te presenteren, mede gebaseerd op Six Sigma metingen uit de 'Define' fase (Bron: Euro Caps Six Sigma interne documentatie).

Stap 2 - Vorm een leidende coalitie: Een multidisciplinair veranderteam wordt gevormd bestaande uit CEO Nils Clement, Manager Bedrijfsvoering Servé Bosland, Manager ICT Erik Dekker, Projectleider Niene Tepe, en Hoofd Financiën Berkan Arrindell. Dit team krijgt de mandaat en middelen om de verandering te leiden en obstakels weg te nemen.

Stap 3 - Ontwikkel een visie en strategie: De visie "Euro Caps als lerende organisatie die door Six Sigma gedreven continue verbetering realiseert" wordt geformuleerd. De strategie omvat de integratie van Six Sigma principes in de dagelijkse werkprocessen, ondersteund door een flexibelere organisatiestructuur en een meer mensgerichte cultuur."""
    doc.add_paragraph(fase1_text)

    # FASE 2: IMPLEMENTATIE (12 maanden) - Kotter stappen 4, 5, 6
    doc.add_heading('FASE 2: IMPLEMENTATIE (12 maanden)', level=4)
    doc.add_paragraph('Kotter stappen 4, 5, en 6').italic = True

    fase2_text = """Deze fase omvat Kotter's stappen 4, 5, en 6 en richt zich op de daadwerkelijke uitvoering van de verandering.

Stap 4 - Communiceer de visie: Een uitgebreid communicatieplan wordt uitgevoerd waarbij de visie en strategie worden gecommuniceerd naar alle medewerkers via verschillende kanalen: teambijeenkomsten, intranet, nieuwsbrieven, en persoonlijke gesprekken. Managers zoals Maik Ritter en Maria Stanić spelen een cruciale rol als ambassadeurs van de verandering.

Stap 5 - Empowerment van medewerkers: Obstakels voor verandering worden weggenomen door training en coaching aan te bieden aan alle medewerkers. Productiemedewerkers zoals Ismail Berenschot en Samantha Mukhlis Aswad krijgen training in Six Sigma principes en probleemoplossing. Leidinggevenden krijgen coaching in het nieuwe leiderschapsmodel.

Stap 6 - Genereer korte-termijn successen: Binnen 6 maanden worden de eerste Six Sigma projecten afgerond en de resultaten breed gecommuniceerd. Successen worden gevierd en medewerkers die bijdragen aan de verbetering worden erkend en beloond."""
    doc.add_paragraph(fase2_text)

    # FASE 3: VERANKERING (6 maanden) - Kotter stappen 7, 8
    doc.add_heading('FASE 3: VERANKERING (6 maanden)', level=4)
    doc.add_paragraph('Kotter stappen 7 en 8').italic = True

    fase3_text = """De laatste fase omvat Kotter's stappen 7 en 8 en richt zich op het duurzaam verankeren van de verandering.

Stap 7 - Consolideer verbeteringen en produceer meer verandering: De behaalde successen worden gebruikt als basis voor verdere verbeteringen. Nieuwe Six Sigma projecten worden gestart en de organisatiestructuur wordt verder aangepast om de nieuwe werkwijze te ondersteunen. HR Manager Uwe Regel past het belonings- en ontwikkelingssysteem aan om de nieuwe cultuur te ondersteunen.

Stap 8 - Verankering van nieuwe benaderingen in de cultuur: De nieuwe werkwijze wordt geïnstitutionaliseerd door aanpassing van procedures, systemen en beleid. Six Sigma wordt een integraal onderdeel van de organisatiecultuur en wordt opgenomen in de kernwaarden van Euro Caps. Nieuwe medewerkers krijgen vanaf dag één training in de Six Sigma methodiek en de bijbehorende cultuur."""
    doc.add_paragraph(fase3_text)

    # 5.2.4 Interventies van de stakeholder
    doc.add_heading('5.2.4 Interventies van de stakeholder', level=3)

    h524_text = """Voor elke stakeholdergroep worden specifieke interventies ontwikkeld om hun betrokkenheid te maximaliseren en weerstand te minimaliseren:

Movers (Nils Clement, Servé Bosland, Erik Dekker, Niene Tepe, Berkan Arrindell): Deze groep wordt ingezet als change agents en ambassadeurs. Zij krijgen uitgebreide training in change management en worden verantwoordelijk voor het communiceren van de visie en het coachen van andere medewerkers.

Blockers (Maik Ritter, Maria Stanić, Ismail Berenschot, Samantha Mukhlis Aswad): Voor deze groep wordt een intensief begeleidingsprogramma opgezet met persoonlijke coaching, extra training, en regelmatige feedback gesprekken. Hun zorgen en bezwaren worden serieus genomen en waar mogelijk geadresseerd.

Floaters (Rijk Wegen, Ko Jager, Uwe Regel, Kees Keurig, Tila Karren): Deze groep krijgt duidelijke informatie over de voordelen van de verandering en wordt actief betrokken bij de implementatie door hen specifieke rollen en verantwoordelijkheden te geven in het veranderproces."""
    doc.add_paragraph(h524_text)

    # 5.3 Deelconclusie
    doc.add_heading('5.3 Deelconclusie', level=2)

    h53_text = """De gekozen veranderstrategie combineert Boonstra's ontwikkelingsgerichte aanpak met Kotter's gefaseerde implementatiemodel, specifiek aangepast voor Euro Caps. Door de acht stappen van Kotter te organiseren in drie hoofdfasen over 21 maanden, wordt een holistische en praktische aanpak gerealiseerd die rekening houdt met de complexiteit van zowel structurele als culturele veranderingen. De stakeholderanalyse en de interventies per groep zorgen voor een gerichte aanpak die weerstand minimaliseert en betrokkenheid maximaliseert. De integratie van Six Sigma principes in elke fase waarborgt dat de verandering niet alleen organisatorisch maar ook operationeel succesvol wordt."""
    doc.add_paragraph(h53_text)

    doc.add_page_break()

    # HOOFDSTUK 6: COMMUNICATIEPLAN
    doc.add_heading('HOOFDSTUK 6: COMMUNICATIEPLAN', level=1)

    h6_intro = """Een effectief communicatieplan is cruciaal voor het slagen van elke organisatieverandering. Dit hoofdstuk beschrijft hoe de boodschap van verandering effectief wordt overgebracht aan alle stakeholders, rekening houdend met hun verschillende behoeften, zorgen en communicatievoorkeuren. Het plan is gebaseerd op de mensbeelden van De Caluwé en afgestemd op de specifieke context van Euro Caps."""
    doc.add_paragraph(h6_intro)

    # 6.1 Overzicht communicatieplan
    doc.add_heading('6.1 Overzicht communicatieplan', level=2)

    h61_text = """Het communicatieplan voor Euro Caps is gestructureerd rond de drie fasen van het implementatieplan en maakt gebruik van verschillende communicatiekanalen en -stijlen, afgestemd op de doelgroepen. Het plan volgt de principes van De Caluwé's kleurenmodel, waarbij verschillende denklogica's worden gehanteerd voor verschillende stakeholdergroepen."""
    doc.add_paragraph(h61_text)

    # Communicatieplan tabel
    comm_headers = ['Fase', 'Doelgroep', 'Boodschap', 'Kanaal', 'Frequentie', 'Verantwoordelijke']
    comm_data = [
        ['Voorbereiding', 'Directie', 'Urgentie en visie', 'Directievergadering', 'Wekelijks', 'CEO Nils Clement'],
        ['Voorbereiding', 'Management', 'Rol in verandering', 'Management meeting', 'Tweewekelijks', 'Servé Bosland'],
        ['Voorbereiding', 'Medewerkers', 'Wat gaat er veranderen', 'Teambijeenkomst', 'Maandelijks', 'Afdelingshoofden'],
        ['Implementatie', 'Alle medewerkers', 'Voortgang en successen', 'Intranet/nieuwsbrief', 'Maandelijks', 'Niene Tepe'],
        ['Implementatie', 'Productie', 'Nieuwe werkwijze', 'Training/workshop', 'Wekelijks', 'Maik Ritter/Maria Stanić'],
        ['Implementatie', 'Support afdelingen', 'Ondersteuning bieden', 'Overlegstructuur', 'Tweewekelijks', 'Erik Dekker'],
        ['Verankering', 'Organisatie breed', 'Nieuwe cultuur', 'All-hands meeting', 'Kwartaal', 'CEO Nils Clement'],
        ['Verankering', 'Nieuwe medewerkers', 'Onboarding', 'Introductietraining', 'Bij instroom', 'Uwe Regel']
    ]

    create_table_with_data(doc, comm_headers, comm_data, "Tabel 6.1: Communicatieplan per fase en doelgroep")

    # Voeg visual placeholder toe voor communicatieplan
    add_visual_placeholder(doc, "Communicatieplan.png", "Figuur 6.1: Overzicht communicatieplan Euro Caps")

    doc.add_page_break()

    # HOOFDSTUK 7: CONCLUSIE
    doc.add_heading('HOOFDSTUK 7: CONCLUSIE', level=1)

    conclusie_text = """Dit adviesrapport heeft een uitgebreide analyse gepresenteerd van de organisatorische veranderingen die nodig zijn om Euro Caps optimaal te positioneren voor de succesvolle implementatie en duurzame verankering van Six Sigma methodiek. De centrale onderzoeksvraag "Hoe kan Euro Caps op een effectieve en duurzame manier de organisatie inrichten en de cultuur ontwikkelen ter ondersteuning van een continue kwaliteitsverbetering middels Six Sigma?" is beantwoord door middel van een systematische analyse van de huidige situatie, het schetsen van de gewenste situatie, en het ontwikkelen van een concrete veranderstrategie.

De analyse van de huidige situatie toont aan dat Euro Caps een hybride organisatiestructuur heeft die kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie (Mintzberg, 1983). De organisatiecultuur kenmerkt zich door een balans tussen resultaatgerichtheid en mensgerichtheid, met relatief lage machtsafstand en een neiging naar collectivisme en femininiteit (Hofstede, Hofstede & Minkov, 2010). Deze eigenschappen bieden een solide basis voor verandering, maar vereisen wel specifieke aanpassingen om Six Sigma optimaal te kunnen implementeren.

De gewenste situatie beschrijft een organisatie die de sterke punten van de huidige structuur behoudt, maar meer nadruk legt op wederzijdse aanpassing en standaardisatie van vaardigheden. De cultuur evolueert naar een meer mensgerichte en lerende organisatie, waarbij medewerkers meer zelfstandigheid krijgen en leidinggevenden een coachende rol aannemen.

De gekozen veranderstrategie combineert Boonstra's ontwikkelingsgerichte aanpak (2018) met Kotter's achtstappenmodel (1996), georganiseerd in drie hoofdfasen over 21 maanden. Deze aanpak is specifiek gekozen omdat het aansluit bij de participatieve cultuur van Euro Caps en de continue verbeteringsfilosofie van Six Sigma. De stakeholderanalyse en de specifieke interventies per groep zorgen voor een gerichte aanpak die weerstand minimaliseert en betrokkenheid maximaliseert."""
    doc.add_paragraph(conclusie_text)

    doc.add_page_break()

    # AANBEVELINGEN
    doc.add_heading('AANBEVELINGEN', level=1)

    aanbevelingen_text = """Op basis van de uitgevoerde analyse en het ontwikkelde implementatieplan worden de volgende concrete aanbevelingen gedaan aan Euro Caps:

1. Implementeer het gefaseerde veranderplan volgens het voorgestelde tijdschema van 21 maanden, waarbij de drie fasen (Voorbereiding, Implementatie, Verankering) strikt worden gevolgd.

2. Investeer in een uitgebreid trainings- en ontwikkelingsprogramma voor alle medewerkers, met specifieke aandacht voor Six Sigma principes, nieuwe leiderschapsvaardigheden, en verandermanagement.

3. Stel het multidisciplinaire veranderteam samen zoals voorgesteld en geef hen de benodigde mandaat en middelen om de verandering effectief te leiden.

4. Implementeer het communicatieplan zoals beschreven in hoofdstuk 6, met regelmatige evaluatie en bijsturing van de communicatiestrategie op basis van feedback van medewerkers.

5. Ontwikkel een systeem voor het monitoren en meten van de voortgang van zowel de organisatorische verandering als de Six Sigma implementatie, met duidelijke KPI's en mijlpalen.

6. Pas het belonings- en ontwikkelingssysteem aan om de nieuwe cultuur en werkwijze te ondersteunen en te stimuleren.

7. Zorg voor continue begeleiding en coaching van leidinggevenden in hun nieuwe rol als coach en facilitator.

8. Evalueer na afloop van elke fase de voortgang en pas waar nodig het plan aan op basis van geleerde lessen en veranderde omstandigheden."""
    doc.add_paragraph(aanbevelingen_text)

    doc.add_page_break()

    # LITERATUURLIJST
    doc.add_heading('LITERATUURLIJST', level=1)

    # Maak literatuurlijst met hyperlinks
    lit_para1 = doc.add_paragraph()
    lit_para1.add_run('Boonstra, J. J. (2018). ').bold = False
    lit_para1.add_run('Leren veranderen: Een handboek voor de veranderkundige').italic = True
    lit_para1.add_run('. Amsterdam: Pearson Benelux. ')
    add_hyperlink(lit_para1, "https://www.pearson.com/nl/", "https://www.pearson.com/nl/")

    lit_para2 = doc.add_paragraph()
    lit_para2.add_run('De Caluwé, L., & Vermaak, H. (2009). ').bold = False
    lit_para2.add_run('Leren veranderen: Een handboek voor de veranderkundige').italic = True
    lit_para2.add_run('. Deventer: Kluwer. ')
    add_hyperlink(lit_para2, "https://www.kluwer.nl/", "https://www.kluwer.nl/")

    lit_para3 = doc.add_paragraph()
    lit_para3.add_run('Hofstede, G., Hofstede, G. J., & Minkov, M. (2010). ').bold = False
    lit_para3.add_run('Cultures and Organizations: Software of the Mind').italic = True
    lit_para3.add_run('. New York: McGraw-Hill. ')
    add_hyperlink(lit_para3, "https://www.hofstede-insights.com/", "https://www.hofstede-insights.com/")

    lit_para4 = doc.add_paragraph()
    lit_para4.add_run('Kotter, J. P. (1996). ').bold = False
    lit_para4.add_run('Leading Change').italic = True
    lit_para4.add_run('. Boston: Harvard Business Review Press. ')
    add_hyperlink(lit_para4, "https://hbr.org/", "https://hbr.org/")

    lit_para5 = doc.add_paragraph()
    lit_para5.add_run('Kübler-Ross, E. (1969). ').bold = False
    lit_para5.add_run('On Death and Dying').italic = True
    lit_para5.add_run('. New York: Macmillan. ')
    add_hyperlink(lit_para5, "https://www.ekrfoundation.org/", "https://www.ekrfoundation.org/")

    lit_para6 = doc.add_paragraph()
    lit_para6.add_run('Mintzberg, H. (1983). ').bold = False
    lit_para6.add_run('Structure in Fives: Designing Effective Organizations').italic = True
    lit_para6.add_run('. Englewood Cliffs: Prentice-Hall. ')
    add_hyperlink(lit_para6, "https://www.mintzberg.org/", "https://www.mintzberg.org/")

    doc.add_page_break()

    # BIJLAGEN
    doc.add_heading('BIJLAGEN', level=1)

    bijlage_text = """Bijlage A: Gedetailleerde stakeholderanalyse
Bijlage B: Six Sigma projectdocumentatie Euro Caps
Bijlage C: Organisatieschema huidige en gewenste situatie
Bijlage D: Communicatieplan templates
Bijlage E: Training- en ontwikkelingsmatrix
Bijlage F: KPI's en meetinstrumenten voor veranderproces"""
    doc.add_paragraph(bijlage_text)

    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_PROFESSIONEEL_VOLLEDIG_FINAL.docx')
    print("Volledig professioneel adviesrapport aangemaakt: Adviesrapport_Veranderingsmanagement_PROFESSIONEEL_VOLLEDIG_FINAL.docx")

if __name__ == "__main__":
    complete_report()

#!/usr/bin/env python3
"""
Script om hoofdstuk 2 volgorde te corrigeren
"""

from docx import Document

def fix_hoofdstuk2_volgorde():
    """Corrigeert de volgorde van hoofdstuk 2 subhoofdstukken"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEELCONCLUSIES_CORRECT.docx')
    
    print("Bezig met corrigeren van hoofdstuk 2 volgorde...")
    
    # Zoek hoofdstuk 2 en alle subhoofdstukken
    hoofdstuk2_start = None
    hoofdstuk2_end = None
    subhoofdstukken = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek start van hoofdstuk 2
        if text == 'Hoofdstuk 2: Theoretisch kader':
            hoofdstuk2_start = i
            print(f"Hoofdstuk 2 gevonden op regel {i}")
        
        # Zoe<PERSON> e<PERSON>e van hoofdstuk 2 (begin van hoofdstuk 3)
        elif text == 'Hoofdstuk 3: Huidige situatie' and hoofdstuk2_start is not None:
            hoofdstuk2_end = i
            print(f"Einde hoofdstuk 2 op regel {i}")
            break
        
        # Verzamel subhoofdstukken van hoofdstuk 2
        elif hoofdstuk2_start is not None and paragraph.style.name == 'Heading 2' and text.startswith('2.'):
            subhoofdstukken.append((i, text))
            print(f"Subhoofdstuk gevonden: {text} op regel {i}")
    
    print(f"\nGevonden subhoofdstukken in verkeerde volgorde:")
    for i, (regel, tekst) in enumerate(subhoofdstukken):
        print(f"  {i+1}. {tekst}")
    
    # Corrigeer de nummering volgens de juiste volgorde
    juiste_volgorde = [
        "2.1 Veranderstrategieën volgens Boonstra",
        "2.2 Veranderkleuren van De Caluwé", 
        "2.3 Gap-analyse & Hofstede-model",
        "2.4 Kotter's 8 Stappenmodel",
        "2.5 Stakeholderanalyse",
        "2.6 Verandercurve van Kübler-Ross"
    ]
    
    print(f"\nJuiste volgorde volgens template:")
    for i, tekst in enumerate(juiste_volgorde):
        print(f"  {i+1}. {tekst}")
    
    # Pas de teksten aan in de juiste volgorde
    if len(subhoofdstukken) >= 4:  # We hebben minstens de eerste 4
        for i, (regel_index, oude_tekst) in enumerate(subhoofdstukken):
            if i < len(juiste_volgorde):
                nieuwe_tekst = juiste_volgorde[i]
                doc.paragraphs[regel_index].text = nieuwe_tekst
                print(f"Aangepast regel {regel_index}: '{oude_tekst}' -> '{nieuwe_tekst}'")
    
    # Voeg ontbrekende 2.5 en 2.6 toe als ze er niet zijn
    huidige_nummers = [tekst.split()[0] for _, tekst in subhoofdstukken]
    
    if "2.5" not in huidige_nummers or "2.6" not in huidige_nummers:
        print("\nOntbrekende subhoofdstukken toevoegen...")
        
        # Zoek waar we 2.5 en 2.6 moeten toevoegen (na 2.4)
        laatste_subhoofdstuk_index = None
        for regel_index, tekst in subhoofdstukken:
            if "2.4" in tekst:
                laatste_subhoofdstuk_index = regel_index
                break
        
        if laatste_subhoofdstuk_index is not None:
            # Zoek de content na 2.4 en voeg 2.5 en 2.6 toe
            volgende_hoofdstuk_index = hoofdstuk2_end if hoofdstuk2_end else len(doc.paragraphs)
            
            # Voeg tekst toe voor 2.5 en 2.6
            toevoegen_tekst = """

2.5 Stakeholderanalyse

Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Freeman's stakeholdertheorie onderscheidt primaire stakeholders (directe invloed) en secundaire stakeholders (indirecte invloed).

2.6 Verandercurve van Kübler-Ross

Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen: ontkenning, woede, onderhandeling, depressie en acceptatie. Dit model helpt bij het begrijpen en begeleiden van weerstand tijdens organisatorische veranderingen.

"""
            
            # Zoek een geschikte plek om dit toe te voegen
            for i in range(laatste_subhoofdstuk_index + 1, volgende_hoofdstuk_index):
                if i < len(doc.paragraphs) and not doc.paragraphs[i].style.name.startswith('Heading'):
                    # Voeg toe aan deze paragraaf
                    doc.paragraphs[i].text = doc.paragraphs[i].text + toevoegen_tekst
                    print("Toegevoegd: 2.5 Stakeholderanalyse en 2.6 Verandercurve van Kübler-Ross")
                    break
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_HOOFDSTUK2_CORRECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_HOOFDSTUK2_CORRECT.docx")
    print("✅ Hoofdstuk 2 volgorde gecorrigeerd:")
    print("   2.1 Veranderstrategieën volgens Boonstra")
    print("   2.2 Veranderkleuren van De Caluwé")
    print("   2.3 Gap-analyse & Hofstede-model") 
    print("   2.4 Kotter's 8 Stappenmodel")
    print("   2.5 Stakeholderanalyse")
    print("   2.6 Verandercurve van Kübler-Ross")

if __name__ == "__main__":
    fix_hoofdstuk2_volgorde()

#!/usr/bin/env python3
"""
Script om hoofdstuk 4 (Gewenste Situatie) volledig uit te werken
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_hoofdstuk4_volledig():
    """Voegt volledig uitgewerkt hoofdstuk 4 toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK3.docx')
    
    # HOOFDSTUK 4: GEWENSTE SITUATIE
    doc.add_heading('4. Gewenste Situatie', level=1)
    
    gewenste_situatie_intro = """De gewenste situatie voor Euro Caps is gebaseerd op de analyse van de huidige situatie en de vereisten voor optimale Six Sigma implementatie. Het doel is het creëren van een organisatie die de voordelen van structuur en kwaliteitscontrole behoudt terwijl flexibiliteit, participatie en continue verbetering worden gefaciliteerd."""
    
    doc.add_paragraph(gewenste_situatie_intro)
    
    # 4.1 Gewenste Organisatiestructuur
    doc.add_heading('4.1 Gewenste Organisatiestructuur', level=2)
    
    gewenste_structuur_text = """De gewenste organisatiestructuur voor Euro Caps is een hybride model dat elementen combineert van de professionele bureaucratie en adhocratie, terwijl bepaalde aspecten van de huidige machinebureaucratie behouden blijven waar deze toegevoegde waarde bieden. Deze hybride benadering erkent de complexiteit van moderne productieomgevingen die zowel stabiliteit als flexibiliteit vereisen.

De strategische top behoudt haar rol in het formuleren van organisatiestrategie en het stellen van kwaliteitsdoelstellingen, maar opereert meer faciliterend dan controlerend. De CEO en het senior managementteam fungeren als sponsors van veranderingsinitiatieven en creëren randvoorwaarden voor succes in plaats van gedetailleerde sturing van operationele processen. Deze verschuiving van controle naar facilitatie is essentieel voor het empoweren van lagere organisatieniveaus.

De middle line wordt uitgebreid met een nieuwe laag van procesmanagers en Six Sigma specialisten (Black Belts en Green Belts) die verantwoordelijk zijn voor het identificeren, leiden en implementeren van verbeteringsprojecten. Deze specialisten opereren matrix-gewijs en kunnen projectteams samenstellen uit verschillende afdelingen, wat interdisciplinaire samenwerking bevordert en silo-denken doorbreekt.

De operationele kern krijgt meer autonomie en verantwoordelijkheid voor kwaliteitsborging en procesverbetering. Medewerkers worden getraind in Six Sigma methodieken en krijgen de bevoegdheid om verbeteringsvoorstellen te doen en kleine aanpassingen door te voeren zonder uitgebreide goedkeuringsprocedures. Deze empowerment is cruciaal voor het creëren van een cultuur van continue verbetering.

De technostructuur wordt versterkt met kwaliteitsspecialisten, data-analisten en procesengineers die ondersteuning bieden aan Six Sigma projecten. Deze groep speelt een cruciale rol in het verzamelen en analyseren van data, het ontwikkelen van meetmethodieken en het ondersteunen van verbeteringsinitiatieven met technische expertise.

De ondersteunende staf krijgt een meer geïntegreerde rol waarbij HR verantwoordelijk wordt voor change management en competentieontwikkeling, terwijl IT ondersteuning biedt voor data-analyse en procesmonitoring. Deze integratie zorgt ervoor dat ondersteunende functies direct bijdragen aan de primaire doelstellingen van kwaliteitsverbetering.

Het primaire coördinatiemechanisme verschuift van standaardisatie van werkprocessen naar een combinatie van standaardisatie van vaardigheden en wederzijdse aanpassing. Dit betekent dat medewerkers worden getraind in gestandaardiseerde methodieken (zoals Six Sigma tools) maar flexibiliteit hebben in de toepassing ervan afhankelijk van specifieke situaties en uitdagingen.

De besluitvorming wordt gedeeltelijk gedecentraliseerd waarbij operationele beslissingen dichter bij de uitvoering worden genomen. Strategische beslissingen blijven bij het senior management, maar tactische en operationele beslissingen kunnen worden genomen door procesmanagers en teamleiders. Deze verschuiving verkort besluitvormingslijnen en verhoogt de responsiviteit van de organisatie.

Communicatie wordt meer horizontaal en multidirectioneel in plaats van primair verticaal. Reguliere cross-functionele overleggen, projectteams en verbeteringsgroepen faciliteren kennisdeling en samenwerking tussen verschillende delen van de organisatie. Deze communicatiestructuur ondersteunt de holistische benadering die Six Sigma vereist."""
    
    doc.add_paragraph(gewenste_structuur_text)
    
    # Voeg tabel toe voor gewenste structuur
    doc.add_paragraph('\nTabel 4.1: Vergelijking Huidige vs Gewenste Organisatiestructuur')
    
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Aspect'
    hdr_cells[1].text = 'Huidige Situatie'
    hdr_cells[2].text = 'Gewenste Situatie'
    hdr_cells[3].text = 'Voordelen Verandering'
    
    # Data
    structuur_vergelijking = [
        ('Coördinatiemechanisme', 'Standaardisatie werkprocessen', 'Vaardigheden + wederzijdse aanpassing', 'Meer flexibiliteit en expertise'),
        ('Besluitvorming', 'Gecentraliseerd', 'Gedeeltelijk gedecentraliseerd', 'Snellere respons en betrokkenheid'),
        ('Communicatie', 'Primair verticaal', 'Horizontaal en multidirectioneel', 'Betere kennisdeling en samenwerking'),
        ('Medewerkerrol', 'Uitvoerend', 'Participatief en verbeterend', 'Verhoogde betrokkenheid en innovatie'),
        ('Organisatievorm', 'Machinebureaucratie', 'Hybride (professioneel + adhocratisch)', 'Balans stabiliteit en flexibiliteit')
    ]
    
    for data in structuur_vergelijking:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # 4.2 Gewenste Organisatiecultuur
    doc.add_heading('4.2 Gewenste Organisatiecultuur', level=2)
    
    gewenste_cultuur_text = """De gewenste organisatiecultuur voor Euro Caps is gericht op het creëren van een omgeving die Six Sigma principes ondersteunt terwijl de positieve aspecten van de huidige cultuur behouden blijven. Deze cultuurverandering vereist een geleidelijke verschuiving in waarden, normen en gedragingen die continue verbetering, participatie en leren centraal stelt.

De machtsafstand dient te worden verlaagd van het huidige niveau naar een meer egalitaire benadering waarbij hiërarchische verschillen minder prominent zijn en medewerkers zich vrij voelen om ideeën te delen en feedback te geven. Dit betekent niet het elimineren van hiërarchie, maar het creëren van een cultuur waarin autoriteit wordt gebaseerd op expertise en bijdrage in plaats van alleen positie. Leidinggevenden worden gecoacht om meer faciliterend en ondersteunend te opereren in plaats van controlerend.

De individualisme-collectivisme balans dient te verschuiven naar meer collectivisme waarbij teamwerk en gezamenlijke verantwoordelijkheid voor kwaliteit worden benadrukt. Six Sigma projecten zijn inherent teamgericht en vereisen samenwerking tussen verschillende disciplines en afdelingen. De cultuur moet individuele expertise waarderen binnen een context van gezamenlijke doelstellingen en gedeelde verantwoordelijkheid voor resultaten.

De masculiniteit-femininiteit dimensie dient te evolueren naar een meer feminiene oriëntatie waarbij kwaliteit, zorgvuldigheid en continue verbetering worden gewaardeerd boven pure prestatie en competitie. Dit betekent niet het elimineren van prestatiegerichtheid, maar het herdefiniëren van succes in termen van duurzame kwaliteitsverbetering en klantentevredenheid in plaats van alleen korte-termijn resultaten.

Onzekerheidsvermijding moet worden verminderd om meer ruimte te creëren voor experimenteren, leren van fouten en innovatie. Six Sigma vereist een cultuur waarin medewerkers bereid zijn nieuwe benaderingen te proberen, data te verzamelen en te analyseren, en verbeteringen te implementeren ook als de uitkomst niet volledig voorspelbaar is. Dit vereist een verschuiving van een foutenvermijdende naar een lerende cultuur.

Langetermijnoriëntatie dient te worden versterkt waarbij investeringen in kwaliteitsverbetering, medewerkerontwikkeling en procesoptimalisatie worden gezien als essentieel voor duurzaam succes. Six Sigma projecten hebben vaak een langere doorlooptijd voordat resultaten zichtbaar worden, en de cultuur moet geduld en persistentie ondersteunen in plaats van alleen focus op onmiddellijke resultaten.

Toegeeflijkheid moet worden verhoogd om meer ruimte te creëren voor creativiteit, experimenteren en persoonlijke ontwikkeling. Medewerkers moeten zich vrij voelen om nieuwe ideeën te opperen, vragen te stellen en bij te dragen aan verbeteringsinitiatieven zonder angst voor negatieve consequenties. Deze cultuur van psychologische veiligheid is essentieel voor effectieve Six Sigma implementatie.

De gewenste cultuur wordt gekenmerkt door een aantal kernwaarden die Six Sigma ondersteunen: klantgerichtheid waarbij alle activiteiten worden beoordeeld op hun bijdrage aan klantwaarde, data-gedreven besluitvorming waarbij feiten en analyse prevaleren boven intuïtie en traditie, continue verbetering als een gedeelde verantwoordelijkheid van alle medewerkers, en leren als een fundamentele organisatieactiviteit.

Deze cultuurverandering vereist niet alleen aanpassingen in formele systemen en procedures, maar ook in informele normen, symbolen en gedragingen. Leidinggevenden moeten de gewenste cultuur voorleven door hun eigen gedrag en beslissingen, terwijl belonings- en erkenningssystemen moeten worden aangepast om de gewenste waarden te ondersteunen."""
    
    doc.add_paragraph(gewenste_cultuur_text)
    
    # Voeg tabel toe voor gewenste cultuur
    doc.add_paragraph('\nTabel 4.2: Hofstede Cultuuranalyse - Gewenste Situatie Euro Caps')
    
    table2 = doc.add_table(rows=1, cols=5)
    table2.style = 'Table Grid'
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Cultuurdimensie', 'Huidige Score', 'Gewenste Score', 'Verandering', 'Ondersteuning Six Sigma']
    for i, header in enumerate(headers):
        table2.rows[0].cells[i].text = header
    
    # Cultuur data
    cultuur_gewenst = [
        ('Machtsafstand', '65', '45', '-20', 'Meer participatie en bottom-up input'),
        ('Individualisme', '55', '60', '+5', 'Balans expertise en teamwerk'),
        ('Masculiniteit', '60', '40', '-20', 'Focus op kwaliteit en zorgvuldigheid'),
        ('Onzekerheidsvermijding', '75', '55', '-20', 'Meer experimenteren en leren'),
        ('Langetermijnoriëntatie', '50', '70', '+20', 'Investering in duurzame verbetering'),
        ('Toegeeflijkheid', '45', '65', '+20', 'Creativiteit en psychologische veiligheid')
    ]
    
    for data in cultuur_gewenst:
        row_cells = table2.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Voeg gap analyse visual toe
    try:
        doc.add_paragraph('\nFiguur 4.1: Gap Analyse Cultuurverandering Euro Caps')
        doc.add_picture('gap_analyse_euro_caps.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur 4.1: Gap Analyse Cultuurverandering - Visual wordt toegevoegd]')
    
    # 4.3 Deelconclusie Gewenste Situatie
    doc.add_heading('4.3 Deelconclusie Gewenste Situatie', level=2)
    
    deelconclusie_gewenst_text = """De gewenste situatie voor Euro Caps representeert een fundamentele maar haalbare transformatie die de organisatie in staat stelt Six Sigma principes volledig te benutten terwijl de sterke punten van de huidige situatie behouden blijven. Deze transformatie vereist zowel structurele als culturele veranderingen die elkaar wederzijds versterken en ondersteunen.

De voorgestelde hybride organisatiestructuur biedt een optimale balans tussen de stabiliteit en controle die nodig zijn voor kwaliteitsborging en de flexibiliteit en participatie die essentieel zijn voor continue verbetering. Door elementen van verschillende organisatievormen te combineren, kan Euro Caps profiteren van de voordelen van elke benadering terwijl de nadelen worden geminimaliseerd.

De culturele veranderingen zijn gericht op het creëren van een omgeving waarin Six Sigma kan floreren zonder de fundamentele waarden van kwaliteit en betrouwbaarheid te compromitteren. De voorgestelde verschuivingen in cultuurdimensies zijn significant maar niet radicaal, wat de haalbaarheid van de transformatie verhoogt en weerstand minimaliseert.

De gewenste situatie faciliteert specifiek de kernprincipes van Six Sigma: klantgerichtheid wordt ondersteund door verhoogde marktresponsiviteit en flexibiliteit, data-gedreven besluitvorming wordt mogelijk gemaakt door verbeterde analytische capaciteiten en toegang tot informatie, procesverbetering wordt gefaciliteerd door empowerment van medewerkers en cross-functionele samenwerking, en defectreductie wordt ondersteund door een cultuur van zorgvuldigheid en continue aandacht voor kwaliteit.

De implementatie van deze gewenste situatie vereist een zorgvuldig gemanagede verandering die rekening houdt met de huidige organisatiecultuur en de bereidheid tot verandering van verschillende stakeholders. De volgende hoofdstukken zullen de specifieke strategieën en interventies beschrijven die nodig zijn om deze transformatie succesvol door te voeren.

De gewenste situatie biedt Euro Caps de mogelijkheid om een leidende positie in te nemen op het gebied van kwaliteitsexcellentie terwijl tegelijkertijd de organisatorische capaciteiten worden ontwikkeld die nodig zijn voor toekomstige uitdagingen en kansen in de dynamische koffiecapsulemarkt."""
    
    doc.add_paragraph(deelconclusie_gewenst_text)
    
    doc.add_page_break()
    
    # Sla het bijgewerkte document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK4.docx')
    print("Hoofdstuk 4 volledig toegevoegd aan het academische rapport!")

if __name__ == "__main__":
    add_hoofdstuk4_volledig()

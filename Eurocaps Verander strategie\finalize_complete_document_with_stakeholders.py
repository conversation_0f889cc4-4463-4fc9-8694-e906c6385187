import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def complete_all_remaining_chapters(doc):
    """Complete all remaining chapters with comprehensive content"""
    
    # Complete Chapter 3
    doc.add_heading('3.2 Huidige organisatiecultuur', 2)
    
    current_culture = """De organisatiecultuur van Euro Caps kan worden geanalyseerd aan de hand van Hofstede's cultuurdimensies. Deze analyse toont een cultuur die gekenmerkt wordt door een balans tussen verschillende dimensies, wat zowel kansen als uitdagingen biedt voor de implementatie van Six Sigma.

Machtsafstand (Gemiddeld): Euro Caps vertoont een gemiddelde machtsafstand waarbij hiërarchie wordt gerespecteerd, maar er ook ruimte is voor input van lagere niveaus. Dit biedt goede mogelijkheden voor Six Sigma implementatie omdat zowel top-down sturing als bottom-up verbetersuggesties mogelijk zijn.

Individualisme vs. Collectivisme (Gebalanceerd): De cultuur toont een balans tussen individuele prestaties en teamwork. Dit is gunstig voor Six Sigma omdat zowel individuele expertise als teamgerichte probleemoplossing worden gewaardeerd.

Masculien vs. Feminien (Licht masculien): Er is een focus op prestaties en resultaten, maar ook aandacht voor werknemerstevredenheid. Dit ondersteunt Six Sigma's focus op meetbare verbeteringen.

Onzekerheidsvermijding (Hoog): Euro Caps heeft een voorkeur voor duidelijke procedures en regels, wat goed aansluit bij Six Sigma's gestructureerde aanpak.

Langetermijngerichtheid (Gemiddeld): Er is aandacht voor zowel korte- als langetermijndoelen, wat belangrijk is voor duurzame Six Sigma implementatie.

Toegeeflijkheid (Gemiddeld): Er is ruimte voor zowel discipline als flexibiliteit, wat helpt bij het balanceren van Six Sigma rigor met praktische toepasbaarheid."""
    
    doc.add_paragraph(current_culture)
    
    doc.add_heading('3.3 Deelconclusie beantwoorden', 2)
    
    conclusion_ch3 = """De analyse van de huidige situatie toont dat Euro Caps een hybride organisatiestructuur heeft die elementen combineert van zowel een machineorganisatie als een innovatieve organisatie. Deze structuur biedt een solide basis voor Six Sigma implementatie door de aanwezige gestandaardiseerde processen, maar vereist aanpassingen om de cross-functionele samenwerking te verbeteren die essentieel is voor succesvolle procesverbetering."""
    
    doc.add_paragraph(conclusion_ch3)
    
    # Chapter 4: Desired Situation
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 4: Gewenste situatie', 1)
    
    intro_ch4 = """Dit hoofdstuk schetst de gewenste toekomstsituatie voor Euro Caps na succesvolle implementatie van Six Sigma. De gewenste situatie is gebaseerd op best practices uit de literatuur en ervaringen van andere organisaties die Six Sigma succesvol hebben geïmplementeerd."""
    
    doc.add_paragraph(intro_ch4)
    
    doc.add_heading('4.1 Gewenste organisatiestructuur', 2)
    
    desired_structure = """De gewenste organisatiestructuur voor Euro Caps behoudt de sterke punten van de huidige hybride vorm, maar voegt elementen toe die Six Sigma ondersteunen. De ideale structuur kenmerkt zich door verbeterde horizontale communicatie, geïntegreerde kwaliteitsfunctie, flexibele projectstructuren, verbeterde informatiestromen, en empowerment van medewerkers."""
    
    doc.add_paragraph(desired_structure)
    
    doc.add_heading('4.2 Gewenste organisatiecultuur', 2)
    
    desired_culture = """De gewenste organisatiecultuur voor Euro Caps bouwt voort op de bestaande sterke punten maar voegt specifieke elementen toe die Six Sigma ondersteunen: continue verbetercultuur, data-gedreven besluitvorming, klantgerichtheid, samenwerking en kennisdeling, leren en ontwikkeling, en erkenning en beloning."""
    
    doc.add_paragraph(desired_culture)
    
    # Chapter 5: Change Strategy with CORRECTED Euro Caps Stakeholder Analysis
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 5: Veranderstrategie + implementatieplan', 1)
    
    strategy_intro = """Dit hoofdstuk presenteert de concrete veranderstrategie voor Euro Caps, gebaseerd op de analyses uit de voorgaande hoofdstukken. De strategie combineert Boonstra's ontwikkelingsstrategie met Kotter's gefaseerde implementatiemodel en wordt uitgevoerd over een periode van 21 maanden."""
    
    doc.add_paragraph(strategy_intro)
    
    # 5.1 Stakeholder Analysis - CORRECTED for actual Euro Caps stakeholders
    doc.add_heading('5.1 Stakeholderanalyse Euro Caps', 2)
    
    stakeholder_intro = """Voor een succesvolle implementatie van Six Sigma bij Euro Caps is een grondige stakeholderanalyse essentieel. De onderstaande analyse identificeert alle relevante stakeholders op basis van de werkelijke organisatiestructuur van Euro Caps en hun rol in het veranderingsproces."""
    
    doc.add_paragraph(stakeholder_intro)
    
    # CORRECTED Euro Caps stakeholder data based on actual organization
    stakeholder_headers = ['Stakeholder', 'Type', 'Belang', 'Invloed', 'Positie', 'Strategie']
    stakeholder_data = [
        ['CEO (Nils Clement)', 'Intern-Primair', 'Hoog', 'Hoog', 'Mover', 'Betrekken bij visie en strategische beslissingen'],
        ['Manager Bedrijfsvoering (Servé Bosland)', 'Intern-Primair', 'Hoog', 'Hoog', 'Mover', 'Actief betrekken bij implementatie'],
        ['Manager ICT (Erik Dekker)', 'Intern-Primair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren en technische ondersteuning'],
        ['Productiemanager (Maik Ritter)', 'Intern-Primair', 'Hoog', 'Hoog', 'Blocker', 'Overtuigen door training en voordelen'],
        ['Productiemanager (Maria Stanić)', 'Intern-Primair', 'Hoog', 'Hoog', 'Blocker', 'Overtuigen door training en voordelen'],
        ['Hoofd Kwaliteitsbeheer (Kees Keurig)', 'Intern-Primair', 'Hoog', 'Gemiddeld', 'Mover', 'Actief betrekken als Six Sigma champion'],
        ['HR Manager (Uwe Regel)', 'Intern-Secundair', 'Gemiddeld', 'Laag', 'Floater', 'Ondersteuning bij training en cultuurverandering'],
        ['Hoofd Financiën (Berkan Arrindell)', 'Intern-Secundair', 'Hoog', 'Gemiddeld', 'Floater', 'Rapporteren ROI en financiële voordelen'],
        ['Manager Logistiek (Rijk Wegen)', 'Intern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren over procesverbeteringen'],
        ['Manager Inkoop (Ko Jager)', 'Intern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren over leverancierseisen'],
        ['Productiemedewerkers (Ismail, Samantha)', 'Intern-Primair', 'Gemiddeld', 'Laag', 'Blocker', 'Betrekken bij verbeterprojecten'],
        ['Klanten (Retailers/Koffiebranders)', 'Extern-Primair', 'Hoog', 'Hoog', 'Mover', 'Communiceren kwaliteitsvoordelen'],
        ['Leveranciers (Machines/Grondstoffen)', 'Extern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Informeren over nieuwe kwaliteitseisen'],
        ['NVWA (Toezichthouder)', 'Extern-Secundair', 'Hoog', 'Hoog', 'Floater', 'Compliance en transparantie waarborgen'],
        ['Certificeringsinstanties (UTZ/Organic)', 'Extern-Secundair', 'Gemiddeld', 'Gemiddeld', 'Floater', 'Standaarden naleven en rapporteren']
    ]
    
    # Create stakeholder table
    create_proper_table(doc, 'Tabel 5.1: Stakeholderanalyse Euro Caps (Gecorrigeerd)', stakeholder_headers, stakeholder_data)
    
    return doc

def add_boonstra_strategy_and_kotter(doc):
    """Add Boonstra strategy selection and Kotter implementation"""
    
    # 5.2 Boonstra Strategy Selection
    doc.add_heading('5.2 Veranderstrategie selectie volgens Boonstra', 2)
    
    boonstra_strategy = """Op basis van de analyse van Euro Caps' cultuur en context wordt de ontwikkelingsstrategie van Boonstra gekozen als de meest geschikte aanpak. Deze strategie kenmerkt zich door een participatieve benadering waarbij medewerkers actief betrokken worden bij het veranderproces."""
    
    doc.add_paragraph(boonstra_strategy)
    
    # Boonstra decision matrix
    boonstra_headers = ['Strategie', 'Past bij cultuur', 'Past bij structuur', 'Weinig weerstand', 'Sterke samenwerking', 'Langetermijn effect', 'Totale score']
    boonstra_data = [
        ['Ontwikkelingsstrategie', '4', '4', '4', '5', '5', '22'],
        ['Ingrijpende strategie', '2', '3', '2', '3', '4', '14'],
        ['Machtsstrategie', '1', '2', '3', '2', '3', '11'],
        ['Onderhandelingsstrategie', '4', '4', '4', '4', '4', '20'],
        ['Verleidingsstrategie', '3', '3', '3', '3', '3', '15']
    ]
    
    create_proper_table(doc, 'Tabel 5.2: Beslissingsmatrix veranderstrategieën volgens Boonstra', boonstra_headers, boonstra_data)
    
    # Add Boonstra decision matrix visual
    add_visual_with_caption(doc, 'Visual_9_Boonstra_Beslissingsmatrix.png', 
                           'Figuur 5.1: Beslissingsmatrix voor veranderstrategieën volgens Boonstra')
    
    # 5.3 Kotter Implementation Plan
    doc.add_heading('5.3 Implementatieplan volgens Kotter (21 maanden)', 2)
    
    kotter_implementation = """De implementatie van Six Sigma bij Euro Caps wordt gefaseerd uitgevoerd over 21 maanden, geïntegreerd met Kotter's acht stappen:

Fase 1: Voorbereiding (Maanden 1-3)
- Stap 1: Urgentiebesef creëren door marktanalyse en concurrentiedruk
- Stap 2: Leidende coalitie vormen met CEO, Manager Bedrijfsvoering, en Hoofd Kwaliteitsbeheer
- Stap 3: Visie en strategie ontwikkelen voor Six Sigma implementatie

Fase 2: Implementatie (Maanden 4-15)
- Stap 4: Visie communiceren naar alle stakeholders
- Stap 5: Draagvlak creëren door training en empowerment
- Stap 6: Korte termijn successen genereren door pilotprojecten

Fase 3: Verankering (Maanden 16-21)
- Stap 7: Verbeteringen consolideren en uitbreiden
- Stap 8: Nieuwe benaderingen verankeren in de cultuur"""
    
    doc.add_paragraph(kotter_implementation)
    
    # Add DMAIC-Kotter integration visual
    add_visual_with_caption(doc, 'Visual_10_DMAIC_Kotter_Integratie.png', 
                           'Figuur 5.2: Integratie van DMAIC-cyclus met Kotter\'s 8-stappenmodel')
    
    return doc

if __name__ == "__main__":
    print("=== Finalizing Complete Document with Stakeholders ===")
    
    # Load existing document
    doc = Document('Adviesrapport_Veranderingsmanagement_COMPLEET_MET_ALLE_VISUALS.docx')
    
    # Complete all remaining content
    doc = complete_all_remaining_chapters(doc)
    doc = add_boonstra_strategy_and_kotter(doc)
    
    # Save final document
    doc.save('Adviesrapport_Veranderingsmanagement_FINAAL_COMPLEET_MET_STAKEHOLDERS.docx')
    print("Final complete document with corrected stakeholders created!")
    print("\n=== FINALE DOCUMENT VOLTOOID ===")
    print("✅ Hoofdstukken 3, 4, 5 compleet")
    print("✅ Correcte Euro Caps stakeholderanalyse")
    print("✅ Boonstra beslissingsmatrix met visual")
    print("✅ Kotter implementatieplan (21 maanden)")
    print("✅ DMAIC-Kotter integratie visual")
    print("✅ Alle echte visuals in plaats van placeholders")
    print("✅ Professionele structuur en opmaak")
    print("\n🎯 DOCUMENT IS NU KLAAR VOOR HOOFDSTUKKEN 6-7 EN APPENDICES!")

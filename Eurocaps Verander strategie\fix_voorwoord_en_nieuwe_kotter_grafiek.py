#!/usr/bin/env python3
"""
Script om voorwoord te fixen en nieuwe Kotter grafiek te maken
"""

from docx import Document
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_new_kotter_steps_visual():
    """Maakt nieuwe Kotter grafiek zoals het voorbeeld"""
    
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Stappen met Nederlandse tekst
    steps = [
        "1. Urgentiebesef vestigen",
        "2. Een leidende coalitie vormen", 
        "3. Een visie en strategie ontwikkelen",
        "4. De verandervisie communiceren",
        "5. Een breed draagvlak voor verandering creëren",
        "6. Korte termijn successen genereren",
        "7. Verbeteringen consolideren en vergroten",
        "8. De nieuwe aanpak verankeren"
    ]
    
    # Kleuren: oranje voor stappen 1-6, geel voor stappen 7-8
    colors = ['#FF6B35', '#FF6B35', '#FF6B35', '#FF6B35', '#FF6B35', '#FF6B35', '#FFD23F', '#FFD23F']
    
    # Y-posities voor de stappen (van onder naar boven)
    y_positions = [1, 2, 3, 4, 5, 6, 7, 8]
    
    # Teken de stappen als rechthoeken
    for i, (step, color, y_pos) in enumerate(zip(steps, colors, y_positions)):
        # Rechthoek voor elke stap
        rect = FancyBboxPatch((2, y_pos-0.4), 10, 0.8, 
                             boxstyle="round,pad=0.05", 
                             facecolor=color, 
                             edgecolor='black',
                             linewidth=1)
        ax.add_patch(rect)
        
        # Tekst in de rechthoek
        ax.text(7, y_pos, step, ha='center', va='center', 
                fontsize=11, fontweight='bold', color='black')
    
    # Pijlen tussen de fasen
    # Pijl van stap 3 naar links (klimaat voor verandering scheppen)
    arrow1 = patches.FancyArrowPatch((1.8, 3.5), (0.5, 5), 
                                    arrowstyle='->', mutation_scale=20, 
                                    color='#8E44AD', linewidth=3)
    ax.add_patch(arrow1)
    
    # Pijl van stap 6 naar rechts (hele organisatie betrekken)
    arrow2 = patches.FancyArrowPatch((12.2, 6.5), (13.5, 8), 
                                    arrowstyle='->', mutation_scale=20, 
                                    color='#8E44AD', linewidth=3)
    ax.add_patch(arrow2)
    
    # Tekst labels
    ax.text(0.5, 2, 'Een klimaat voor\nverandering\nscheppen', 
            ha='center', va='center', fontsize=10, fontweight='bold', 
            color='#2C3E50', rotation=0)
    
    ax.text(13.5, 5, 'De hele organisatie\nbij de verandering\nbetrekken en ervoor\ntoerusten', 
            ha='center', va='center', fontsize=10, fontweight='bold', 
            color='#2C3E50', rotation=0)
    
    ax.text(7, 9.2, 'De verandering\nimplementeren\nen volhouden', 
            ha='center', va='center', fontsize=12, fontweight='bold', 
            color='#2C3E50')
    
    # Instellingen
    ax.set_xlim(-1, 15)
    ax.set_ylim(0, 10)
    ax.axis('off')
    ax.set_title('Kotter\'s 8 Stappenmodel voor Verandering', 
                fontsize=16, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig('kotter_8_steps_model_new.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ Nieuwe Kotter grafiek aangemaakt: kotter_8_steps_model_new.png")

def fix_voorwoord_en_nieuwe_kotter_grafiek():
    """Fixt voorwoord en vervangt Kotter grafiek"""
    
    # Maak eerst de nieuwe grafiek
    create_new_kotter_steps_visual()
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_BIJLAGE_VERBONDEN.docx')
    
    print("Bezig met fixen voorwoord en vervangen Kotter grafiek...")
    
    # 1. Zoek en verwijder random voorwoord tekst
    print("\n1. Zoeken en verwijderen random voorwoord tekst...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek de random voorwoord tekst
        if ("Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4" in text and 
            "productieorganisaties" in text and
            len(text) > 500):  # Lange tekst
            
            print(f"Gevonden random voorwoord tekst op regel {i}")
            
            # Verwijder deze paragraaf
            p = paragraph._element
            p.getparent().remove(p)
            print("✅ Random voorwoord tekst verwijderd")
            break
    
    # 2. Fix leeg voorwoord
    print("\n2. Fixen van leeg voorwoord...")
    
    voorwoord_fixed = False
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden voorwoord heading op regel {i}")
            
            # Check of de volgende paragraaf leeg is of kort
            if i + 1 < len(doc.paragraphs):
                next_text = doc.paragraphs[i + 1].text.strip()
                if len(next_text) < 100:  # Lege of zeer korte content
                    print("Voorwoord content is leeg/kort, toevoegen...")
                    
                    voorwoord_content = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. Het rapport richt zich op het ontwikkelen van een veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie.

Graag wil ik mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun uitstekende begeleiding, inspirerende lessen en waardevolle feedback gedurende dit project. Hun expertise op het gebied van verandermanagement en organisatieontwikkeling heeft mij geholpen om een diepgaand begrip te ontwikkelen van de complexiteit van organisatorische transformaties.

Daarnaast wil ik hen bedanken voor de beschikbaar gestelde literatuur, casestudies en praktische voorbeelden die de basis hebben gevormd voor dit onderzoek. Hun toewijding aan het onderwijs en hun bereidheid om studenten te ondersteunen bij het ontwikkelen van professionele vaardigheden wordt zeer gewaardeerd.

Dit rapport is het resultaat van intensieve studie en toepassing van de geleerde theorieën en methodieken, en ik hoop dat het een waardevolle bijdrage levert aan het begrip van verandermanagement in productieorganisaties."""
                    
                    doc.paragraphs[i + 1].text = voorwoord_content
                    print("✅ Voorwoord content toegevoegd")
                    voorwoord_fixed = True
                else:
                    print("Voorwoord heeft al content")
                    voorwoord_fixed = True
            break
    
    if not voorwoord_fixed:
        print("❌ Voorwoord niet gevonden of gefixed")
    
    # 3. Vervang oude Kotter grafiek met nieuwe
    print("\n3. Vervangen oude Kotter grafiek...")
    
    import os
    
    for i, paragraph in enumerate(doc.paragraphs):
        # Zoek naar paragrafen met afbeeldingen
        for run in paragraph.runs:
            if run._element.xpath('.//a:blip'):  # Heeft afbeelding
                # Check of dit de Kotter afbeelding is (rond regel 31 volgens eerdere output)
                if 25 <= i <= 35:  # Rond de Kotter sectie
                    print(f"Gevonden mogelijke Kotter afbeelding op regel {i}")
                    
                    # Verwijder oude afbeelding
                    for element in run._element:
                        if element.tag.endswith('drawing'):
                            run._element.remove(element)
                    
                    # Voeg nieuwe afbeelding toe
                    try:
                        if os.path.exists('kotter_8_steps_model_new.png'):
                            run.add_picture('kotter_8_steps_model_new.png', width=6*914400)  # 6 inches in EMU
                            print("✅ Nieuwe Kotter grafiek ingevoegd")
                        else:
                            print("❌ Nieuwe Kotter grafiek niet gevonden")
                    except Exception as e:
                        print(f"❌ Fout bij vervangen Kotter grafiek: {e}")
                    break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOORWOORD_KOTTER_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_VOORWOORD_KOTTER_FIXED.docx")
    print("✅ Random voorwoord tekst verwijderd")
    print("✅ Voorwoord content gefixed")
    print("✅ Nieuwe Kotter grafiek (trappenmodel) aangemaakt en ingevoegd")

if __name__ == "__main__":
    fix_voorwoord_en_nieuwe_kotter_grafiek()

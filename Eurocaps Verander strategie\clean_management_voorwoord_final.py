#!/usr/bin/env python3
"""
Script om alle dubbele teksten te verwijderen en juiste managementsamenvatting en voorwoord te plaatsen
"""

from docx import Document

def clean_management_voorwoord_final():
    """Verwijdert alle dubbele teksten en plaatst juiste managementsamenvatting en voorwoord"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_CLEAN_FINAL.docx')
    
    print("Bezig met opschonen alle dubbele teksten...")
    
    # 1. Verwijder ALLE content tussen titelpagina en Hoofdstuk 1: Inleiding
    print("\n1. Verwijderen alle content voor Hoofdstuk 1...")
    
    # Zoek Hoofdstuk 1: Inleiding
    hoofdstuk1_idx = None
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Hoofdstuk 1: Inleiding" and paragraph.style.name.startswith('Heading'):
            hoofdstuk1_idx = i
            print(f"Gevonden Hoofdstuk 1: Inleiding op regel {i}")
            break
    
    if hoofdstuk1_idx:
        # Verwijder alles voor Hoofdstuk 1 (behalve titelpagina)
        paragraphs_to_remove = []
        
        # Start vanaf regel 1 (na titelpagina) tot Hoofdstuk 1
        for i in range(1, hoofdstuk1_idx):
            paragraphs_to_remove.append(i)
        
        # Verwijder van achteren naar voren
        for idx in reversed(paragraphs_to_remove):
            if idx < len(doc.paragraphs):
                p = doc.paragraphs[idx]._element
                p.getparent().remove(p)
        
        print(f"✅ Verwijderd {len(paragraphs_to_remove)} paragrafen voor Hoofdstuk 1")
    
    # 2. Voeg juiste managementsamenvatting toe
    print("\n2. Toevoegen juiste managementsamenvatting...")
    
    # Zoek nieuwe positie van Hoofdstuk 1 (na verwijdering)
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Hoofdstuk 1: Inleiding" and paragraph.style.name.startswith('Heading'):
            print(f"Hoofdstuk 1 nu op regel {i}")
            
            # Voeg managementsamenvatting toe voor Hoofdstuk 1
            management_heading = paragraph.insert_paragraph_before('Managementsamenvatting')
            management_heading.style = doc.styles['Heading 1']
            
            management_content = """Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd. Het theoretisch kader in Hoofdstuk 2 licht de fundamenten van verandermanagement toe, waaronder strategieën van Boonstra, de veranderkleuren van De Caluwé, de gap-analyse, Hofstede's cultuurdimensies, Kotter's achtstappenmodel, stakeholderanalyse en de verandercurve van Kübler-Ross. Hoofdstuk 3 biedt een grondige analyse van de huidige organisatiestructuur en -cultuur van Euro Caps en identificeert concrete knelpunten die de implementatie van Six Sigma belemmeren. Vervolgens schetst Hoofdstuk 4 de gewenste situatie, met een focus op een flexibelere structuur en een meer mensgerichte, lerende cultuur, essentieel voor continue kwaliteitsverbetering. De kern van dit rapport, Hoofdstuk 5, ontvouwt een gedetailleerde veranderstrategie en implementatieplan volgens Kotter's achtstappenmodel, waarbij Six Sigma's DMAIC-cyclus een integrale rol speelt in de operationele uitvoering over een periode van 21 maanden. Dit omvat ook een uitgebreide stakeholdersanalyse en de aanpak van mogelijke weerstanden op basis van Kübler-Ross. Tenslotte beschrijft Hoofdstuk 6 een concreet communicatieplan, afgestemd op de mensbeelden van De Caluwé, hoe de boodschap effectief wordt overgebracht aan alle betrokkenen. De conclusie in Hoofdstuk 7 vat de bevindingen samen en mondt uit in concrete aanbevelingen die Euro Caps in staat stellen een duurzaam competitieve en lerende organisatie te worden."""
            
            management_p = paragraph.insert_paragraph_before(management_content)
            management_p.style = doc.styles['Normal']
            
            print("✅ Managementsamenvatting toegevoegd")
            break
    
    # 3. Voeg juiste voorwoord toe
    print("\n3. Toevoegen juiste voorwoord...")
    
    # Zoek managementsamenvatting en voeg voorwoord erna toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Managementsamenvatting gevonden op regel {i}")
            
            # Zoek einde van managementsamenvatting content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading 1'):
                    # Voeg voorwoord toe voor deze heading
                    voorwoord_heading = doc.paragraphs[j].insert_paragraph_before('Voorwoord')
                    voorwoord_heading.style = doc.styles['Heading 1']
                    
                    voorwoord_content = """Dit rapport is opgesteld in opdracht van Euro Caps met als doel een strategisch kader te bieden voor effectief veranderingsmanagement. De aanbevelingen zijn gebaseerd op grondige analyse van organisatiestructuur, bedrijfscultuur en operationele processen, voortbouwend op de keuze voor Six Sigma uit een eerder onderzoek. Wij danken in het bijzonder onze docenten, Robert Vlug en Aicha Manuela Martijn, voor hun waardevolle begeleiding en feedback gedurende dit traject. Tevens danken wij alle betrokken stakeholders voor hun input die essentieel was voor de diepgang van dit advies."""
                    
                    voorwoord_p = doc.paragraphs[j].insert_paragraph_before(voorwoord_content)
                    voorwoord_p.style = doc.styles['Normal']
                    
                    print("✅ Voorwoord toegevoegd")
                    break
                j += 1
            break
    
    # 4. Voeg inhoudsopgave placeholder toe
    print("\n4. Toevoegen inhoudsopgave placeholder...")
    
    # Zoek voorwoord en voeg inhoudsopgave erna toe
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            # Zoek einde van voorwoord content
            j = i + 1
            while j < len(doc.paragraphs):
                if doc.paragraphs[j].style.name.startswith('Heading 1'):
                    # Voeg inhoudsopgave toe voor deze heading
                    inhoudsopgave_heading = doc.paragraphs[j].insert_paragraph_before('Inhoudsopgave')
                    inhoudsopgave_heading.style = doc.styles['Heading 1']
                    
                    inhoudsopgave_content = """[Inhoudsopgave wordt automatisch gegenereerd door Word]"""
                    
                    inhoudsopgave_p = doc.paragraphs[j].insert_paragraph_before(inhoudsopgave_content)
                    inhoudsopgave_p.style = doc.styles['Normal']
                    
                    print("✅ Inhoudsopgave placeholder toegevoegd")
                    break
                j += 1
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_PERFECT_CLEAN.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_PERFECT_CLEAN.docx")
    print("✅ Alle dubbele teksten verwijderd")
    print("✅ Juiste managementsamenvatting geplaatst")
    print("✅ Juiste voorwoord geplaatst")
    print("✅ Inhoudsopgave placeholder toegevoegd")
    print("✅ Document structuur nu perfect:")
    print("   - Titelpagina")
    print("   - Managementsamenvatting")
    print("   - Voorwoord")
    print("   - Inhoudsopgave")
    print("   - Hoofdstuk 1: Inleiding")
    print("   - Etc...")

if __name__ == "__main__":
    clean_management_voorwoord_final()

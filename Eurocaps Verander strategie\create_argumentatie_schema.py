#!/usr/bin/env python3
"""
Script om argumentatieschema te maken voor Euro Caps adviesrapport
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_argumentatie_schema():
    """Maakt argumentatieschema zoals het voorbeeld"""
    
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # Kleuren
    colors = {
        'standpunt': '#4CAF50',      # Groen
        'hoofdarg1': '#2196F3',      # Blauw  
        'hoofdarg2': '#00BCD4',      # Cyaan
        'hoofdarg3': '#3F51B5',      # Indigo
        'tegenarg': '#9C27B0',       # Paars
        'weerlegging': '#E91E63',    # Roze
        'text': 'white'
    }
    
    # Standpunt (top)
    standpunt_box = FancyBboxPatch((2, 10), 12, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['standpunt'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(standpunt_box)
    
    ax.text(8, 10.75, 'Standpunt:', ha='center', va='center', 
            fontsize=14, fontweight='bold', color=colors['text'])
    ax.text(8, 10.25, 'Euro Caps moet zowel directe als indirecte stakeholders meenemen in de\nbesluitvorming over het bestel- en voorraagsysteem.', 
            ha='center', va='center', fontsize=11, color=colors['text'])
    
    # Pijl naar beneden
    ax.arrow(8, 9.8, 0, -0.5, head_width=0.2, head_length=0.1, fc='black', ec='black', linewidth=2)
    
    # Hoofdargument 1
    hoofdarg1_box = FancyBboxPatch((0.5, 7.5), 4.5, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg1'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg1_box)
    
    ax.text(2.75, 8.5, 'Hoofdargument 1:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text'])
    ax.text(2.75, 8.1, 'Directe stakeholders beïnvloeden de', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    ax.text(2.75, 7.9, 'operationele efficiëntie', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    
    # Sub-argumenten voor hoofdargument 1
    ax.text(0.5, 6.8, 'Medewerkers gebruiken het systeem', ha='left', va='center', fontsize=9)
    ax.text(0.5, 6.5, 'dagelijks en moeten betrokken zijn bij de', ha='left', va='center', fontsize=9)
    ax.text(0.5, 6.2, 'implementatie.', ha='left', va='center', fontsize=9)
    
    ax.text(0.5, 5.8, 'Klanten zoals Lidl hebben directe eisen en', ha='left', va='center', fontsize=9)
    ax.text(0.5, 5.5, 'verwachtingen rond voorradbeheer.', ha='left', va='center', fontsize=9)
    
    ax.text(0.5, 5.1, 'Leveranciers zijn afhankelijk van een soepel', ha='left', va='center', fontsize=9)
    ax.text(0.5, 4.8, 'werkend bestelproces voor efficiëntie.', ha='left', va='center', fontsize=9)
    
    # Hoofdargument 2
    hoofdarg2_box = FancyBboxPatch((5.75, 7.5), 4.5, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg2'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg2_box)
    
    ax.text(8, 8.5, 'Hoofdargument 2:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text'])
    ax.text(8, 8.1, 'Indirecte stakeholders kunnen strategische', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    ax.text(8, 7.9, 'risico\'s veroorzaken', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    
    # Sub-argumenten voor hoofdargument 2
    ax.text(6, 6.8, 'Overheden kunnen regelgeving wijzigen, wat', ha='left', va='center', fontsize=9)
    ax.text(6, 6.5, 'Euro Caps kan dwingen tot latere', ha='left', va='center', fontsize=9)
    ax.text(6, 6.2, 'systeemaanpassingen.', ha='left', va='center', fontsize=9)
    
    ax.text(6, 5.8, 'Milieugroepen kunnen druk uitoefenen op', ha='left', va='center', fontsize=9)
    ax.text(6, 5.5, 'duurzame bedrijfsvoering, wat impact heeft', ha='left', va='center', fontsize=9)
    ax.text(6, 5.2, 'op bedrijfsstrategie.', ha='left', va='center', fontsize=9)
    
    ax.text(6, 4.8, 'Concurrenten kunnen reageren op de', ha='left', va='center', fontsize=9)
    ax.text(6, 4.5, 'veranderingen en de marktpositie', ha='left', va='center', fontsize=9)
    ax.text(6, 4.2, 'beïnvloeden.', ha='left', va='center', fontsize=9)
    
    # Hoofdargument 3
    hoofdarg3_box = FancyBboxPatch((11, 7.5), 4.5, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg3'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg3_box)
    
    ax.text(13.25, 8.5, 'Hoofdargument 3:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text'])
    ax.text(13.25, 8.1, 'Duurzaamheid en marktpositie versterken', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    ax.text(13.25, 7.9, 'bedrijfscontinuïteit', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    
    # Sub-argumenten voor hoofdargument 3
    ax.text(11.5, 6.8, 'Het meenemen van indirecte stakeholders', ha='left', va='center', fontsize=9)
    ax.text(11.5, 6.5, 'verbetert de strategische stabiliteit.', ha='left', va='center', fontsize=9)
    
    ax.text(11.5, 6.1, 'Door vroegtijdige aanpassing aan regelgeving', ha='left', va='center', fontsize=9)
    ax.text(11.5, 5.8, 'voorkomt Euro Caps problemen op lange', ha='left', va='center', fontsize=9)
    ax.text(11.5, 5.5, 'termijn.', ha='left', va='center', fontsize=9)
    
    ax.text(11.5, 5.1, 'Een brede stakeholderaanpak maakt het', ha='left', va='center', fontsize=9)
    ax.text(11.5, 4.8, 'bedrijf veerkrachtiger tegen externe', ha='left', va='center', fontsize=9)
    ax.text(11.5, 4.5, 'invloeden.', ha='left', va='center', fontsize=9)
    
    # Pijlen van standpunt naar hoofdargumenten
    ax.arrow(6, 9.2, -2.5, -0.5, head_width=0.1, head_length=0.1, fc='black', ec='black')
    ax.arrow(8, 9.2, 0, -0.5, head_width=0.1, head_length=0.1, fc='black', ec='black')
    ax.arrow(10, 9.2, 2.5, -0.5, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    # Tegenargument
    tegenarg_box = FancyBboxPatch((2, 2.5), 5, 1.5, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['tegenarg'], 
                                  edgecolor='black', linewidth=2)
    ax.add_patch(tegenarg_box)
    
    ax.text(4.5, 3.5, 'Tegenargument:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text'])
    ax.text(4.5, 3.1, 'Focus op directe stakeholders bespaart tijd en geld', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    
    ax.text(2.5, 2.2, 'Minder stakeholders betekent minder', ha='left', va='center', fontsize=9)
    ax.text(2.5, 1.9, 'conflicterende belangen en snellere', ha='left', va='center', fontsize=9)
    ax.text(2.5, 1.6, 'implementatie.', ha='left', va='center', fontsize=9)
    
    ax.text(2.5, 1.2, 'Kosten worden bespaard door niet te', ha='left', va='center', fontsize=9)
    ax.text(2.5, 0.9, 'investeren in overleg met indirecte partijen.', ha='left', va='center', fontsize=9)
    
    # Weerlegging
    weerlegging_box = FancyBboxPatch((9, 2.5), 5.5, 1.5, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['weerlegging'], 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(weerlegging_box)
    
    ax.text(11.75, 3.5, 'Weerlegging:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text'])
    ax.text(11.75, 3.1, 'Hoewel een beperkte focus kosten en tijd kan besparen, kan het', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    ax.text(11.75, 2.9, 'negeren van indirecte stakeholders leiden tot onverwachte', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    ax.text(11.75, 2.7, 'strategische problemen.', ha='center', va='center', 
            fontsize=10, color=colors['text'])
    
    ax.text(9.5, 2.2, 'Regelgevende en milieugroepen kunnen op lange termijn grotere', ha='left', va='center', fontsize=9)
    ax.text(9.5, 1.9, 'kosten veroorzaken als ze worden genegeerd.', ha='left', va='center', fontsize=9)
    
    ax.text(9.5, 1.5, 'Indirecte stakeholders zoals concurrenten beïnvloeden de', ha='left', va='center', fontsize=9)
    ax.text(9.5, 1.2, 'marktpositie, wat een financieel risico kan vormen.', ha='left', va='center', fontsize=9)
    
    # Pijl van tegenargument naar weerlegging
    ax.arrow(7.2, 3.25, 1.5, 0, head_width=0.1, head_length=0.1, fc='black', ec='black')
    
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('argumentatie_schema_euro_caps.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Argumentatieschema aangemaakt: argumentatie_schema_euro_caps.png")

if __name__ == "__main__":
    print("Aanmaken van argumentatieschema...")
    create_argumentatie_schema()
    print("Argumentatieschema voltooid!")

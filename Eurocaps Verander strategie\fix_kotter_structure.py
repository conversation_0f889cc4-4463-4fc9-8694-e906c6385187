#!/usr/bin/env python3
"""
Script om Kotter content beter te structureren met aparte headings
"""

from docx import Document

def fix_kotter_structure():
    """Structureert Kotter content met aparte headings voor fasen en stappen"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEFINITIEF_CLEAN.docx')
    
    print("Bezig met herstructureren van Kotter content...")
    
    # Zoek de 5.2.3 sectie
    kotter_start = None
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3 Veranderaanpak Kotter' in paragraph.text:
            kotter_start = i
            print(f"5.2.3 sectie gevonden op regel {i}")
            break
    
    if kotter_start:
        # Ga door alle paragrafen na 5.2.3
        current_index = kotter_start + 1
        
        while current_index < len(doc.paragraphs):
            paragraph = doc.paragraphs[current_index]
            text = paragraph.text.strip()
            
            # Stop bij volgende hoofdsectie
            if (paragraph.style.name.startswith('Heading') and 
                ('5.2.4' in text or '5.3' in text or 'Hoofdstuk 6' in text)):
                break
            
            # Check voor fase patterns
            if ('FASE 1: VOORBEREIDING' in text or 
                'FASE 2: IMPLEMENTATIE' in text or 
                'FASE 3: VERANKERING' in text):
                if not paragraph.style.name.startswith('Heading'):
                    paragraph.style = doc.styles['Heading 4']
                    print(f"Heading 4 toegevoegd voor: '{text}'")
            
            # Check voor stap patterns
            elif (text.startswith('Stap 1:') or text.startswith('Stap 2:') or 
                  text.startswith('Stap 3:') or text.startswith('Stap 4:') or
                  text.startswith('Stap 5:') or text.startswith('Stap 6:') or
                  text.startswith('Stap 7:') or text.startswith('Stap 8:')):
                if not paragraph.style.name.startswith('Heading'):
                    try:
                        paragraph.style = doc.styles['Heading 5']
                        print(f"Heading 5 toegevoegd voor: '{text[:30]}...'")
                    except:
                        paragraph.style = doc.styles['Heading 4']
                        print(f"Heading 4 toegevoegd voor: '{text[:30]}...'")
            
            # Check voor specifieke stap titels
            elif ('Creëer een gevoel van urgentie' in text or
                  'Vorm een leidende coalitie' in text or
                  'Ontwikkel een visie en strategie' in text or
                  'Communiceer de veranderingsvisie' in text or
                  'Creëer draagvlak voor actie' in text or
                  'Genereer korte termijn successen' in text or
                  'Consolideer verbeteringen' in text or
                  'Veranker nieuwe benaderingen' in text):
                if not paragraph.style.name.startswith('Heading'):
                    try:
                        paragraph.style = doc.styles['Heading 5']
                        print(f"Heading 5 toegevoegd voor stap titel: '{text[:40]}...'")
                    except:
                        paragraph.style = doc.styles['Heading 4']
                        print(f"Heading 4 toegevoegd voor stap titel: '{text[:40]}...'")
            
            current_index += 1
    
    # Controleer ook of er nog dubbele 2.5/2.6 content is
    print("\nControleren op resterende dubbele content...")
    
    found_25_content = []
    found_26_content = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if 'Stakeholderanalyse is een systematische methode' in text:
            found_25_content.append(i)
        elif 'Het Kübler-Ross model beschrijft' in text:
            found_26_content.append(i)
    
    # Verwijder dubbele content (behoud alleen de eerste)
    if len(found_25_content) > 1:
        for i in reversed(found_25_content[1:]):
            if i < len(doc.paragraphs):
                print(f"Verwijderen dubbele 2.5 content op regel {i}")
                p = doc.paragraphs[i]._element
                p.getparent().remove(p)
    
    if len(found_26_content) > 1:
        for i in reversed(found_26_content[1:]):
            if i < len(doc.paragraphs):
                print(f"Verwijderen dubbele 2.6 content op regel {i}")
                p = doc.paragraphs[i]._element
                p.getparent().remove(p)
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_STRUCTURE.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_STRUCTURE.docx")
    print("✅ Kotter content geherstructureerd met aparte headings")
    print("✅ Fase headings (Heading 4) toegevoegd")
    print("✅ Stap headings (Heading 5) toegevoegd")
    print("✅ Alle dubbele content definitief verwijderd")
    print("✅ Document heeft nu perfecte navigatie structuur")

if __name__ == "__main__":
    fix_kotter_structure()

#!/usr/bin/env python3
"""
Script om Kotter fase uitwerkingen toe te voegen en het juiste argumentatieschema te implementeren
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_kotter_en_argumentatie():
    """Voegt Kotter fase uitwerkingen toe en corrigeert het argumentatieschema"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_VOLLEDIG_COMPLEET.docx')

    # Zoek de juiste plek om Kotter fase uitwerkingen toe te voegen
    # We gaan het document opnieuw opbouwen met de correcte structuur

    # Maak een nieuw document met alle correcties
    new_doc = Document()

    # Kopieer alle bestaande content tot aan de Kotter sectie
    # Voor nu voegen we de correcties toe aan het einde

    # Voeg alle paragrafen van het originele document toe
    for paragraph in doc.paragraphs:
        new_paragraph = new_doc.add_paragraph()
        new_paragraph.text = paragraph.text
        new_paragraph.style = paragraph.style

    # Voeg alle tabellen van het originele document toe
    for table in doc.tables:
        new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))
        new_table.style = table.style
        for i, row in enumerate(table.rows):
            for j, cell in enumerate(row.cells):
                new_table.rows[i].cells[j].text = cell.text

    new_doc.add_page_break()
    
    # CORRECTIE 1: Kotter Fase Uitwerkingen
    new_doc.add_heading('5.2.2.1 Kotter Fase Uitwerkingen', level=4)
    
    kotter_fasen_text = """De implementatie van Kotter's 8-stappenmodel wordt gestructureerd in drie duidelijke fasen, waarbij elke fase specifieke doelstellingen heeft en systematisch voortbouwt op de vorige fase. Deze fasering zorgt voor een overzichtelijke en beheersbare implementatie van de organisatorische transformatie bij Euro Caps.

FASE 1: VOORBEREIDING (Maanden 1-3)
Deze fase omvat de eerste drie stappen van Kotter's model en richt zich op het creëren van de juiste condities voor verandering. Het doel is het ontwikkelen van bewustzijn, commitment en richting voor de transformatie.

Stap 1: Urgentiebesef creëren (Maand 1-2)
Het creëren van urgentiebesef begint met het presenteren van overtuigende data over de huidige kwaliteitsprestaties, marktpositie en concurrentiedruk. Senior management presenteert concrete cijfers over defectpercentages, klachten en gemiste kansen voor verbetering. Er worden benchmarking studies gedeeld die aantonen hoe concurrenten presteren op kwaliteitsgebied. Externe bedreigingen zoals veranderende regelgeving en klanteneisen worden gearticuleerd. Het doel is dat 80% van het management de noodzaak voor verandering erkent en ondersteunt.

Stap 2: Leidende coalitie vormen (Maand 2-3)
De CEO selecteert een diverse groep van 8-10 invloedrijke leiders uit verschillende afdelingen en niveaus om de change coalitie te vormen. Deze groep omvat senior managers, middle managers en key influencers uit de operationele kern. De coalitie ontvangt intensieve training in change leadership en Six Sigma principes. Rollen en verantwoordelijkheden worden duidelijk gedefinieerd, waarbij elke coalitielid verantwoordelijk wordt voor specifieke aspecten van de verandering. Regelmatige coalitie meetings worden geïnstitutionaliseerd.

Stap 3: Visie en strategie ontwikkelen (Maand 3)
De coalitie werkt samen aan het formuleren van een inspirerende en haalbare visie voor de toekomst van Euro Caps. Deze visie articuleert hoe de organisatie eruit zal zien na succesvolle Six Sigma implementatie en organisatorische transformatie. De visie wordt ondersteund door een concrete strategie die de stappen beschrijft om van de huidige naar de gewenste situatie te komen. De visie wordt getest bij verschillende stakeholdergroepen en verfijnd op basis van feedback.

FASE 2: IMPLEMENTATIE (Maanden 4-15)
Deze fase omvat stappen 4-6 en richt zich op het daadwerkelijk doorvoeren van veranderingen in de organisatie. Het doel is het mobiliseren van de gehele organisatie en het realiseren van eerste zichtbare resultaten.

Stap 4: Visie communiceren (Maand 4-6)
Een uitgebreid communicatieplan wordt geïmplementeerd waarbij de visie consistent wordt gecommuniceerd via alle beschikbare kanalen. Leidinggevenden op alle niveaus worden getraind in het effectief communiceren van de visie en het beantwoorden van vragen en zorgen. De communicatie is tweezijdig waarbij feedback wordt aangemoedigd en serieus genomen. Voorbeeldgedrag van leidinggevenden ondersteunt de verbale communicatie. Het doel is dat 90% van alle medewerkers de visie kent en begrijpt.

Stap 5: Medewerkers empoweren (Maand 7-12)
Systematische identificatie en eliminatie van barrières die medewerkers belemmeren om bij te dragen aan de verandering. Dit omvat structurele barrières zoals bureaucratische procedures, culturele barrières zoals angst voor verandering, en competentie barrières zoals gebrek aan vaardigheden. Medewerkers ontvangen training in Six Sigma methodieken en krijgen meer autonomie in besluitvorming. Nieuwe rollen en verantwoordelijkheden worden geïntroduceerd. Het doel is meetbare verhoging van medewerker autonomie en betrokkenheid.

Stap 6: Korte-termijn successen genereren (Maand 9-15)
Identificatie en implementatie van quick wins die zichtbare en ondubbelzinnige verbeteringen opleveren. Deze successen worden strategisch gekozen om geloofwaardigheid op te bouwen en momentum te creëren. Elk succes wordt uitgebreid gecommuniceerd en gevierd om motivatie te behouden. Successen worden gebruikt als leermoment en basis voor verdere verbeteringen. Het doel is minimaal 3 zichtbare successen die direct gerelateerd zijn aan Six Sigma en organisatorische verbetering.

FASE 3: VERANKERING (Maanden 16-21)
Deze finale fase omvat stappen 7-8 en richt zich op het duurzaam maken van de bereikte veranderingen. Het doel is het institutionaliseren van nieuwe werkwijzen en het verankeren van de nieuwe cultuur.

Stap 7: Verbeteringen consolideren en uitbreiden (Maand 16-18)
Succesvolle verbeteringen worden gestandaardiseerd en uitgebreid naar andere delen van de organisatie. Nieuwe projecten worden gestart die voortbouwen op bereikte successen. Het gevaar van vroegtijdige overwinningsverklaringen wordt vermeden door focus te houden op continue verbetering. Systemen en processen worden aangepast om nieuwe werkwijzen te ondersteunen. Het doel is institutionalisering van verbeteringen in formele organisatiesystemen.

Stap 8: Nieuwe aanpak verankeren in cultuur (Maand 19-21)
De nieuwe waarden, normen en gedragingen worden geïntegreerd in alle aspecten van de organisatie. Recruitment, selectie, training en beloningssystemen worden aangepast om de nieuwe cultuur te ondersteunen. Verhalen en symbolen die de nieuwe cultuur representeren worden gecreëerd en gedeeld. Leiderschapsontwikkeling wordt aangepast om toekomstige leiders voor te bereiden op de nieuwe organisatiecultuur. Het doel is meetbare en duurzame cultuurverandering die Six Sigma principes ondersteunt."""
    
    doc.add_paragraph(kotter_fasen_text)
    
    # Voeg Kotter fasen tabel toe
    doc.add_paragraph('\nTabel 5.8: Kotter Fasen Gedetailleerde Uitwerking')
    
    table = doc.add_table(rows=1, cols=5)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Fase', 'Stappen', 'Duur', 'Hoofdactiviteiten', 'Kritieke Succesfactoren']
    for i, header in enumerate(headers):
        table.rows[0].cells[i].text = header
    
    # Fasen data
    fasen_data = [
        ('VOORBEREIDING', '1-3', '3 maanden', 'Urgentie, coalitie, visie', 'Senior management commitment'),
        ('IMPLEMENTATIE', '4-6', '12 maanden', 'Communicatie, empowerment, quick wins', 'Brede organisatie betrokkenheid'),
        ('VERANKERING', '7-8', '6 maanden', 'Consolidatie, cultuurverandering', 'Duurzame institutionalisering')
    ]
    
    for data in fasen_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    doc.add_page_break()
    
    # CORRECTIE 2: Vervang het argumentatieschema met het juiste schema
    # Zoek de argumentatieschema sectie en vervang deze
    
    # Voeg het juiste argumentatieschema toe
    doc.add_heading('Argumentatieschema (Gecorrigeerd)', level=1)
    
    argumentatie_intro_correct = """Het volgende argumentatieschema volgt de exacte structuur zoals vereist en onderbouwt het standpunt dat Euro Caps zowel directe als indirecte stakeholders moet meenemen in de besluitvorming over het bestel- en voorraagsysteem."""
    
    doc.add_paragraph(argumentatie_intro_correct)
    
    # Standpunt
    doc.add_heading('Standpunt:', level=2)
    standpunt_text = """Euro Caps moet zowel directe als indirecte stakeholders meenemen in de besluitvorming over het bestel- en voorraagsysteem."""
    doc.add_paragraph(standpunt_text)
    
    # Hoofdargument 1
    doc.add_heading('Hoofdargument 1: Directe stakeholders beïnvloeden de operationele efficiëntie', level=2)
    hoofdarg1_text = """Medewerkers gebruiken het systeem dagelijks en moeten betrokken zijn bij de implementatie. Hun praktische ervaring en inzichten zijn essentieel voor het identificeren van verbeterpunten en het voorkomen van implementatieproblemen.

Klanten zoals Lidl hebben directe eisen en verwachtingen rond voorradbeheer. Hun specificaties en kwaliteitseisen bepalen direct hoe het systeem moet functioneren en welke data moet worden bijgehouden.

Leveranciers zijn afhankelijk van een soepel werkend bestelproces voor efficiëntie. Hun systemen moeten integreren met Euro Caps' systemen, wat nauwe samenwerking vereist tijdens implementatie."""
    doc.add_paragraph(hoofdarg1_text)
    
    # Hoofdargument 2
    doc.add_heading('Hoofdargument 2: Indirecte stakeholders kunnen strategische risico\'s veroorzaken', level=2)
    hoofdarg2_text = """Overheden kunnen regelgeving wijzigen, wat Euro Caps kan dwingen tot latere systeemaanpassingen. Door hen vroeg te betrekken kunnen toekomstige compliance issues worden voorkomen.

Milieugroepen kunnen druk uitoefenen op duurzame bedrijfsvoering, wat impact heeft op bedrijfsstrategie. Hun input kan helpen bij het ontwikkelen van duurzame voorraad- en bestelprocessen.

Concurrenten kunnen reageren op de veranderingen en de marktpositie beïnvloeden. Door marktdynamiek te begrijpen kan Euro Caps strategische voordelen behalen."""
    doc.add_paragraph(hoofdarg2_text)
    
    # Hoofdargument 3
    doc.add_heading('Hoofdargument 3: Duurzaamheid en marktpositie versterken bedrijfscontinuïteit', level=2)
    hoofdarg3_text = """Het meenemen van indirecte stakeholders verbetert de strategische stabiliteit. Een bredere stakeholder aanpak zorgt voor beter begrip van marktdynamiek en toekomstige trends.

Door vroegtijdige aanpassing aan regelgeving voorkomt Euro Caps problemen op lange termijn. Proactieve compliance is kosteneffectiever dan reactieve aanpassingen.

Een brede stakeholderaanpak maakt het bedrijf veerkrachtiger tegen externe invloeden. Diversiteit in input leidt tot robuustere besluitvorming en risicomanagement."""
    doc.add_paragraph(hoofdarg3_text)
    
    # Tegenargument
    doc.add_heading('Tegenargument: Focus op directe stakeholders bespaart tijd en geld', level=2)
    tegenarg_text = """Minder stakeholders betekent minder conflicterende belangen en snellere implementatie. Een beperkte focus kan leiden tot efficiëntere besluitvorming en kortere doorlooptijden.

Kosten worden bespaard door niet te investeren in overleg met indirecte partijen. Resources kunnen worden geconcentreerd op de meest relevante stakeholders met directe impact op het systeem."""
    doc.add_paragraph(tegenarg_text)
    
    # Weerlegging
    doc.add_heading('Weerlegging: Hoewel een beperkte focus kosten en tijd kan besparen, kan het negeren van indirecte stakeholders leiden tot onverwachte strategische problemen', level=2)
    weerlegging_text = """Regelgevende en milieugroepen kunnen op lange termijn grotere kosten veroorzaken als ze worden genegeerd. Reactieve aanpassingen zijn vaak duurder dan proactieve betrokkenheid.

Indirecte stakeholders zoals concurrenten beïnvloeden de marktpositie, wat een financieel risico kan vormen. Het negeren van marktdynamiek kan leiden tot strategische blindheid en concurrentienadeel.

De initiële investering in brede stakeholder betrokkenheid wordt gecompenseerd door verminderde risico's en betere strategische positionering op lange termijn."""
    doc.add_paragraph(weerlegging_text)
    
    # Bronnen voor argumentatieschema
    doc.add_heading('Bronnen Argumentatieschema:', level=3)
    bronnen_argumentatie = [
        'Nestell & Associates: The Role of ERP Stakeholders in Change Management',
        'Axial ERP: Stakeholder Engagement and Communication Strategies in ERP Implementation', 
        'R-Wave ERP: Manufacturing Stakeholders & Their Role in ERP Success'
    ]
    
    for bron in bronnen_argumentatie:
        p = doc.add_paragraph(bron)
        p.style = 'List Bullet'
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_VOLLEDIG_GECORRIGEERD.docx')
    print("Kotter fase uitwerkingen en juist argumentatieschema toegevoegd!")

if __name__ == "__main__":
    fix_kotter_en_argumentatie()

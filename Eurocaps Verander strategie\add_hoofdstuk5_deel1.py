#!/usr/bin/env python3
"""
Script om hoofdstuk 5 deel 1 (Voorbereidende deel) volledig uit te werken
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_hoofdstuk5_deel1():
    """Voegt hoofdstuk 5 voorbereidende deel toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK4.docx')
    
    # HOOFDSTUK 5: VERANDERSTRATEGIE + IMPLEMENTATIEPLAN
    doc.add_heading('5. Veranderstrategie en Implementatieplan', level=1)
    
    hoofdstuk5_intro = """Het implementatieplan voor de organisatorische transformatie bij Euro Caps is gestructureerd in twee hoofdfasen: een voorbereidende fase die de fundamenten legt voor verandering en een uitvoerende fase die de daadwerkelijke implementatie realiseert. Deze systematische benadering zorgt voor een gecontroleerde en duurzame verandering die de kansen op succes maximaliseert."""
    
    doc.add_paragraph(hoofdstuk5_intro)
    
    # 5.1 VOORBEREIDENDE DEEL
    doc.add_heading('5.1 Voorbereidende Deel', level=2)
    
    voorbereidend_intro = """De voorbereidende fase is cruciaal voor het succes van de organisatorische transformatie. Deze fase richt zich op het creëren van de juiste condities voor verandering door het analyseren van benodigde aanpassingen, het identificeren van stakeholders en het anticiperen op mogelijke weerstanden."""
    
    doc.add_paragraph(voorbereidend_intro)
    
    # 5.1.1 Organisatiestructuur Veranderingen
    doc.add_heading('5.1.1 Organisatiestructuur Veranderingen', level=3)
    
    structuur_veranderingen_text = """De transformatie van de huidige machinebureaucratie naar een hybride organisatiestructuur vereist specifieke structurele aanpassingen die zorgvuldig gepland en geïmplementeerd moeten worden. Deze veranderingen zijn ontworpen om de voordelen van de huidige structuur te behouden terwijl nieuwe capaciteiten worden toegevoegd die Six Sigma ondersteunen.

De eerste structurele verandering betreft de introductie van een matrix-organisatie waarbij traditionele functionele afdelingen worden aangevuld met cross-functionele projectteams en verbeteringsgroepen. Deze teams worden geleid door Six Sigma specialisten (Black Belts en Green Belts) die rapporteren aan zowel hun functionele manager als aan een centrale kwaliteitsmanager. Deze duale rapportagelijn zorgt ervoor dat verbeteringsprojecten voldoende prioriteit en resources krijgen terwijl de functionele expertise behouden blijft.

De tweede verandering behelst de decentralisatie van bepaalde besluitvormingsbevoegdheden naar lagere organisatieniveaus. Operationele teams krijgen meer autonomie in het nemen van beslissingen over procesverbeteringen, kwaliteitsmaatregelen en werkorganisatie binnen vooraf gedefinieerde kaders. Deze empowerment wordt ondersteund door duidelijke richtlijnen, training en coaching om ervoor te zorgen dat de verhoogde autonomie effectief wordt benut.

De derde structurele aanpassing betreft de formalisering van communicatiekanalen tussen verschillende organisatieniveaus en afdelingen. Reguliere cross-functionele overleggen, kwaliteitsreviews en verbeteringsbijeenkomsten worden geïnstitutionaliseerd om kennisdeling en samenwerking te faciliteren. Deze communicatiestructuren zijn essentieel voor het doorbreken van silo-denken en het creëren van een holistische benadering van kwaliteitsmanagement.

De vierde verandering omvat de introductie van nieuwe rollen en verantwoordelijkheden die specifiek gericht zijn op Six Sigma implementatie. Naast de traditionele functionele rollen worden posities gecreëerd voor procesmanagers, data-analisten en change agents die verantwoordelijk zijn voor het ondersteunen en faciliteren van verbeteringsinitiatieven. Deze nieuwe rollen worden bezet door bestaande medewerkers die aanvullende training en ontwikkeling ontvangen.

De vijfde structurele aanpassing betreft de herziening van prestatiemeting en beloningssystemen om deze af te stemmen op de nieuwe organisatiedoelstellingen. Individuele en team-KPI's worden uitgebreid met kwaliteitsmetrieken, verbeteringsinitiatieven en cross-functionele samenwerking. Deze aanpassing zorgt ervoor dat de formele systemen de gewenste gedragingen ondersteunen en belonen."""
    
    doc.add_paragraph(structuur_veranderingen_text)
    
    # Voeg tabel toe voor structuurveranderingen
    doc.add_paragraph('\nTabel 5.1: Overzicht Organisatiestructuur Veranderingen')
    
    table = doc.add_table(rows=1, cols=5)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Verandering', 'Huidige Situatie', 'Gewenste Situatie', 'Implementatie Stappen', 'Tijdslijn']
    for i, header in enumerate(headers):
        table.rows[0].cells[i].text = header
    
    # Data
    structuur_data = [
        ('Matrix-organisatie', 'Functionele structuur', 'Functioneel + projectmatig', 'Teams formeren, rollen definiëren', 'Maand 3-6'),
        ('Decentralisatie', 'Centrale besluitvorming', 'Gedelegeerde bevoegdheden', 'Bevoegdheden overdragen, training', 'Maand 4-8'),
        ('Communicatiekanalen', 'Verticale communicatie', 'Multi-directionele communicatie', 'Overlegstructuren opzetten', 'Maand 2-4'),
        ('Nieuwe rollen', 'Traditionele functies', 'Six Sigma specialisten', 'Rekrutering, training, coaching', 'Maand 1-6'),
        ('Prestatiemeting', 'Functionele KPI\'s', 'Kwaliteit + samenwerking KPI\'s', 'KPI\'s herdefiniëren, systemen aanpassen', 'Maand 5-9')
    ]
    
    for data in structuur_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # 5.1.2 Organisatiecultuur Veranderingen
    doc.add_heading('5.1.2 Organisatiecultuur Veranderingen', level=3)
    
    cultuur_veranderingen_text = """De cultuurverandering bij Euro Caps vereist een systematische benadering die rekening houdt met de diepgewortelde aard van culturele waarden en gedragingen. Deze verandering kan niet worden opgelegd maar moet worden gefaciliteerd door het creëren van ervaringen, voorbeelden en systemen die de gewenste cultuur ondersteunen en versterken.

De eerste culturele verandering richt zich op het verlagen van machtsafstand door het promoten van meer egalitaire interacties tussen verschillende organisatieniveaus. Dit wordt gerealiseerd door leidinggevenden te trainen in participatieve leiderschapsstijlen, open-deur beleid te implementeren en regelmatige feedback-sessies te organiseren waarbij medewerkers van alle niveaus input kunnen geven. Symbolische acties zoals het elimineren van statusverschillen in faciliteiten en het gebruik van informelere communicatiestijlen ondersteunen deze verandering.

De tweede culturele interventie betreft het verhogen van tolerantie voor onzekerheid en experimenteren. Dit wordt bereikt door het creëren van veilige ruimtes voor het uitproberen van nieuwe ideeën, het vieren van leerprocessen (ook wanneer experimenten niet slagen) en het implementeren van "fail-fast" principes waarbij snelle iteraties en aanpassingen worden aangemoedigd. Training in design thinking en agile methodieken ondersteunt deze cultuurverandering.

De derde verandering richt zich op het versterken van langetermijndenken door het communiceren van de strategische voordelen van Six Sigma, het instellen van langetermijn-KPI's naast korte-termijn metrieken en het investeren in medewerkerontwikkeling en competentiebuilding. Verhalen en case studies van succesvolle langetermijn verbeteringsinitiatieven helpen bij het internaliseren van deze oriëntatie.

De vierde culturele aanpassing behelst het verhogen van toegeeflijkheid en psychologische veiligheid door het creëren van een omgeving waarin medewerkers zich vrij voelen om vragen te stellen, fouten toe te geven en nieuwe ideeën te delen. Dit wordt ondersteund door training van leidinggevenden in coaching vaardigheden, het implementeren van constructieve feedback-cultuur en het erkennen en belonen van openheid en transparantie.

De vijfde verandering richt zich op het balanceren van individualisme en collectivisme door het waarderen van individuele expertise binnen teamcontexten. Dit wordt gerealiseerd door team-gebaseerde projecten te promoten, cross-training te implementeren en beloningssystemen aan te passen om zowel individuele bijdragen als teamresultaten te erkennen."""
    
    doc.add_paragraph(cultuur_veranderingen_text)
    
    # Voeg tabel toe voor cultuurveranderingen
    doc.add_paragraph('\nTabel 5.2: Cultuurverandering Interventies per Hofstede Dimensie')
    
    table2 = doc.add_table(rows=1, cols=4)
    table2.style = 'Table Grid'
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Cultuurdimensie', 'Gewenste Verandering', 'Interventies', 'Meetbare Indicatoren']
    for i, header in enumerate(headers):
        table2.rows[0].cells[i].text = header
    
    # Cultuur interventies data
    cultuur_interventies = [
        ('Machtsafstand', 'Van 65 naar 45', 'Participatief leiderschap, open communicatie', 'Aantal bottom-up voorstellen, feedback scores'),
        ('Onzekerheidsvermijding', 'Van 75 naar 55', 'Experimenteerruimte, fail-fast cultuur', 'Aantal experimenten, leerinitiatieven'),
        ('Langetermijnoriëntatie', 'Van 50 naar 70', 'Strategische communicatie, investering ontwikkeling', 'Commitment lange termijn projecten'),
        ('Toegeeflijkheid', 'Van 45 naar 65', 'Psychologische veiligheid, coaching', 'Openheid scores, innovatie-indicatoren'),
        ('Masculiniteit', 'Van 60 naar 40', 'Kwaliteitsfocus, zorgvuldigheid', 'Kwaliteitsmetrieken, samenwerkingsscores')
    ]
    
    for data in cultuur_interventies:
        row_cells = table2.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Sla het bijgewerkte document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK5_DEEL1.docx')
    print("Hoofdstuk 5 deel 1 (voorbereidend) toegevoegd aan het academische rapport!")

if __name__ == "__main__":
    add_hoofdstuk5_deel1()

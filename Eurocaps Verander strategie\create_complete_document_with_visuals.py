import os
import matplotlib.pyplot as plt
import numpy as np
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
import matplotlib.patches as patches

def create_all_visuals():
    """Create all required visuals for the document"""
    
    # Set style for all plots
    plt.style.use('default')
    
    # 1. Boonstra's Change Strategies
    create_boonstra_strategies()
    
    # 2. De Caluwé Color Model
    create_caluwe_colors()
    
    # 3. Gap Analysis Model
    create_gap_analysis()
    
    # 4. Hofstede Dimensions
    create_hofstede_dimensions()
    
    # 5. <PERSON>tter 8-Step Model
    create_kotter_model()
    
    # 6. Stakeholder Matrix
    create_stakeholder_matrix()
    
    # 7. <PERSON><PERSON><PERSON>-Ross Change Curve
    create_kubler_ross_curve()
    
    # 8. Mintzberg Decision Matrix
    create_mintzberg_matrix()
    
    # 9. Boonstra Decision Matrix
    create_boonstra_decision_matrix()
    
    # 10. DMAIC-Kotter Integration
    create_dmaic_kotter_integration()

def create_boonstra_strategies():
    """Visual 1: Boonstra's 5 Change Strategies"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    strategies = ['Ontwikkeling', 'Ingrijpend', 'Macht', 'Onderhandeling', 'Verleiding']
    characteristics = ['Participatief\nLangzaam\nDuurzaam', 'Top-down\nSnel\nWeerstand', 
                      'Controle\nHiërarchie\nCompliance', 'Compromis\nTijd\nDraagvlak', 
                      'Inspiratie\nMotivatie\nCharisma']
    
    colors = ['#4CAF50', '#F44336', '#FF9800', '#2196F3', '#9C27B0']
    
    # Create boxes for each strategy
    for i, (strategy, char, color) in enumerate(zip(strategies, characteristics, colors)):
        rect = patches.Rectangle((i*2, 0), 1.8, 3, linewidth=2, edgecolor='black', 
                               facecolor=color, alpha=0.7)
        ax.add_patch(rect)
        
        # Add strategy name
        ax.text(i*2 + 0.9, 2.5, strategy, ha='center', va='center', 
               fontsize=12, fontweight='bold', color='white')
        
        # Add characteristics
        ax.text(i*2 + 0.9, 1.5, char, ha='center', va='center', 
               fontsize=9, color='white')
    
    ax.set_xlim(-0.5, 10)
    ax.set_ylim(-0.5, 3.5)
    ax.set_title('Boonstra\'s Vijf Veranderstrategieën', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_1_Boonstra_Veranderstrategieen.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_caluwe_colors():
    """Visual 2: De Caluwé Color Model"""
    fig, ax = plt.subplots(figsize=(12, 10))
    
    colors = ['blue', 'yellow', 'red', 'green', 'white']
    labels = ['Blauwdruk\n(Rationeel)', 'Geeldruk\n(Politiek)', 'Rooddruk\n(Relationeel)', 
              'Groendruk\n(Lerend)', 'Witdruk\n(Emergent)']
    descriptions = ['Feiten & Cijfers\nPlanmatig', 'Macht & Belangen\nOnderhandeling', 
                   'Relaties & Emoties\nMotivatie', 'Leren & Ontwikkeling\nExperiment', 
                   'Complexiteit\nOnvoorspelbaarheid']
    
    # Create circular arrangement
    angles = np.linspace(0, 2*np.pi, 6)[:-1]  # 5 colors
    radius = 3
    
    for i, (color, label, desc, angle) in enumerate(zip(colors, labels, descriptions, angles)):
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # Create circle for each color
        circle = patches.Circle((x, y), 1.2, facecolor=color, alpha=0.7, 
                              edgecolor='black', linewidth=2)
        ax.add_patch(circle)
        
        # Add label
        ax.text(x, y+0.3, label, ha='center', va='center', 
               fontsize=11, fontweight='bold', color='white' if color != 'white' else 'black')
        
        # Add description
        ax.text(x, y-0.3, desc, ha='center', va='center', 
               fontsize=9, color='white' if color != 'white' else 'black')
    
    ax.set_xlim(-5, 5)
    ax.set_ylim(-5, 5)
    ax.set_title('De Caluwé\'s Kleurenmodel voor Verandering', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_2_Caluwe_Kleurenmodel.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_gap_analysis():
    """Visual 3: Gap Analysis Model"""
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Current state
    current_rect = patches.Rectangle((1, 2), 3, 2, linewidth=2, edgecolor='red', 
                                   facecolor='lightcoral', alpha=0.7)
    ax.add_patch(current_rect)
    ax.text(2.5, 3, 'HUIDIGE\nSITUATIE\n(AS-IS)', ha='center', va='center', 
           fontsize=12, fontweight='bold')
    
    # Gap
    gap_rect = patches.Rectangle((5, 1), 2, 4, linewidth=2, edgecolor='orange', 
                               facecolor='lightyellow', alpha=0.7)
    ax.add_patch(gap_rect)
    ax.text(6, 3, 'GAP\n\nVerschil\ntussen\nhuidig\nen\ngewenst', ha='center', va='center', 
           fontsize=10, fontweight='bold')
    
    # Desired state
    desired_rect = patches.Rectangle((8, 2), 3, 2, linewidth=2, edgecolor='green', 
                                   facecolor='lightgreen', alpha=0.7)
    ax.add_patch(desired_rect)
    ax.text(9.5, 3, 'GEWENSTE\nSITUATIE\n(TO-BE)', ha='center', va='center', 
           fontsize=12, fontweight='bold')
    
    # Arrows
    ax.arrow(4.2, 3, 0.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    ax.arrow(7.2, 3, 0.6, 0, head_width=0.2, head_length=0.2, fc='black', ec='black')
    
    # Action steps
    ax.text(6, 0.5, 'ACTIESTAPPEN:\n• Analyse\n• Planning\n• Implementatie\n• Evaluatie', 
           ha='center', va='center', fontsize=10, 
           bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 5.5)
    ax.set_title('Gap-Analyse Model', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_3_Gap_Analyse_Model.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_hofstede_dimensions():
    """Visual 4: Hofstede's Cultural Dimensions for Euro Caps"""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    dimensions = ['Machtsafstand', 'Individualisme', 'Masculiniteit', 
                 'Onzekerheids-\nvermijding', 'Lange termijn\ngerichtheid', 'Toegeeflijkheid']
    euro_caps_scores = [3, 3, 4, 4, 3, 3]  # Scores for Euro Caps
    
    bars = ax.bar(dimensions, euro_caps_scores, color='skyblue', alpha=0.8, 
                 edgecolor='navy', linewidth=2)
    
    # Add value labels on bars
    for bar, score in zip(bars, euro_caps_scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{score}', ha='center', va='bottom', fontweight='bold', fontsize=12)
    
    ax.set_ylim(0, 5)
    ax.set_ylabel('Score (1-5)', fontsize=12, fontweight='bold')
    ax.set_title('Hofstede\'s Cultuurdimensies - Euro Caps Analyse', fontsize=16, fontweight='bold', pad=20)
    ax.grid(axis='y', alpha=0.3)
    
    # Rotate x-axis labels for better readability
    plt.xticks(rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig('Visual_4_Hofstede_Cultuurdimensies.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_kotter_model():
    """Visual 5: Kotter's 8-Step Model"""
    fig, ax = plt.subplots(figsize=(12, 10))
    
    steps = [
        '1. Urgentiebesef\ncreëren',
        '2. Leidende coalitie\nvormen',
        '3. Visie en strategie\nontwikkelen',
        '4. Visie\ncommuniceren',
        '5. Draagvlak voor\nactie creëren',
        '6. Korte termijn\nsuccessen genereren',
        '7. Verbeteringen\nconsolideren',
        '8. Nieuwe benaderingen\nverankeren'
    ]
    
    # Create circular flow
    angles = np.linspace(0, 2*np.pi, 9)[:-1]  # 8 steps
    radius = 4
    
    for i, (step, angle) in enumerate(zip(steps, angles)):
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        
        # Create circle for each step
        circle = patches.Circle((x, y), 1, facecolor='lightblue', alpha=0.8, 
                              edgecolor='darkblue', linewidth=2)
        ax.add_patch(circle)
        
        # Add step text
        ax.text(x, y, step, ha='center', va='center', 
               fontsize=9, fontweight='bold')
        
        # Add arrows between steps
        if i < len(steps) - 1:
            next_angle = angles[i + 1]
            next_x = radius * np.cos(next_angle)
            next_y = radius * np.sin(next_angle)
            
            # Calculate arrow position
            arrow_start_x = x + 0.8 * np.cos(next_angle - angle)
            arrow_start_y = y + 0.8 * np.sin(next_angle - angle)
            arrow_end_x = next_x - 0.8 * np.cos(next_angle - angle)
            arrow_end_y = next_y - 0.8 * np.sin(next_angle - angle)
            
            ax.annotate('', xy=(arrow_end_x, arrow_end_y), xytext=(arrow_start_x, arrow_start_y),
                       arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    # Connect last step to first
    last_x = radius * np.cos(angles[-1])
    last_y = radius * np.sin(angles[-1])
    first_x = radius * np.cos(angles[0])
    first_y = radius * np.sin(angles[0])
    
    ax.annotate('', xy=(first_x - 0.8, first_y), xytext=(last_x + 0.8, last_y),
               arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))
    
    ax.set_xlim(-6, 6)
    ax.set_ylim(-6, 6)
    ax.set_title('Kotter\'s 8-Stappenmodel voor Organisatieverandering', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('Visual_5_Kotter_8_Stappenmodel.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_stakeholder_matrix():
    """Visual 6: Stakeholder Power-Interest Matrix for Euro Caps"""
    fig, ax = plt.subplots(figsize=(12, 10))

    # Define Euro Caps stakeholders with their power and interest levels
    stakeholders = {
        'CEO\n(Nils Clement)': (5, 5),
        'Manager Bedrijfsvoering\n(Servé Bosland)': (4, 5),
        'Manager ICT\n(Erik Dekker)': (3, 3),
        'Productiemanager\n(Maik Ritter)': (3, 4),
        'Productiemanager\n(Maria Stanić)': (3, 4),
        'Hoofd Kwaliteitsbeheer\n(Kees Keurig)': (3, 4),
        'HR Manager\n(Uwe Regel)': (2, 2),
        'Hoofd Financiën\n(Berkan Arrindell)': (3, 3),
        'Manager Logistiek\n(Rijk Wegen)': (2, 3),
        'Manager Inkoop\n(Ko Jager)': (2, 3),
        'Productiemedewerkers\n(Ismail, Samantha)': (1, 3),
        'Klanten\n(Retailers)': (4, 5),
        'Leveranciers': (2, 2),
        'NVWA': (4, 3)
    }

    # Create quadrants
    ax.axhline(y=2.5, color='gray', linestyle='--', alpha=0.7)
    ax.axvline(x=2.5, color='gray', linestyle='--', alpha=0.7)

    # Add quadrant labels
    ax.text(1.25, 4.5, 'SUBJECTS\n(Hoog belang,\nLage macht)', ha='center', va='center',
           fontsize=10, fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightcoral', alpha=0.7))
    ax.text(3.75, 4.5, 'KEY PLAYERS\n(Hoog belang,\nHoge macht)', ha='center', va='center',
           fontsize=10, fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))
    ax.text(1.25, 1.5, 'CROWD\n(Laag belang,\nLage macht)', ha='center', va='center',
           fontsize=10, fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
    ax.text(3.75, 1.5, 'CONTEXT SETTERS\n(Laag belang,\nHoge macht)', ha='center', va='center',
           fontsize=10, fontweight='bold', bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.7))

    # Plot stakeholders
    colors = {'CEO\n(Nils Clement)': 'red', 'Manager Bedrijfsvoering\n(Servé Bosland)': 'red',
              'Klanten\n(Retailers)': 'red', 'NVWA': 'orange'}

    for stakeholder, (power, interest) in stakeholders.items():
        color = colors.get(stakeholder, 'blue')
        ax.scatter(power, interest, s=200, c=color, alpha=0.7, edgecolors='black', linewidth=2)
        ax.annotate(stakeholder, (power, interest), xytext=(5, 5), textcoords='offset points',
                   fontsize=8, ha='left', va='bottom',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

    ax.set_xlim(0.5, 5.5)
    ax.set_ylim(0.5, 5.5)
    ax.set_xlabel('MACHT (Power)', fontsize=12, fontweight='bold')
    ax.set_ylabel('BELANG (Interest)', fontsize=12, fontweight='bold')
    ax.set_title('Stakeholder Power-Interest Matrix - Euro Caps', fontsize=16, fontweight='bold', pad=20)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('Visual_6_Stakeholderanalyse_Matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_kubler_ross_curve():
    """Visual 7: Kübler-Ross Change Curve"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # Define the curve points
    x = np.linspace(0, 10, 100)
    y = -2 * np.exp(-0.5 * (x - 2)**2) + 1.5 * np.exp(-0.3 * (x - 7)**2) + 0.5

    ax.plot(x, y, 'b-', linewidth=3, label='Emotionele Reactie')

    # Mark the phases
    phases = [
        (1, -1.5, 'Ontkenning'),
        (3, -0.8, 'Woede/\nFrustratie'),
        (4.5, -0.3, 'Onderhandeling'),
        (6, 0.2, 'Depressie'),
        (8.5, 1.2, 'Acceptatie')
    ]

    for x_pos, y_pos, phase in phases:
        ax.annotate(phase, xy=(x_pos, y_pos), xytext=(x_pos, y_pos + 0.8),
                   arrowprops=dict(arrowstyle='->', color='red', lw=2),
                   fontsize=11, fontweight='bold', ha='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.8))

    # Add horizontal line for neutral emotional state
    ax.axhline(y=0, color='gray', linestyle='--', alpha=0.7, label='Neutrale Staat')

    ax.set_xlim(0, 10)
    ax.set_ylim(-2.5, 2)
    ax.set_xlabel('Tijd', fontsize=12, fontweight='bold')
    ax.set_ylabel('Emotionele Reactie', fontsize=12, fontweight='bold')
    ax.set_title('Kübler-Ross Verandercurve - Emotionele Fasen bij Verandering', fontsize=16, fontweight='bold', pad=20)
    ax.grid(True, alpha=0.3)
    ax.legend()

    plt.tight_layout()
    plt.savefig('Visual_7_Kubler_Ross_Verandercurve.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_mintzberg_matrix():
    """Visual 8: Mintzberg Decision Matrix"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # Data for the matrix
    structures = ['Ondernemende\norganisatie', 'Machine-\norganisatie', 'Professionele\norganisatie',
                 'Innovatieve\norganisatie', 'Hybride\n(Euro Caps)']
    criteria = ['Standaardisatie', 'Innovatie', 'Flexibiliteit', 'Technologie', 'Efficiëntie']

    # Scores matrix
    scores = np.array([
        [2, 5, 4, 3, 2],  # Ondernemende
        [5, 1, 1, 3, 5],  # Machine
        [4, 2, 2, 2, 4],  # Professionele
        [1, 5, 5, 4, 1],  # Innovatieve
        [4, 4, 3, 4, 4]   # Hybride (Euro Caps)
    ])

    # Create heatmap
    im = ax.imshow(scores, cmap='RdYlGn', aspect='auto', vmin=1, vmax=5)

    # Add text annotations
    for i in range(len(structures)):
        for j in range(len(criteria)):
            text = ax.text(j, i, scores[i, j], ha="center", va="center",
                         color="black", fontweight='bold', fontsize=12)

    # Set ticks and labels
    ax.set_xticks(np.arange(len(criteria)))
    ax.set_yticks(np.arange(len(structures)))
    ax.set_xticklabels(criteria, fontsize=11)
    ax.set_yticklabels(structures, fontsize=11)

    # Rotate the tick labels and set their alignment
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # Add colorbar
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Score (1-5)', fontsize=12, fontweight='bold')

    ax.set_title('Mintzberg Beslissingsmatrix - Organisatiestructuren', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('Visual_8_Beslissingsmatrix_Mintzberg.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_boonstra_decision_matrix():
    """Visual 9: Boonstra Decision Matrix"""
    fig, ax = plt.subplots(figsize=(12, 8))

    # Data for the matrix
    strategies = ['Ontwikkeling', 'Ingrijpend', 'Macht', 'Onderhandeling', 'Verleiding']
    criteria = ['Past bij cultuur', 'Past bij structuur', 'Weinig weerstand', 'Sterke samenwerking', 'Langetermijn effect']

    # Scores matrix
    scores = np.array([
        [4, 4, 4, 5, 5],  # Ontwikkeling
        [2, 3, 2, 3, 4],  # Ingrijpend
        [1, 2, 3, 2, 3],  # Macht
        [4, 4, 4, 4, 4],  # Onderhandeling
        [3, 3, 3, 3, 3]   # Verleiding
    ])

    # Create heatmap
    im = ax.imshow(scores, cmap='RdYlGn', aspect='auto', vmin=1, vmax=5)

    # Add text annotations
    for i in range(len(strategies)):
        for j in range(len(criteria)):
            ax.text(j, i, scores[i, j], ha="center", va="center",
                   color="black", fontweight='bold', fontsize=12)

    # Set ticks and labels
    ax.set_xticks(np.arange(len(criteria)))
    ax.set_yticks(np.arange(len(strategies)))
    ax.set_xticklabels(criteria, fontsize=11)
    ax.set_yticklabels(strategies, fontsize=11)

    # Rotate the tick labels
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")

    # Add colorbar
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('Score (1-5)', fontsize=12, fontweight='bold')

    ax.set_title('Boonstra Beslissingsmatrix - Veranderstrategieën voor Euro Caps', fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig('Visual_9_Boonstra_Beslissingsmatrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_dmaic_kotter_integration():
    """Visual 10: DMAIC-Kotter Integration"""
    fig, ax = plt.subplots(figsize=(14, 10))

    # DMAIC phases
    dmaic_phases = ['DEFINE', 'MEASURE', 'ANALYZE', 'IMPROVE', 'CONTROL']
    dmaic_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    # Kotter steps
    kotter_steps = [
        'Stap 1-2: Urgentie & Coalitie',
        'Stap 3-4: Visie & Communicatie',
        'Stap 5: Draagvlak creëren',
        'Stap 6-7: Successen & Consolidatie',
        'Stap 8: Verankering'
    ]

    # Create DMAIC cycle
    angles = np.linspace(0, 2*np.pi, 6)[:-1]  # 5 phases
    radius = 3

    for i, (phase, color, angle) in enumerate(zip(dmaic_phases, dmaic_colors, angles)):
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)

        # Create circle for each phase
        circle = patches.Circle((x, y), 0.8, facecolor=color, alpha=0.8,
                              edgecolor='black', linewidth=2)
        ax.add_patch(circle)

        # Add phase text
        ax.text(x, y, phase, ha='center', va='center',
               fontsize=10, fontweight='bold')

        # Add Kotter step below
        ax.text(x, y-1.5, kotter_steps[i], ha='center', va='center',
               fontsize=9, bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        # Add arrows between phases
        if i < len(dmaic_phases) - 1:
            next_angle = angles[i + 1]
            next_x = radius * np.cos(next_angle)
            next_y = radius * np.sin(next_angle)

            # Calculate arrow position
            arrow_start_x = x + 0.6 * np.cos(next_angle - angle)
            arrow_start_y = y + 0.6 * np.sin(next_angle - angle)
            arrow_end_x = next_x - 0.6 * np.cos(next_angle - angle)
            arrow_end_y = next_y - 0.6 * np.sin(next_angle - angle)

            ax.annotate('', xy=(arrow_end_x, arrow_end_y), xytext=(arrow_start_x, arrow_start_y),
                       arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))

    # Connect last phase to first
    last_x = radius * np.cos(angles[-1])
    last_y = radius * np.sin(angles[-1])
    first_x = radius * np.cos(angles[0])
    first_y = radius * np.sin(angles[0])

    ax.annotate('', xy=(first_x - 0.6, first_y), xytext=(last_x + 0.6, last_y),
               arrowprops=dict(arrowstyle='->', lw=2, color='darkblue'))

    # Add title in center
    ax.text(0, 0, 'DMAIC\n+\nKOTTER\nINTEGRATIE', ha='center', va='center',
           fontsize=12, fontweight='bold',
           bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.9, edgecolor='black'))

    ax.set_xlim(-5, 5)
    ax.set_ylim(-5, 5)
    ax.set_title('Integratie van DMAIC-cyclus met Kotter\'s 8-stappenmodel', fontsize=16, fontweight='bold', pad=20)
    ax.axis('off')

    plt.tight_layout()
    plt.savefig('Visual_10_DMAIC_Kotter_Integratie.png', dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    print("=== Creating All Visuals ===")
    create_all_visuals()
    print("All visuals created successfully!")
    print("\n=== VISUALS CREATED ===")
    print("✅ Visual_1_Boonstra_Veranderstrategieen.png")
    print("✅ Visual_2_Caluwe_Kleurenmodel.png")
    print("✅ Visual_3_Gap_Analyse_Model.png")
    print("✅ Visual_4_Hofstede_Cultuurdimensies.png")
    print("✅ Visual_5_Kotter_8_Stappenmodel.png")
    print("✅ Visual_6_Stakeholderanalyse_Matrix.png")
    print("✅ Visual_7_Kubler_Ross_Verandercurve.png")
    print("✅ Visual_8_Beslissingsmatrix_Mintzberg.png")
    print("✅ Visual_9_Boonstra_Beslissingsmatrix.png")
    print("✅ Visual_10_DMAIC_Kotter_Integratie.png")

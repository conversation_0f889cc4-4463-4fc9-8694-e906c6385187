#!/usr/bin/env python3
"""
Script om managementsamenvatting toe te voegen na voorwoord
"""

from docx import Document

def add_management_samenvatting_after_voorwoord():
    """Voegt managementsamenvatting toe na voorwoord"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_COMPLEET.docx')
    
    print("Bezig met toevoegen managementsamenvatting na voorwoord...")
    
    # Zoek voorwoord en voeg managementsamenvatting erna toe
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden voorwoord heading op regel {i}")
            
            # Zoek einde van voorwoord content
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if (doc.paragraphs[j].style.name.startswith('Heading 1') and 
                    ('Inhoudsopgave' in next_text or 'Hoofdstuk 1' in next_text)):
                    
                    print(f"Einde voorwoord gevonden op regel {j}")
                    
                    # Voeg managementsamenvatting toe voor deze heading
                    management_heading = doc.paragraphs[j].insert_paragraph_before('Managementsamenvatting')
                    management_heading.style = doc.styles['Heading 1']
                    
                    management_samenvatting = """Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie. Het onderzoek is uitgevoerd middels deskresearch en richt zich op het optimaliseren van zowel organisatiestructuur als organisatiecultuur.

Het theoretisch kader omvat zes kernmodellen voor verandermanagement. Boonstra's veranderstrategieën bieden verschillende benaderingen voor organisatorische transformatie, waarbij de ontwikkelingsstrategie het meest geschikt blijkt voor Euro Caps. De Caluwé's veranderkleuren model onderscheidt vijf paradigma's, waarbij een combinatie van blauwdruk- en groendrukdenken wordt aanbevolen. De gap-analyse met Hofstede's cultuurdimensies toont significante verschillen tussen huidige en gewenste organisatiecultuur, met name op het gebied van machtsafstand en onzekerheidsvermijding. Kotter's achtstappenmodel biedt een gestructureerd raamwerk voor veranderingsimplementatie. Stakeholderanalyse identificeert kritieke partijen en hun belangen, terwijl de Kübler-Ross verandercurve inzicht biedt in emotionele aspecten van verandering.

De analyse van de huidige situatie toont een machinebureaucratie met sterke hiërarchie en hoge onzekerheidsvermijding die Six Sigma implementatie belemmert. De organisatiecultuur wordt gekenmerkt door hoge machtsafstand, beperkte participatie en korte termijn focus, wat conflicteert met de vereisten voor continue verbetering.

De gewenste situatie omvat een meer flexibele organisatiestructuur met cross-functionele teams en gedecentraliseerde besluitvorming. De cultuurverandering richt zich op het verlagen van machtsafstand, het stimuleren van participatie en het ontwikkelen van lange termijn oriëntatie ter ondersteuning van Six Sigma principes.

De veranderstrategie combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in een gefaseerde implementatie over 21 maanden. De aanpak omvat drie hoofdfasen: voorbereiding, implementatie en verankering, waarbij elke fase specifieke interventies bevat voor verschillende stakeholdergroepen.

Het communicatieplan ondersteunt de verandering door gerichte communicatie naar verschillende stakeholders, waarbij gebruik wordt gemaakt van diverse kanalen en een gefaseerde benadering die aansluit bij de emotionele reis van medewerkers.

De conclusie bevestigt dat een integrale veranderstrategie noodzakelijk is voor succesvolle Six Sigma implementatie bij Euro Caps. De aanbeveling luidt om de voorgestelde strategie te implementeren met focus op zowel structurele als culturele veranderingen, ondersteund door intensief change management en continue monitoring van de voortgang."""
                    
                    management_content = doc.paragraphs[j].insert_paragraph_before(management_samenvatting)
                    management_content.style = doc.styles['Normal']
                    
                    print("✅ Managementsamenvatting toegevoegd na voorwoord")
                    break
                j += 1
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MET_MANAGEMENT_SAMENVATTING.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_MET_MANAGEMENT_SAMENVATTING.docx")
    print("✅ Managementsamenvatting toegevoegd")
    print("✅ Document structuur nu: Voorwoord → Managementsamenvatting → Inhoudsopgave → Hoofdstukken")

if __name__ == "__main__":
    add_management_samenvatting_after_voorwoord()

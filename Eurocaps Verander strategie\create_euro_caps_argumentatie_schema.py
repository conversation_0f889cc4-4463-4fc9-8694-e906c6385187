#!/usr/bin/env python3
"""
Script om het juiste Euro Caps argumentatieschema visual te maken
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

def create_euro_caps_argumentatie_schema():
    """Maakt het juiste Euro Caps argumentatieschema volgens het voorbeeld"""
    
    fig, ax = plt.subplots(figsize=(14, 18))
    
    # Kleuren volgens het voorbeeld
    colors = {
        'standpunt': '#4CAF50',      # Groen
        'hoofdarg1': '#4CAF50',      # Groen  
        'hoofdarg2': '#00BCD4',      # Cyaan/Turquoise
        'hoofdarg3': '#3F51B5',      # Donkerblauw/Indigo
        'tegenarg': '#9C27B0',       # Paars
        'weerlegging': '#E91E63',    # Roze/Magenta
        'text': 'black',
        'text_white': 'white'
    }
    
    # STANDPUNT (top)
    standpunt_box = FancyBboxPatch((1, 16), 12, 1.5, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['standpunt'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(standpunt_box)
    
    ax.text(7, 16.75, 'Standpunt:', ha='center', va='center', 
            fontsize=12, fontweight='bold', color=colors['text_white'])
    ax.text(7, 16.25, 'Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur\nen cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.', 
            ha='center', va='center', fontsize=9, color=colors['text'], 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.8))
    
    # Pijl naar beneden
    ax.arrow(7, 15.8, 0, -0.4, head_width=0.25, head_length=0.15, fc=colors['standpunt'], ec=colors['standpunt'], linewidth=3)
    
    # HOOFDARGUMENT 1 (Groen)
    hoofdarg1_box = FancyBboxPatch((0.5, 13.5), 13, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg1'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg1_box)
    
    ax.text(7, 14.1, 'Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 1 (3 kolommen)
    ax.text(2.5, 12.7, 'Machinebureaucratie met sterke\nhiërarchie beperkt cross-functionele\nsamenwerking voor Six Sigma projecten.', 
            ha='center', va='center', fontsize=8, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(7, 12.7, 'Gecentraliseerde besluitvorming vertraagt\nprocesverbeteringen en beperkt\nmedewerkerparticipatie.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(11.5, 12.7, 'Functionele silo\'s belemmeren\nholistische procesoptimalisatie\ndie Six Sigma vereist.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar hoofdargument 2
    ax.arrow(7, 12, 0, -0.4, head_width=0.25, head_length=0.15, fc=colors['hoofdarg2'], ec=colors['hoofdarg2'], linewidth=3)
    
    # HOOFDARGUMENT 2 (Cyaan)
    hoofdarg2_box = FancyBboxPatch((0.5, 10), 13, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg2'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg2_box)
    
    ax.text(7, 10.6, 'Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 2 (3 kolommen)
    ax.text(2.5, 9.2, 'Hoge onzekerheidsvermijding (80/100)\nbelemmert experimenteren en innovatie\nnodig voor Six Sigma.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(7, 9.2, 'Hoge machtsafstand (75/100) beperkt\nmedewerkerparticipatie in\nverbeterprocessen.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(11.5, 9.2, 'Korte termijn focus (40/100)\nconflicteert met lange termijn\nverbeterdoelstellingen Six Sigma.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar hoofdargument 3
    ax.arrow(7, 8.5, 0, -0.4, head_width=0.25, head_length=0.15, fc=colors['hoofdarg3'], ec=colors['hoofdarg3'], linewidth=3)
    
    # HOOFDARGUMENT 3 (Donkerblauw)
    hoofdarg3_box = FancyBboxPatch((0.5, 6.5), 13, 1.2, 
                                   boxstyle="round,pad=0.1", 
                                   facecolor=colors['hoofdarg3'], 
                                   edgecolor='black', linewidth=2)
    ax.add_patch(hoofdarg3_box)
    
    ax.text(7, 7.1, 'Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten hoofdargument 3 (3 kolommen)
    ax.text(2.5, 5.7, 'Boonstra\'s ontwikkelingsstrategie\nfaciliteert participatieve verandering\ndie aansluit bij Six Sigma principes.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(7, 5.7, 'Kotter\'s achtstappenmodel biedt\nbewezen raamwerk voor complexe\norganisatorische transformaties.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(11.5, 5.7, 'Gecombineerde structuur- en\ncultuurverandering creëert duurzame\nbasis voor continue kwaliteitsverbetering.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar tegenargument
    ax.arrow(7, 5, 0, -0.4, head_width=0.25, head_length=0.15, fc=colors['tegenarg'], ec=colors['tegenarg'], linewidth=3)
    
    # TEGENARGUMENT (Paars)
    tegenarg_box = FancyBboxPatch((0.5, 3), 13, 1.2, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor=colors['tegenarg'], 
                                  edgecolor='black', linewidth=2)
    ax.add_patch(tegenarg_box)
    
    ax.text(7, 3.6, 'Tegenargument: Gefaseerde aanpak is voldoende', 
            ha='center', va='center', fontsize=11, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten tegenargument (3 kolommen)
    ax.text(2.5, 2.2, 'Geleidelijke aanpassing van alleen\nstructuur of cultuur vereist minder\nresources en creëert minder weerstand.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(7, 2.2, 'Beperkte verandering minimaliseert\nrisico op operationele disruption\ntijdens implementatie.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(11.5, 2.2, 'Incrementele verbeteringen kunnen\nook tot kwaliteitsverbetering\nleiden zonder grote transformatie.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    # Pijl naar weerlegging
    ax.arrow(7, 1.5, 0, -0.4, head_width=0.25, head_length=0.15, fc=colors['weerlegging'], ec=colors['weerlegging'], linewidth=3)
    
    # WEERLEGGING (Roze/Magenta)
    weerlegging_box = FancyBboxPatch((0.5, -0.5), 13, 1.2, 
                                     boxstyle="round,pad=0.1", 
                                     facecolor=colors['weerlegging'], 
                                     edgecolor='black', linewidth=2)
    ax.add_patch(weerlegging_box)
    
    ax.text(7, 0.1, 'Weerlegging: Geïsoleerde veranderingen zijn onvoldoende voor duurzame transformatie', 
            ha='center', va='center', fontsize=10, fontweight='bold', color=colors['text_white'])
    
    # Sub-argumenten weerlegging (2 kolommen)
    ax.text(3.5, -1.3, 'Structuurverandering zonder cultuurverandering leidt tot weerstand\nen terugval naar oude patronen. Six Sigma vereist fundamentele\ntransformatie voor duurzaam succes.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.text(10.5, -1.3, 'Cultuurverandering zonder structurele ondersteuning blijft\noppervlakkig en niet-duurzaam. Integrale aanpak is noodzakelijk\nvoor effectieve Six Sigma implementatie.', 
            ha='center', va='center', fontsize=8,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.9))
    
    ax.set_xlim(0, 14)
    ax.set_ylim(-2, 18)
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('argumentatie_schema_euro_caps_SPECIFIEK.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Euro Caps specifiek argumentatieschema visual aangemaakt: argumentatie_schema_euro_caps_SPECIFIEK.png")

if __name__ == "__main__":
    print("Aanmaken van Euro Caps specifiek argumentatieschema...")
    create_euro_caps_argumentatie_schema()
    print("Euro Caps argumentatieschema voltooid!")

#!/usr/bin/env python3
"""
Script om managementsamenvatting te vullen en Hofstede grafiek te fixen
"""

from docx import Document
from docx.shared import Inches
import os

def fix_management_en_hofstede_grafiek():
    """Vult managementsamenvatting en fixt Hofstede grafiek"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MET_MANAGEMENT_SAMENVATTING.docx')
    
    print("Bezig met vullen managementsamenvatting en fixen Hofstede grafiek...")
    
    # 1. Zoek en vul managementsamenvatting
    print("\n1. Zoeken en vullen managementsamenvatting...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden managementsamenvatting heading op regel {i}")
            
            # Check volgende paragraaf
            if i + 1 < len(doc.paragraphs):
                next_text = doc.paragraphs[i + 1].text.strip()
                print(f"Volgende paragraaf lengte: {len(next_text)}")
                
                if len(next_text) < 100:  # Lege of korte content
                    print("Managementsamenvatting is leeg, vullen...")
                    
                    management_samenvatting = """Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie. Het onderzoek is uitgevoerd middels deskresearch en richt zich op het optimaliseren van zowel organisatiestructuur als organisatiecultuur.

Het theoretisch kader omvat zes kernmodellen voor verandermanagement. Boonstra's veranderstrategieën bieden verschillende benaderingen voor organisatorische transformatie, waarbij de ontwikkelingsstrategie het meest geschikt blijkt voor Euro Caps. De Caluwé's veranderkleuren model onderscheidt vijf paradigma's, waarbij een combinatie van blauwdruk- en groendrukdenken wordt aanbevolen. De gap-analyse met Hofstede's cultuurdimensies toont significante verschillen tussen huidige en gewenste organisatiecultuur, met name op het gebied van machtsafstand en onzekerheidsvermijding. Kotter's achtstappenmodel biedt een gestructureerd raamwerk voor veranderingsimplementatie. Stakeholderanalyse identificeert kritieke partijen en hun belangen, terwijl de Kübler-Ross verandercurve inzicht biedt in emotionele aspecten van verandering.

De analyse van de huidige situatie toont een machinebureaucratie met sterke hiërarchie en hoge onzekerheidsvermijding die Six Sigma implementatie belemmert. De organisatiecultuur wordt gekenmerkt door hoge machtsafstand, beperkte participatie en korte termijn focus, wat conflicteert met de vereisten voor continue verbetering.

De gewenste situatie omvat een meer flexibele organisatiestructuur met cross-functionele teams en gedecentraliseerde besluitvorming. De cultuurverandering richt zich op het verlagen van machtsafstand, het stimuleren van participatie en het ontwikkelen van lange termijn oriëntatie ter ondersteuning van Six Sigma principes.

De veranderstrategie combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in een gefaseerde implementatie over 21 maanden. De aanpak omvat drie hoofdfasen: voorbereiding, implementatie en verankering, waarbij elke fase specifieke interventies bevat voor verschillende stakeholdergroepen.

Het communicatieplan ondersteunt de verandering door gerichte communicatie naar verschillende stakeholders, waarbij gebruik wordt gemaakt van diverse kanalen en een gefaseerde benadering die aansluit bij de emotionele reis van medewerkers.

De conclusie bevestigt dat een integrale veranderstrategie noodzakelijk is voor succesvolle Six Sigma implementatie bij Euro Caps. De aanbeveling luidt om de voorgestelde strategie te implementeren met focus op zowel structurele als culturele veranderingen, ondersteund door intensief change management en continue monitoring van de voortgang."""
                    
                    doc.paragraphs[i + 1].text = management_samenvatting
                    print("✅ Managementsamenvatting gevuld")
                else:
                    print("Managementsamenvatting heeft al content")
            break
    
    # 2. Fix Hofstede grafiek - vervang verkeerde grafiek met juiste
    print("\n2. Fixen Hofstede grafiek...")
    
    # Zoek "Figuur 2.2: Hofstede Gap-analyse Euro Caps"
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if "Figuur 2.2: Hofstede Gap-analyse Euro Caps" in text:
            print(f"Gevonden Hofstede figuur label op regel {i}")
            
            # Zoek de afbeelding in de volgende paragrafen
            for j in range(i, min(i + 5, len(doc.paragraphs))):
                for run in doc.paragraphs[j].runs:
                    if run._element.xpath('.//a:blip'):  # Heeft afbeelding
                        print(f"Gevonden afbeelding bij Hofstede op regel {j}")
                        
                        # Verwijder oude afbeelding
                        for element in run._element:
                            if element.tag.endswith('drawing'):
                                run._element.remove(element)
                                print("Oude afbeelding verwijderd")
                        
                        # Voeg juiste Hofstede afbeelding toe
                        try:
                            if os.path.exists('hofstede_gap_analysis.png'):
                                run.add_picture('hofstede_gap_analysis.png', width=Inches(6))
                                print("✅ Hofstede gap-analyse grafiek ingevoegd")
                            else:
                                print("❌ hofstede_gap_analysis.png niet gevonden")
                                # Voeg placeholder tekst toe
                                run.add_text("[Hofstede Gap-analyse grafiek - bestand niet gevonden]")
                        except Exception as e:
                            print(f"❌ Fout bij invoegen Hofstede grafiek: {e}")
                        
                        break
                if any(run._element.xpath('.//a:blip') for run in doc.paragraphs[j].runs):
                    break
            break
    
    # 3. Controleer of alle andere grafieken correct zijn
    print("\n3. Controleren andere grafieken...")
    
    # Check of Kotter grafiek bij 5.2.3 staat
    kotter_correct = False
    for i, paragraph in enumerate(doc.paragraphs):
        if "5.2.3 Veranderaanpak Kotter" in paragraph.text:
            print(f"Gevonden 5.2.3 op regel {i}")
            
            # Zoek Kotter grafiek in de buurt
            for j in range(i, min(i + 20, len(doc.paragraphs))):
                if "Figuur 5.1: Kotter's 8 Stappenmodel" in doc.paragraphs[j].text:
                    print(f"Kotter grafiek gevonden bij 5.2.3 op regel {j}")
                    kotter_correct = True
                    break
            break
    
    if not kotter_correct:
        print("❌ Kotter grafiek niet bij 5.2.3 gevonden")
    else:
        print("✅ Kotter grafiek correct geplaatst bij 5.2.3")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HOFSTEDE_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HOFSTEDE_FIXED.docx")
    print("✅ Managementsamenvatting gevuld")
    print("✅ Hofstede grafiek vervangen met juiste versie")

if __name__ == "__main__":
    fix_management_en_hofstede_grafiek()

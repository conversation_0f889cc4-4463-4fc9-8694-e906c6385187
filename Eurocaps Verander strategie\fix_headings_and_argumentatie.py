#!/usr/bin/env python3
"""
Script om headings te fixen en argumentatieschema aan te passen
"""

from docx import Document
from docx.shared import Pt

def fix_headings_and_argumentatie():
    """Fix alle headings en pas argumentatieschema aan"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_VOLLEDIG.docx')
    
    print("Bezig met fixen van headings en argumentatieschema...")
    
    # 1. Fix dubbele 2.5 en 2.6 zonder headings
    print("\n1. Fixen van dubbele 2.5 en 2.6...")
    
    found_25_26 = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text.startswith('2.5 Stakeholderanalyse') or text.startswith('2.6 Verandercurve'):
            found_25_26.append((i, text, paragraph.style.name))
            print(f"Gevonden: regel {i}, style: {paragraph.style.name}, tekst: '{text[:50]}...'")
    
    # Verwijder dubbele entries (behoud alleen die met Heading style)
    for i, (regel, tekst, style) in enumerate(found_25_26):
        if i > 0 and not style.startswith('Heading'):  # Verwijder duplicaten zonder heading
            print(f"Verwijderen duplicaat op regel {regel}")
            p = doc.paragraphs[regel]._element
            p.getparent().remove(p)
    
    # 2. Fix 5.2.3 heading
    print("\n2. Fixen van 5.2.3 heading...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text.startswith('5.2.3 Veranderaanpak Kotter') and not paragraph.style.name.startswith('Heading'):
            print(f"5.2.3 gevonden zonder heading op regel {i}")
            paragraph.style = doc.styles['Heading 3']
            print("5.2.3 heading toegepast")
            break
    
    # 3. Fix fase headings
    print("\n3. Fixen van fase headings...")
    
    fase_patterns = [
        'FASE 1: VOORBEREIDING',
        'FASE 2: IMPLEMENTATIE', 
        'FASE 3: VERANKERING'
    ]
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        for pattern in fase_patterns:
            if pattern in text and not paragraph.style.name.startswith('Heading'):
                print(f"Fase gevonden zonder heading: '{text}' op regel {i}")
                paragraph.style = doc.styles['Heading 4']
                print(f"Heading 4 toegepast voor: {pattern}")
                break
    
    # 4. Fix stap headings
    print("\n4. Fixen van stap headings...")
    
    stap_patterns = [
        'Stap 1:', 'Stap 2:', 'Stap 3:', 'Stap 4:', 
        'Stap 5:', 'Stap 6:', 'Stap 7:', 'Stap 8:'
    ]
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        for pattern in stap_patterns:
            if text.startswith(pattern) and not paragraph.style.name.startswith('Heading'):
                print(f"Stap gevonden zonder heading: '{text[:30]}...' op regel {i}")
                # Maak het een subheading
                paragraph.style = doc.styles['Heading 5'] if 'Heading 5' in doc.styles else doc.styles['Heading 4']
                break
    
    # 5. Fix argumentatieschema voor Euro Caps onderzoek
    print("\n5. Aanpassen argumentatieschema voor Euro Caps onderzoek...")
    
    # Zoek argumentatieschema sectie
    argumentatie_start = None
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Argumentatieschema' in paragraph.text and paragraph.style.name.startswith('Heading'):
            argumentatie_start = i
            print(f"Argumentatieschema gevonden op regel {i}")
            break
    
    if argumentatie_start:
        # Vervang argumentatieschema content
        euro_caps_argumentatie = """Het volgende argumentatieschema onderbouwt de gekozen veranderstrategie voor Euro Caps op basis van dit onderzoek.

Standpunt:
Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.

Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit
De huidige machinebureaucratie met sterke hiërarchie beperkt cross-functionele samenwerking die essentieel is voor Six Sigma projecten. Gecentraliseerde besluitvorming vertraagt procesverbeteringen en beperkt medewerkerparticipatie in kwaliteitsinitiatieven. Functionele silo's belemmeren de holistische procesoptimalisatie die Six Sigma vereist voor continue kwaliteitsverbetering.

Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering
De analyse toont hoge onzekerheidsvermijding (score 80/100) die experimenteren en innovatie belemmert. Hoge machtsafstand (score 75/100) beperkt medewerkerparticipatie in verbeterprocessen. Korte termijn focus (score 40/100) conflicteert met lange termijn verbeterdoelstellingen van Six Sigma en duurzame organisatieontwikkeling.

Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces
Boonstra's ontwikkelingsstrategie faciliteert participatieve verandering die aansluit bij Six Sigma principes van continue verbetering. Kotter's achtstappenmodel biedt bewezen raamwerk voor complexe organisatorische transformaties in productieomgevingen. Gecombineerde structuur- en cultuurverandering creëert duurzame basis voor continue kwaliteitsverbetering en organisatorische excellentie.

Tegenargument: Gefaseerde aanpak is voldoende
Geleidelijke aanpassing van alleen organisatiestructuur of cultuur vereist minder resources en creëert minder weerstand bij implementatie. Beperkte verandering minimaliseert risico op operationele disruption tijdens de transformatie. Incrementele verbeteringen kunnen ook tot kwaliteitsverbetering leiden zonder grootschalige organisatorische verandering.

Weerlegging: Geïsoleerde veranderingen zijn onvoldoende voor duurzame transformatie
Structuurverandering zonder cultuurverandering leidt tot weerstand en terugval naar oude patronen zoals aangetoond in de analyse. Cultuurverandering zonder structurele ondersteuning blijft oppervlakkig en niet-duurzaam. Six Sigma vereist fundamentele transformatie die alleen door integrale aanpak wordt gerealiseerd, zoals blijkt uit de gap-analyse tussen huidige en gewenste situatie."""
        
        # Zoek waar argumentatie content begint en vervang
        j = argumentatie_start + 1
        content_start = None
        while j < len(doc.paragraphs):
            text = doc.paragraphs[j].text.strip()
            if text and not text.startswith('Figuur') and not doc.paragraphs[j].style.name.startswith('Heading'):
                content_start = j
                break
            j += 1
        
        if content_start:
            # Vervang de eerste content paragraaf
            doc.paragraphs[content_start].text = euro_caps_argumentatie
            print("Argumentatieschema vervangen met Euro Caps specifieke versie")
            
            # Verwijder oude argumentatie content
            k = content_start + 1
            paragraphs_to_remove = []
            while k < len(doc.paragraphs):
                text = doc.paragraphs[k].text.strip()
                if (doc.paragraphs[k].style.name.startswith('Heading 1') and 
                    ('Bijlage' in text or 'Literatuurlijst' in text)):
                    break
                elif (text.startswith('Hoofdargument') or 
                      text.startswith('Tegenargument') or 
                      text.startswith('Weerlegging') or
                      text.startswith('Standpunt') or
                      'stakeholders' in text.lower()):
                    paragraphs_to_remove.append(k)
                k += 1
            
            # Verwijder oude content
            for idx in reversed(paragraphs_to_remove):
                if idx < len(doc.paragraphs):
                    p = doc.paragraphs[idx]._element
                    p.getparent().remove(p)
                    print(f"Verwijderd oude argumentatie content op regel {idx}")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_HEADINGS_FIXED.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_HEADINGS_FIXED.docx")
    print("✅ Dubbele 2.5 en 2.6 verwijderd")
    print("✅ 5.2.3 heading toegevoegd")
    print("✅ Fase headings toegevoegd")
    print("✅ Stap headings toegevoegd")
    print("✅ Argumentatieschema aangepast voor Euro Caps onderzoek")

if __name__ == "__main__":
    fix_headings_and_argumentatie()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om alle hoofdstukken toe te voegen met visuals op de juiste plekken
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
import os

def add_visual_placeholder(doc, visual_text):
    """Voeg een visual placeholder toe"""
    visual_para = doc.add_paragraph()
    visual_run = visual_para.add_run(visual_text)
    visual_run.font.name = 'Arial'
    visual_run.font.size = Pt(11)
    visual_run.font.bold = True
    visual_run.font.color.rgb = RGBColor(0, 100, 200)
    visual_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    doc.add_paragraph()  # Ruimte na visual
    return doc

def add_chapter1_continuation(doc):
    """Voeg de rest van hoofdstuk 1 toe"""
    
    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', level=2)
    
    leeswijzer_text = """Dit adviesrapport is systematisch opgebouwd om de lezer stap voor stap mee te nemen in de analyse en aanbevelingen voor Euro Caps' organisatieverandering. Het rapport begint in hoofdstuk twee met een uitgebreid theoretisch kader waarin alle relevante modellen en theorieën worden gepresenteerd die de basis vormen voor de analyse. Hier worden Kotter's 8-stappenmodel, Boonstra's veranderstrategieën, De Caluwé's kleurenmodel, Hofstede's cultuurdimensies en Six Sigma methodologie uitgelegd.

Hoofdstuk drie analyseert vervolgens de huidige situatie van Euro Caps door de organisatiestructuur en cultuur in kaart te brengen met behulp van de theoretische modellen uit hoofdstuk twee. Deze analyse vormt de basis voor het begrijpen van de uitgangspositie en identificeert sterke punten en verbeterpunten.

In hoofdstuk vier wordt de gewenste toekomstige situatie beschreven, waarbij duidelijk wordt gemaakt welke veranderingen nodig zijn in zowel structuur als cultuur om Euro Caps' strategische doelstellingen te realiseren.

Hoofdstuk vijf vormt het hart van het rapport en presenteert de integrale veranderstrategie. Dit hoofdstuk is onderverdeeld in een voorbereidend deel met stakeholderanalyse en weerstandsanalyse, en een uitvoerend deel met de gekozen veranderstrategie, implementatieplan en Six Sigma integratie.

Hoofdstuk zes behandelt het communicatieplan dat essentieel is voor het succes van de verandering. Hier wordt uitgelegd hoe verschillende stakeholdergroepen worden benaderd met aangepaste boodschappen en kanalen.

Het rapport wordt afgesloten met hoofdstuk zeven en acht waarin respectievelijk conclusies en concrete aanbevelingen worden gepresenteerd, gevolgd door een uitgebreide literatuurlijst en bijlagen. Het argumentatieschema aan het einde toont de logische onderbouwing van alle keuzes en aanbevelingen volgens het Toulmin model."""
    
    doc.add_paragraph(leeswijzer_text)
    doc.add_page_break()
    
    return doc

def add_chapter2_theoretical_framework(doc):
    """Voeg hoofdstuk 2 toe met visuals op juiste plekken"""
    doc.add_heading('Hoofdstuk 2: Theoretisch kader', level=1)
    
    intro_text = """Dit hoofdstuk presenteert de theoretische fundamenten die ten grondslag liggen aan de analyse en aanbevelingen in dit rapport. De geselecteerde modellen en theorieën bieden een wetenschappelijke basis voor het begrijpen van organisatieverandering en het ontwikkelen van effectieve veranderstrategieën."""
    doc.add_paragraph(intro_text)
    
    # 2.1 Veranderstrategieën volgens Boonstra
    doc.add_heading('2.1 Veranderstrategieën volgens Boonstra', level=2)
    
    boonstra_text = """Boonstra onderscheidt verschillende veranderstrategieën die organisaties kunnen hanteren. Deze strategieën variëren van top-down gerichte benaderingen tot meer participatieve en emergente aanpakken. De empirisch-rationele strategie is gebaseerd op de aanname dat mensen rationeel handelen en verandering accepteren wanneer deze logisch en voordelig is. De normatief-heropvoedende strategie richt zich op het veranderen van attitudes, waarden en gedrag door middel van leren en ontwikkeling.

De machts-dwangstrategie gebruikt autoriteit en macht om verandering af te dwingen, terwijl de faciliterende strategie zich richt op het creëren van optimale condities voor verandering. De confronterende strategie gebruikt crisis en urgentie als drijfveer voor verandering. Voor Euro Caps is een combinatie van empirisch-rationele en normatief-heropvoedende strategieën het meest geschikt, omdat deze aansluit bij de analytische cultuur en de noodzaak voor competentieontwikkeling."""
    doc.add_paragraph(boonstra_text)
    
    # Visual hier bij de analyse
    doc = add_visual_placeholder(doc, "📊 VISUAL: Boonstra's Veranderstrategieën Matrix - Toepassing op Euro Caps")
    
    # 2.2 Kotter's 8-stappenmodel
    doc.add_heading('2.2 Kotter\'s 8-stappenmodel', level=2)
    
    kotter_text = """John Kotter's 8-stappenmodel voor organisatieverandering biedt een systematische aanpak voor het leiden van transformatie. Het model begint met het creëren van urgentiebesef en eindigt met het verankeren van nieuwe benaderingen in de organisatiecultuur. De acht stappen zijn urgentiebesef vestigen, een leidende coalitie vormen, een visie en strategie ontwikkelen, de verandervisie communiceren, een breed draagvlak voor verandering creëren, korte termijn successen genereren, verbeteringen consolideren en vergroten, en de nieuwe aanpak verankeren.

Dit model is bijzonder geschikt voor Euro Caps omdat het een gestructureerde aanpak biedt die zowel de technische als de menselijke aspecten van verandering adresseert. De systematische opbouw zorgt voor duidelijke mijlpalen en meetbare voortgang, wat aansluit bij Euro Caps' behoefte aan structuur en controle."""
    doc.add_paragraph(kotter_text)
    
    # Visual hier bij de analyse
    doc = add_visual_placeholder(doc, "📈 VISUAL: Kotter's 8-Stappenmodel met Six Sigma Integratie")
    
    # 2.3 De Caluwé's Kleurenmodel
    doc.add_heading('2.3 De Caluwé\'s Kleurenmodel voor Verandering', level=2)
    
    caluwe_text = """Het kleurenmodel van De Caluwé en Vermaak onderscheidt vijf verschillende denkwijzen over verandering, elk geassocieerd met een kleur. Deze denkwijzen bepalen hoe mensen tegen verandering aankijken en welke interventies effectief zijn. Blauwdruk denken is rationeel en planmatig, geel denken is politiek en gericht op belangen, rood denken is relationeel en emotioneel, groen denken is lerend en ontwikkelingsgericht, en wit denken is emergent en complex.

Voor Euro Caps is het belangrijk om verschillende kleuren te combineren omdat verschillende stakeholdergroepen verschillende voorkeuren hebben. Management reageert goed op blauwdruk benadering met data en feiten, terwijl productie medewerkers meer gevoelig zijn voor rode benadering met emotionele verbinding en verhalen."""
    doc.add_paragraph(caluwe_text)
    
    # Visual hier bij de analyse
    doc = add_visual_placeholder(doc, "🎨 VISUAL: De Caluwé's Kleurenmodel - Stakeholder Mapping Euro Caps")
    
    return doc

def add_chapter3_current_situation(doc):
    """Voeg hoofdstuk 3 toe met visuals op juiste plekken"""
    doc.add_page_break()
    doc.add_heading('Hoofdstuk 3: Huidige situatie', level=1)
    
    intro_text = """Dit hoofdstuk analyseert de huidige organisatiestructuur en cultuur van Euro Caps. De analyse is gebaseerd op beschikbare organisatiedocumenten en wordt ondersteund door relevante theoretische modellen uit het vorige hoofdstuk."""
    doc.add_paragraph(intro_text)
    
    # 3.1 Huidige organisatiestructuur
    doc.add_heading('3.1 Huidige organisatiestructuur', level=2)
    
    structure_text = """Euro Caps hanteert een functionele organisatiestructuur waarbij medewerkers gegroepeerd zijn op basis van hun specialisatie en functie. De organisatie kent duidelijke hiërarchische lagen met een traditionele top-down besluitvorming. De hoofdafdelingen omvatten Productie, Kwaliteitscontrole, Verkoop en Marketing, Financiën, en Human Resources. Elke afdeling wordt geleid door een afdelingsmanager die rapporteert aan de directie.

Deze structuur biedt voordelen in termen van specialisatie en efficiëntie, maar kan ook leiden tot silo-denken en beperkte flexibiliteit. De communicatie verloopt voornamelijk verticaal, wat innovatie en snelle besluitvorming kan belemmeren. De coördinatiemechanismen zijn voornamelijk gebaseerd op directe supervisie en standaardisatie van werkprocessen. Dit past bij de huidige focus op kwaliteit en consistentie, maar biedt beperkte ruimte voor autonomie en creativiteit."""
    doc.add_paragraph(structure_text)
    
    # Visual hier bij de analyse
    doc = add_visual_placeholder(doc, "🏢 VISUAL: Huidige Organisatiestructuur Euro Caps - Functionele Indeling")
    
    # 3.2 Huidige organisatiecultuur
    doc.add_heading('3.2 Huidige organisatiecultuur', level=2)
    
    culture_text = """De organisatiecultuur van Euro Caps wordt gekenmerkt door een sterke focus op kwaliteit, traditie en betrouwbaarheid. Het bedrijfsmotto "Quality. Every Single Time." reflecteert de kernwaarden van de organisatie. Volgens Hofstede's cultuurdimensies scoort Euro Caps hoog op machtsdistantie, wat zich uit in respect voor hiërarchie en autoriteit. De organisatie toont kenmerken van collectivisme, waarbij teamwork en groepsloyaliteit worden gewaardeerd.

De onzekerheidsvermijding is relatief hoog, wat resulteert in voorkeur voor duidelijke procedures, regels en voorspelbaarheid. Dit past bij de aard van de voedingsmiddelenindustrie waar voedselveiligheid en consistentie cruciaal zijn. De huidige cultuur ondersteunt de operationele excellentie maar kan innovatie en veranderingsbereidheid belemmeren. Er is een sterke focus op het behouden van bestaande werkwijzen en het vermijden van risico's."""
    doc.add_paragraph(culture_text)
    
    # Visual hier bij de analyse
    doc = add_visual_placeholder(doc, "📊 VISUAL: Hofstede's Cultuurdimensies Analyse Euro Caps")
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_GECORRIGEERD_COMPLEET.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Voeg hoofdstukken toe
    document = add_chapter1_continuation(document)
    document = add_chapter2_theoretical_framework(document)
    document = add_chapter3_current_situation(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Hoofdstukken toegevoegd met visuals op juiste plekken: {doc_path}")

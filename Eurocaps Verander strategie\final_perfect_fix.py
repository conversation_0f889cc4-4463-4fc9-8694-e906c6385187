#!/usr/bin/env python3
"""
Script om het perfecte document te maken door het goede document te nemen en alleen de nodige fixes toe te passen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
import re

def final_perfect_fix():
    """Maakt het perfecte document door alleen de nodige fixes toe te passen"""
    
    # Open het goede document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_FINAAL_GECORRIGEERD.docx')
    
    # 1. Verwijder alle "CORRECTIE:" teksten en gerelateerde content
    paragraphs_to_remove = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if (text.startswith('CORRECTIE:') or 
            'Deze sectie hoort in hoofdstuk' in text or
            text.startswith('CORRECTIE') or
            'Hieronder de volledige uitwerking' in text):
            paragraphs_to_remove.append(i)
    
    # Verwij<PERSON> van achteren naar voren
    for i in reversed(paragraphs_to_remove):
        p = doc.paragraphs[i]._element
        p.getparent().remove(p)
    
    # 2. Voeg hoofdstuk nummering toe
    heading_mapping = {
        'Managementsamenvatting': 'Managementsamenvatting',
        'Voorwoord': 'Voorwoord', 
        'Inleiding': '1. Inleiding',
        'Theoretisch kader': '2. Theoretisch kader',
        'Huidige situatie': '3. Huidige situatie',
        'Gewenste situatie': '4. Gewenste situatie',
        'Veranderstrategie + implementatieplan': '5. Veranderstrategie + implementatieplan',
        'Communicatieplan': '6. Communicatieplan',
        'Conclusie': '7. Conclusie',
        'Aanbevelingen': 'Aanbevelingen',
        'Literatuurlijst': 'Literatuurlijst',
        'Argumentatieschema': 'Argumentatieschema',
        'Bijlage': 'Bijlage'
    }
    
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading 1'):
            text = paragraph.text.strip()
            for old_text, new_text in heading_mapping.items():
                if old_text.lower() in text.lower() and not text.startswith(new_text.split('.')[0] if '.' in new_text else new_text):
                    paragraph.text = new_text
                    break
    
    # 3. Voeg subhoofdstuk nummering toe
    subheading_mapping = {
        'Deskresearch methode': '1.1 Deskresearch Methode',
        'Leeswijzer': '1.2 Leeswijzer',
        'Veranderstrategieën volgens Boonstra': '2.1 Veranderstrategieën volgens Boonstra',
        'Veranderkleuren van De Caluwé': '2.2 Veranderkleuren van De Caluwé',
        'Gap-analyse & Hofstede-model': '2.3 Gap-analyse & Hofstede-model',
        'Kotter\'s 8 Stappenmodel': '2.4 Kotter\'s 8 Stappenmodel',
        'Stakeholderanalyse': '2.5 Stakeholderanalyse',
        'Verandercurve van Kübler-Ross': '2.6 Verandercurve van Kübler-Ross',
        'Huidige organisatiestructuur': '3.1 Huidige Organisatiestructuur',
        'Huidige organisatiecultuur': '3.2 Huidige Organisatiecultuur',
        'Deelconclusie beantwoorden': '3.3 Deelconclusie',
        'Gewenste organisatiestructuur': '4.1 Gewenste Organisatiestructuur',
        'Gewenste organisatiecultuur': '4.2 Gewenste Organisatiecultuur',
        'Overzicht communicatieplan': '6.1 Overzicht Communicatieplan'
    }
    
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading 2'):
            text = paragraph.text.strip()
            for old_text, new_text in subheading_mapping.items():
                if old_text.lower() in text.lower() and not text.startswith(new_text.split(' ')[0]):
                    paragraph.text = new_text
                    break
    
    # 4. Zoek naar de Kotter sectie en voeg fase uitwerkingen toe
    kotter_section_found = False
    insert_after_paragraph = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        if ('Veranderaanpak Kotter' in paragraph.text or 
            '5.2.3' in paragraph.text and 'Kotter' in paragraph.text):
            kotter_section_found = True
            # Zoek naar de volgende paragraaf na deze heading
            for j in range(i+1, len(doc.paragraphs)):
                if doc.paragraphs[j].text.strip():  # Eerste niet-lege paragraaf
                    insert_after_paragraph = j
                    break
            break
    
    if kotter_section_found and insert_after_paragraph is not None:
        # Voeg de Kotter fase uitwerkingen toe
        kotter_text = """De implementatie van Kotter's 8-stappenmodel wordt gestructureerd in drie duidelijke fasen waarbij elke fase specifieke doelstellingen heeft en systematisch voortbouwt op de vorige fase.

FASE 1: VOORBEREIDING (Maanden 1-3)
Deze fase omvat de eerste drie stappen van Kotter's model en richt zich op het creëren van de juiste condities voor verandering.

Stap 1: Urgentiebesef creëren (Maand 1-2) - Het senior management presenteert concrete data over kwaliteitsprestaties, defectpercentages en marktpositie. Benchmarking studies tonen hoe concurrenten presteren op kwaliteitsgebied.

Stap 2: Leidende coalitie vormen (Maand 2-3) - De CEO selecteert 8-10 invloedrijke leiders uit verschillende afdelingen. Deze coalitie ontvangt training in change leadership en Six Sigma principes.

Stap 3: Visie en strategie ontwikkelen (Maand 3) - De coalitie formuleert een inspirerende visie voor Euro Caps na Six Sigma implementatie. De visie wordt ondersteund door concrete strategie.

FASE 2: IMPLEMENTATIE (Maanden 4-15)
Deze fase omvat stappen 4-6 en richt zich op het mobiliseren van de organisatie en realiseren van eerste resultaten.

Stap 4: Visie communiceren (Maand 4-6) - Uitgebreid communicatieplan via alle kanalen. Leidinggevenden worden getraind in visie communicatie. Doel: 90% van medewerkers kent de visie.

Stap 5: Medewerkers empoweren (Maand 7-12) - Systematische eliminatie van barrières en Six Sigma training voor medewerkers. Meer autonomie in besluitvorming.

Stap 6: Korte-termijn successen genereren (Maand 9-15) - Identificatie en implementatie van quick wins. Strategische keuze voor zichtbare verbeteringen.

FASE 3: VERANKERING (Maanden 16-21)
Deze finale fase omvat stappen 7-8 en richt zich op duurzaam maken van veranderingen.

Stap 7: Verbeteringen consolideren en uitbreiden (Maand 16-18) - Standaardisatie en uitbreiding van succesvolle verbeteringen. Nieuwe projecten bouwen voort op successen.

Stap 8: Nieuwe aanpak verankeren in cultuur (Maand 19-21) - Integratie van nieuwe waarden in alle organisatieaspecten. Aanpassing van recruitment, training en beloningssystemen."""
        
        # Voeg de tekst toe na de gevonden paragraaf
        p = doc.paragraphs[insert_after_paragraph]
        new_p = p.insert_paragraph_before(kotter_text)
    
    # 5. Vervang het argumentatieschema standpunt
    for paragraph in doc.paragraphs:
        if 'Euro Caps moet zowel directe als indirecte stakeholders' in paragraph.text:
            paragraph.text = paragraph.text.replace(
                'Euro Caps moet zowel directe als indirecte stakeholders meenemen in de besluitvorming over het bestel- en voorraagsysteem.',
                'Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.'
            )
    
    # 6. Vervang de argumentatieschema argumenten met Euro Caps specifieke
    for paragraph in doc.paragraphs:
        text = paragraph.text
        if 'Directe stakeholders beïnvloeden de operationele efficiëntie' in text:
            paragraph.text = 'Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit'
        elif 'Indirecte stakeholders kunnen strategische risico\'s veroorzaken' in text:
            paragraph.text = 'Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering'
        elif 'Duurzaamheid en marktpositie versterken bedrijfscontinuïteit' in text:
            paragraph.text = 'Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces'
    
    # 7. Verwijder externe bronnen en vervang met relevante bronnen
    for paragraph in doc.paragraphs:
        text = paragraph.text
        if 'Nestell & Associates' in text or 'Axial ERP' in text or 'R-Wave ERP' in text:
            paragraph.text = 'Bronnen: Boonstra (2018), Kotter (2012), Hofstede (2010)'
    
    # Sla het perfecte document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_PERFECT_FINAAL.docx')
    print("Perfect document gemaakt: Alle correcties toegepast, hoofdstuk nummering toegevoegd, Kotter fasen op juiste plek!")

if __name__ == "__main__":
    final_perfect_fix()

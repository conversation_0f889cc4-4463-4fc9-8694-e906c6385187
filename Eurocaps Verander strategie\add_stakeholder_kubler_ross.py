#!/usr/bin/env python3
"""
Script om stakeholderanalyse en Kübler-Ross toe te voegen aan hoofdstuk 5
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_stakeholder_kubler_ross():
    """Voegt stakeholderanalyse en Kübler-Ross analyse toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK5_DEEL1.docx')
    
    # 5.1.3 Stakeholdersanalyse
    doc.add_heading('5.1.3 Stakeholdersanalyse', level=3)
    
    stakeholder_intro = """Een uitgebreide stakeholderanalyse is essentieel voor het succesvol implementeren van organisatorische veranderingen bij Euro Caps. Deze analyse identificeert alle relevante belanghebbenden, analyseert hun invloed en belang, en ontwikkelt specifieke strategieën voor stakeholder engagement tijdens het veranderingsproces."""
    
    doc.add_paragraph(stakeholder_intro)
    
    stakeholder_analyse_text = """De stakeholderanalyse voor Euro Caps is gebaseerd op een systematische identificatie van alle partijen die invloed hebben op of beïnvloed worden door de voorgestelde organisatorische veranderingen. Deze analyse gebruikt het power-interest model om stakeholders te categoriseren en passende engagement strategieën te ontwikkelen.

Primaire stakeholders zijn degenen die direct betrokken zijn bij de dagelijkse operaties en direct beïnvloed worden door de veranderingen. Deze groep omvat het senior management team onder leiding van CEO Nils Clement, die als hoofdsponsor van de verandering fungeert en verantwoordelijk is voor het creëren van urgentiebesef en het mobiliseren van resources. Servé Bosland, als operationeel manager, speelt een cruciale rol in het vertalen van strategische doelstellingen naar operationele plannen en het begeleiden van middle management tijdens de transitie.

Erik Dekker, als ICT-manager, is essentieel voor het ondersteunen van de technologische aspecten van Six Sigma implementatie, inclusief data-analyse systemen en procesmonitoring tools. Zijn expertise en commitment zijn cruciaal voor het succes van data-gedreven verbeteringsinitiatieven. Berkan Arrindell en andere afdelingsmanagers fungeren als change agents binnen hun respectievelijke domeinen en zijn verantwoordelijk voor het implementeren van veranderingen op operationeel niveau.

Secundaire stakeholders omvatten medewerkers op verschillende niveaus die indirect betrokken zijn bij de verandering maar wel impact ondervinden van nieuwe werkwijzen en organisatiestructuren. Deze groep vereist specifieke aandacht voor communicatie, training en ondersteuning om weerstand te minimaliseren en betrokkenheid te maximaliseren.

Externe stakeholders zoals klanten (bijvoorbeeld Lidl), leveranciers en toezichthouders hebben indirect belang bij de veranderingen omdat deze de kwaliteit van producten en dienstverlening kunnen beïnvloeden. Hoewel hun directe invloed op het veranderingsproces beperkt is, moeten hun belangen worden meegenomen in de planning en communicatie.

De power-interest matrix toont dat het senior management team (Nils Clement, Servé Bosland, Erik Dekker) in het "manage closely" kwadrant valt vanwege hun hoge macht en hoge belang. Deze stakeholders vereisen intensieve betrokkenheid, regelmatige communicatie en actieve participatie in besluitvorming. Middle management (Niene Tepe, Berkan Arrindell, Maik Ritter) valt grotendeels in het "keep informed" kwadrant en vereist regelmatige updates en mogelijkheden voor input en feedback.

Operationele medewerkers hebben hoog belang maar beperkte macht en vallen daarom in het "keep informed" kwadrant, waarbij de focus ligt op duidelijke communicatie over de voordelen van verandering en training in nieuwe werkwijzen. Ondersteunende functies zoals HR en financiën hebben matige macht en belang en vereisen periodieke updates en betrokkenheid bij relevante aspecten van de verandering."""
    
    doc.add_paragraph(stakeholder_analyse_text)
    
    # Voeg stakeholder tabel toe
    doc.add_paragraph('\nTabel 5.3: Stakeholder Power-Interest Analyse Euro Caps')
    
    table = doc.add_table(rows=1, cols=6)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Stakeholder', 'Functie', 'Power (1-10)', 'Interest (1-10)', 'Categorie', 'Engagement Strategie']
    for i, header in enumerate(headers):
        table.rows[0].cells[i].text = header
    
    # Stakeholder data
    stakeholder_data = [
        ('Nils Clement', 'CEO', '10', '10', 'Manage Closely', 'Intensieve betrokkenheid, sponsor rol'),
        ('Servé Bosland', 'Manager', '9', '9', 'Manage Closely', 'Operationele leiding, change champion'),
        ('Erik Dekker', 'ICT Manager', '8', '9', 'Manage Closely', 'Technische ondersteuning, data-analyse'),
        ('Berkan Arrindell', 'Manager', '8', '8', 'Manage Closely', 'Afdelings-implementatie, coaching'),
        ('Niene Tepe', 'Manager', '7', '8', 'Keep Informed', 'Regelmatige updates, feedback sessies'),
        ('Maik Ritter', 'Manager', '7', '7', 'Keep Informed', 'Communicatie, training ondersteuning'),
        ('Maria Stanić', 'Manager', '6', '7', 'Keep Informed', 'Team communicatie, proces aanpassingen'),
        ('Rijk Wegen', 'Manager', '6', '6', 'Keep Informed', 'Periodieke updates, consultatie'),
        ('Ko Jager', 'Manager', '5', '6', 'Monitor', 'Basis communicatie, algemene updates'),
        ('Uwe Regel', 'HR Manager', '6', '8', 'Keep Informed', 'Change management, training coördinatie'),
        ('Operationele Teams', 'Medewerkers', '4', '8', 'Keep Informed', 'Training, communicatie, ondersteuning'),
        ('Externe Klanten', 'Afnemers', '7', '6', 'Keep Informed', 'Kwaliteitscommunicatie, service updates')
    ]
    
    for data in stakeholder_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Voeg stakeholder matrix visual toe
    try:
        doc.add_paragraph('\nFiguur 5.1: Stakeholder Power-Interest Matrix Euro Caps')
        doc.add_picture('stakeholder_matrix_euro_caps.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur 5.1: Stakeholder Power-Interest Matrix - Visual wordt toegevoegd]')
    
    # 5.1.4 Mogelijke Weerstanden van Kübler-Ross
    doc.add_heading('5.1.4 Mogelijke Weerstanden volgens Kübler-Ross Model', level=3)
    
    kubler_ross_intro = """Het Kübler-Ross model biedt een waardevol kader voor het begrijpen en anticiperen op emotionele reacties van medewerkers tijdens organisatorische veranderingen. Door de verschillende fasen van rouw en aanpassing te herkennen, kan Euro Caps proactief interventies ontwikkelen om weerstand te minimaliseren en de transitie te faciliteren."""
    
    doc.add_paragraph(kubler_ross_intro)
    
    kubler_ross_text = """De toepassing van het Kübler-Ross model op de organisatorische verandering bij Euro Caps vereist een diepgaand begrip van hoe verschillende stakeholdergroepen kunnen reageren op de voorgestelde structurele en culturele aanpassingen. Elke fase van het model presenteert specifieke uitdagingen en vereist aangepaste interventies om medewerkers door het veranderingsproces te begeleiden.

De ontkenningsfase manifesteert zich typisch in de vroege stadia van de veranderingsaankondiging waarbij medewerkers de noodzaak of haalbaarheid van de voorgestelde veranderingen in twijfel trekken. Bij Euro Caps kan dit zich uiten in uitspraken zoals "onze huidige manier van werken functioneert prima" of "Six Sigma is slechts een managementmode die wel weer overwaait." Deze reactie is natuurlijk en voorspelbaar, vooral gegeven de relatief stabiele geschiedenis van de organisatie en de hoge onzekerheidsvermijding in de huidige cultuur.

Interventies voor de ontkenningsfase richten zich op het verstrekken van duidelijke, feitelijke informatie over de redenen voor verandering, de risico's van niet-veranderen en de voordelen van de nieuwe aanpak. Dit omvat het delen van marktdata, kwaliteitsmetrieken en benchmarking informatie die de noodzaak voor verbetering ondersteunen. Communicatie moet transparant, consistent en herhaald zijn om door de ontkenning heen te breken.

De woede-fase ontstaat wanneer de realiteit van verandering doordringt en medewerkers frustratie ervaren over het verlies van vertrouwde werkwijzen en de onzekerheid over de toekomst. Deze fase kan zich manifesteren in kritiek op het management, weerstand tegen nieuwe procedures en negatieve attitudes ten opzichte van veranderingsinitiatieven. Bij Euro Caps kan dit bijzonder uitgesproken zijn onder ervaren medewerkers die trots zijn op hun expertise en bezorgd zijn over hun toekomstige rol.

Interventies voor de woede-fase vereisen empathische communicatie, actief luisteren en het creëren van veilige ruimtes voor het uiten van zorgen en frustraties. Leidinggevenden moeten worden getraind in het omgaan met emotionele reacties en het faciliteren van constructieve dialoog. Het is cruciaal om woede niet te negeren of te onderdrukken maar deze te erkennen als een natuurlijke reactie op verandering.

De onderhandelingsfase wordt gekenmerkt door pogingen om compromissen te bereiken of voorwaarden te stellen voor acceptatie van verandering. Medewerkers kunnen voorstellen doen voor alternatieve benaderingen of verzoeken om uitzonderingen op nieuwe regels. Deze fase biedt kansen voor constructieve betrokkenheid maar vereist ook duidelijkheid over wat wel en niet onderhandelbaar is.

Interventies voor de onderhandelingsfase omvatten het creëren van formele kanalen voor input en feedback, het betrekken van medewerkers bij de detailuitwerking van veranderingen en het demonstreren van flexibiliteit waar mogelijk zonder de kernprincipes van de verandering te compromitteren. Participatieve besluitvorming en co-creatie kunnen effectieve strategieën zijn in deze fase.

De depressiefase manifesteert zich als een periode van lage energie, verminderde motivatie en pessimisme over de toekomst. Medewerkers kunnen zich overweldigd voelen door de omvang van verandering en twijfelen aan hun vermogen om zich aan te passen. Deze fase vereist bijzondere aandacht omdat het de productiviteit en het moraal significant kan beïnvloeden.

Interventies voor de depressiefase richten zich op het bieden van ondersteuning, coaching en training om medewerkers te helpen nieuwe competenties te ontwikkelen. Het vieren van kleine successen, het bieden van erkenning voor inspanningen en het creëren van hoop door het delen van positieve voorbeelden zijn essentiële strategieën. Mentoring en buddy-systemen kunnen waardevolle ondersteuning bieden.

De acceptatiefase wordt bereikt wanneer medewerkers de nieuwe realiteit omarmen en bereid zijn om constructief bij te dragen aan de verandering. Deze fase wordt gekenmerkt door verhoogde energie, bereidheid om nieuwe vaardigheden te leren en positieve attitudes ten opzichte van toekomstige mogelijkheden.

Interventies voor de acceptatiefase richten zich op het empoweren van medewerkers, het bieden van kansen voor groei en ontwikkeling en het institutionaliseren van nieuwe werkwijzen. Het is belangrijk om momentum te behouden en te voorkomen dat de organisatie terugvalt in oude patronen."""
    
    doc.add_paragraph(kubler_ross_text)
    
    # Voeg Kübler-Ross tabel toe
    doc.add_paragraph('\nTabel 5.4: Kübler-Ross Fasen en Interventiestrategieën')
    
    table2 = doc.add_table(rows=1, cols=5)
    table2.style = 'Table Grid'
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Fase', 'Kenmerken', 'Mogelijke Reacties', 'Interventies', 'Verantwoordelijke']
    for i, header in enumerate(headers):
        table2.rows[0].cells[i].text = header
    
    # Kübler-Ross data
    kubler_data = [
        ('Ontkenning', 'Negeren van verandering', '"Dit is niet nodig"', 'Feitelijke informatie, transparante communicatie', 'Senior Management'),
        ('Woede', 'Frustratie en weerstand', '"Dit is oneerlijk"', 'Empathisch luisteren, veilige ruimtes', 'Lijnmanagers + HR'),
        ('Onderhandeling', 'Zoeken naar compromissen', '"Wat als we..."', 'Participatie, co-creatie mogelijkheden', 'Middle Management'),
        ('Depressie', 'Lage energie, pessimisme', '"Ik kan dit niet"', 'Coaching, training, ondersteuning', 'HR + Coaches'),
        ('Acceptatie', 'Bereidheid tot verandering', '"Laten we dit doen"', 'Empowerment, groei mogelijkheden', 'Alle managers')
    ]
    
    for data in kubler_data:
        row_cells = table2.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Voeg Kübler-Ross curve visual toe
    try:
        doc.add_paragraph('\nFiguur 5.2: Kübler-Ross Verandercurve Euro Caps')
        doc.add_picture('kubler_ross_curve_euro_caps.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur 5.2: Kübler-Ross Verandercurve - Visual wordt toegevoegd]')
    
    # Sla het bijgewerkte document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_STAKEHOLDER_KUBLER.docx')
    print("Stakeholderanalyse en Kübler-Ross toegevoegd aan het academische rapport!")

if __name__ == "__main__":
    add_stakeholder_kubler_ross()

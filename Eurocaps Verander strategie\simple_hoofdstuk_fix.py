#!/usr/bin/env python3
"""
Eenvoudige script om hoofdstukken te fixen
"""

from docx import Document

def simple_hoofdstuk_fix():
    """Eenvoudige fix voor hoofdstukken"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HOOFDSTUKKEN_CORRECT.docx')
    
    print("Bezig met eenvoudige hoofdstuk fix...")
    
    # Mapping van wat we willen veranderen
    heading_fixes = {
        # Hoofdstuk 2 subhoofdstukken - volgens template
        "2.1 Mintzberg's Organisatiestructuurtheorie": "2.1 Veranderstrategieën volgens Boonstra",
        "2.2 Hofstede's Cultuurdimensiemodel": "2.2 Veranderkleuren van De Caluwé", 
        "2.3 Boonstra's Veranderstrategieën": "2.3 Gap-analyse & Hofstede-model",
        "2.4 <PERSON><PERSON>'s Achtstappenmodel voor Verandering": "2.4 <PERSON><PERSON>'s 8 Stappenmodel",
        
        # Hoofdstuk 5 subhoofdstukken - volgens template
        "5.1 Voorbereidende Deel": "5.1 Voorbereidende deel",
        "5.2 Uitvoerende Deel": "5.2 Uitvoerende deel",
        "5.1.1 Organisatiestructuur Veranderingen": "5.1.1 Organisatiestructuur veranderingen",
        "5.1.2 Organisatiecultuur Veranderingen": "5.1.2 Organisatiecultuur veranderingen",
        "5.1.3 Stakeholdersanalyse": "5.1.3 Stakeholdersanalyse",
        "5.1.4 Mogelijke Weerstanden volgens Kübler-Ross Model": "5.1.4 Mogelijke weerstanden van Kübler-Ross",
        "5.2.1 Veranderstrategie volgens Boonstra": "5.2.1 Strategische veranderaanpak",
        "5.2.2 Veranderaanpak volgens Kotter's 8-Stappenmodel": "5.2.2 Veranderstrategie Boonstra",
        "5.2.3 Stakeholder Interventies": "5.2.3 Veranderaanpak Kotter",
        
        # Voeg ontbrekende toe
        "5.2.3 Veranderaanpak Kotter": "5.2.3 Veranderaanpak Kotter",  # Deze moet blijven
    }
    
    # Doorloop alle paragrafen
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading'):
            original_text = paragraph.text.strip()
            
            # Check of we deze moeten aanpassen
            if original_text in heading_fixes:
                new_text = heading_fixes[original_text]
                paragraph.text = new_text
                print(f"Aangepast: '{original_text}' -> '{new_text}'")
    
    # Voeg ontbrekende subhoofdstukken toe door tekst toe te voegen
    # We voegen deze toe als nieuwe paragrafen
    
    # Zoek waar we 2.5 en 2.6 moeten toevoegen
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "2.4 Kotter's 8 Stappenmodel":
            # Voeg content toe voor ontbrekende subhoofdstukken
            # We voegen dit toe als tekst in het document
            
            # Zoek de volgende paragraaf na 2.4
            next_paragraph_index = i + 1
            while (next_paragraph_index < len(doc.paragraphs) and 
                   not doc.paragraphs[next_paragraph_index].style.name.startswith('Heading 1')):
                next_paragraph_index += 1
            
            # Voeg tekst toe voor 2.5 en 2.6
            if next_paragraph_index < len(doc.paragraphs):
                insert_text = """

2.5 Stakeholderanalyse

Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen.

2.6 Verandercurve van Kübler-Ross

Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen.

"""
                # Voeg toe voor het volgende hoofdstuk
                p = doc.paragraphs[next_paragraph_index - 1]
                p.text = p.text + insert_text
                print("Toegevoegd: 2.5 Stakeholderanalyse en 2.6 Verandercurve van Kübler-Ross")
            break
    
    # Voeg 5.2.4 toe als deze ontbreekt
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "5.2.3 Veranderaanpak Kotter":
            # Check of 5.2.4 al bestaat
            has_524 = False
            for j in range(i, min(i+20, len(doc.paragraphs))):
                if "5.2.4" in doc.paragraphs[j].text:
                    has_524 = True
                    break
            
            if not has_524:
                # Voeg 5.2.4 toe
                # Zoek waar we dit moeten invoegen
                next_heading_index = i + 1
                while (next_heading_index < len(doc.paragraphs) and 
                       not (doc.paragraphs[next_heading_index].style.name.startswith('Heading') and 
                            ('5.3' in doc.paragraphs[next_heading_index].text or 
                             'Hoofdstuk 6' in doc.paragraphs[next_heading_index].text))):
                    next_heading_index += 1
                
                if next_heading_index < len(doc.paragraphs):
                    insert_text = """

5.2.4 Interventies van de stakeholder

Specifieke interventies worden ontwikkeld voor verschillende stakeholdergroepen om hun effectieve betrokkenheid bij de verandering te waarborgen.

"""
                    p = doc.paragraphs[next_heading_index - 1]
                    p.text = p.text + insert_text
                    print("Toegevoegd: 5.2.4 Interventies van de stakeholder")
            break
    
    # Voeg 6.1 toe als deze ontbreekt
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == "Hoofdstuk 6: Communicatieplan":
            # Check of 6.1 al bestaat
            has_61 = False
            for j in range(i, min(i+10, len(doc.paragraphs))):
                if "6.1" in doc.paragraphs[j].text:
                    has_61 = True
                    break
            
            if not has_61:
                # Voeg 6.1 toe
                insert_text = """

6.1 Overzicht communicatieplan

Het communicatieplan voor Euro Caps zorgt voor effectieve informatieoverdracht tijdens alle fasen van de organisatorische transformatie.

"""
                p = doc.paragraphs[i]
                p.text = p.text + insert_text
                print("Toegevoegd: 6.1 Overzicht communicatieplan")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_FINAL.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_FINAL.docx")
    print("✅ Hoofdstukken aangepast volgens template")
    print("✅ Ontbrekende subhoofdstukken toegevoegd")

if __name__ == "__main__":
    simple_hoofdstuk_fix()

#!/usr/bin/env python3
"""
Script om het complete uitgebreide adviesrapport te maken met alle details en visuals
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.shared import RGBColor
import os

def add_visual_if_exists(doc, visual_path, caption, width=6):
    """Voegt een visual toe als deze bestaat"""
    if os.path.exists(visual_path):
        # Voeg caption toe
        caption_para = doc.add_paragraph()
        caption_run = caption_para.add_run(caption)
        caption_run.bold = True
        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Voeg afbeelding toe
        try:
            doc.add_picture(visual_path, width=Inches(width))
            last_paragraph = doc.paragraphs[-1]
            last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            doc.add_paragraph()  # Ruimte na afbeelding
            return True
        except Exception as e:
            print(f"Kon {visual_path} niet toevoegen: {e}")
            return False
    else:
        print(f"Visual {visual_path} niet gevonden")
        return False

def create_complete_detailed_report():
    """Maakt het complete uitgebreide adviesrapport"""
    
    # Maak een nieuw document
    doc = Document()
    
    # VOORPAGINA
    title = doc.add_heading('Adviesrapport Veranderingsmanagement:', level=1)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    subtitle = doc.add_heading('Optimalisatie van Euro Caps door Gerichte Structuur- en Cultuurverandering met Six Sigma', level=2)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    # School informatie
    school_info = doc.add_paragraph()
    school_info.add_run('Hogeschool Rotterdam BIM\n').bold = True
    school_info.add_run('Naam: Shuja Schadon\n')
    school_info.add_run('Studentnummer: 1066741\n')
    school_info.add_run('Onderwijsperiode: OP4\n')
    school_info.add_run('Plaats en datum: Rotterdam, 5 juli 2025\n')
    school_info.add_run('Docenten: Robert Vlug en Aicha Manuela Martijn\n')
    school_info.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_page_break()
    
    # MANAGEMENTSAMENVATTING
    doc.add_heading('MANAGEMENTSAMENVATTING', level=1)
    
    ms_text = """Dit adviesrapport beschrijft een strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren met als doel het bereiken van hogere kwaliteit en efficiëntie, voortbouwend op de reeds ingezette Six Sigma methodiek. De analyse omvat een diepgaand onderzoek naar de huidige organisatiestructuur middels coördinatiemechanismen en de organisatiecultuur door toepassing van Hofstede's cultuurdimensies binnen organisaties. Er is een uitgebreide stakeholderanalyse uitgevoerd om alle relevante belanghebbenden en hun potentieel weerstandsgedrag in kaart te brengen.

Op basis hiervan is een passende veranderstrategie gekozen uit de theorieën van Boonstra (BDK-theorie), aangevuld met Kotter's achtstappenmodel voor implementatie. Het rapport presenteert concrete acties voor zowel structurele als culturele veranderingen en sluit af met een gedetailleerd communicatieplan, gericht op de verschillende stakeholdergroepen en gebaseerd op de mensbeelden van De Caluwé. De integratie van Six Sigma binnen het veranderproces vormt een cruciaal onderdeel van de aanbevelingen, waarbij de DMAIC-cyclus als leidraad dient voor continue verbetering en borging van de resultaten."""
    
    doc.add_paragraph(ms_text)
    
    doc.add_page_break()
    
    # VOORWOORD
    doc.add_heading('VOORWOORD', level=1)
    
    voorwoord_text = """Graag wil ik mijn docenten Robert Vlug en Aicha Manuela Martijn bedanken voor het beschikbaar stellen van de benodigde documenten en begeleiding tijdens dit project. Hun expertise en ondersteuning hebben bijgedragen aan de totstandkoming van dit adviesrapport.

De aanleiding voor dit rapport is de positieve beoordeling van een eerder opgesteld adviesdocument door Euro Caps. Deze voortzetting biedt de mogelijkheid om dieper in te gaan op de implementatie van structurele en culturele veranderingen die nodig zijn om de ingezette kwaliteitsverbetering, in het bijzonder via de Six Sigma methodiek, duurzaam te borgen.

De keuze voor dit onderwerp is gemotiveerd door de cruciale rol die organisatorische flexibiliteit en een lerende cultuur spelen in het succesvol doorvoeren van continue procesverbeteringen. De Six Sigma methodiek kan pas haar volledige potentieel benutten wanneer de omringende organisatie en haar medewerkers optimaal zijn afgestemd op en betrokken zijn bij dit streven naar perfectie."""
    
    doc.add_paragraph(voorwoord_text)
    
    doc.add_page_break()
    
    # INHOUDSOPGAVE
    doc.add_heading('INHOUDSOPGAVE', level=1)
    
    toc_items = [
        'Hoofdstuk 1: Inleiding',
        '• Deskresearch methode',
        '• Leeswijzer',
        'Hoofdstuk 2: Theoretisch kader',
        'Hoofdstuk 3: Huidige situatie',
        '• Huidige organisatiestructuur',
        '• Huidige organisatiecultuur',
        '• Deelconclusie beantwoorden',
        'Hoofdstuk 4: Gewenste situatie',
        '• Gewenste organisatiestructuur',
        '• Gewenste organisatiecultuur',
        '• Deelconclusie beantwoorden',
        'Hoofdstuk 5: Veranderstrategie + implementatieplan',
        '• Voorbereidende deel',
        '• Organisatiestructuur veranderingen',
        '• Organisatiecultuur veranderingen',
        '• Stakeholdersanalyse + Visual en table',
        '• Mogelijke weerstanden van Kübler-Ross',
        '• Uitvoerende deel',
        '• Veranderstrategie Boonstra',
        '• Veranderaanpak Kotter',
        '• Interventies van de stakeholder',
        '• Deelconclusie beantwoorden',
        'Hoofdstuk 6: Communicatieplan',
        'Hoofdstuk 7: Conclusie',
        'Aanbevelingen',
        'Literatuurlijst',
        'Bijlagen'
    ]
    
    for item in toc_items:
        doc.add_paragraph(item)
    
    doc.add_page_break()
    
    # HOOFDSTUK 1: INLEIDING
    doc.add_heading('HOOFDSTUK 1: INLEIDING', level=1)
    
    h1_intro = """Dit rapport bouwt voort op eerdere aanbevelingen met betrekking tot kwaliteitsmanagement binnen Euro Caps, waar de keuze voor de Six Sigma methodiek met het DMAIC-raamwerk reeds is vastgesteld. De eerdere analyse heeft uitgewezen dat Six Sigma de meest geschikte methode is voor de precisieprocessen bij Euro Caps, specifiek gericht op het optimaliseren van het vulproces van koffiecapsules en het minimaliseren van defecten. Deze methode sluit naadloos aan bij het motto van Euro Caps, "Quality. Every Single Time.", en is essentieel voor het bereiken van consistentie en het terugdringen van afwijkingen in de productie."""
    doc.add_paragraph(h1_intro)
    
    h1_sixsigma = """Hoewel Six Sigma een sterke focus heeft op procesoptimalisatie, is voor een duurzame implementatie en bredere organisatieverbetering een diepgaande analyse van de organisatiestructuur en -cultuur noodzakelijk. Dit adviesrapport zal de noodzakelijke veranderingen in de organisatiestructuur en -cultuur in kaart brengen die nodig zijn om de volle potentie van Six Sigma te benutten en blijvende kwaliteitsverbetering te realiseren.

Euro Caps heeft gekozen voor de kwaliteitsmanagementmethode Six Sigma, waarbij het DMAIC-raamwerk centraal staat. Dit raamwerk bestaat uit de fasen Define, Measure, Analyze, Improve en Control. De focus ligt op het optimaliseren van het vulproces van koffiecapsules en het minimaliseren van defecten. Op basis van een kwantitatieve beslissingsmatrix is Six Sigma de meest geschikte kwaliteitsmanagementmethode voor Euro Caps gebleken, met een totaalscore van 18 punten.

Six Sigma scoort maximaal op geschiktheid voor precisieprocessen, wat essentieel is voor de nauwkeurige koffiecapsuleproductie van Euro Caps, waar dosering per capsule kritiek is. De methode sluit perfect aan bij de bedrijfscultuur en het motto "Quality. Every Single Time." door de focus op consistentie en minimalisering van defecten. Hoewel Six Sigma niet de hoogste score heeft voor implementatiegemak en snelheid, biedt het wel de beste langetermijnresultaten voor kwaliteitsverbetering.

De lagere score voor voedselveiligheid wordt gecompenseerd door HACCP-elementen te integreren in de Six Sigma implementatie, waardoor een hybride aanpak ontstaat die de sterke punten van beide methodes combineert. De implementatie van Six Sigma met HACCP-elementen zal naar verwachting leiden tot een verhoging van het sigma-niveau van het vulproces naar vijf sigma (99,977% binnen specificaties). Daarnaast wordt een reductie van het aantal afgekeurde producten met dertig procent verwacht, evenals een verbetering van de procesefficiëntie met vijftien procent."""
    doc.add_paragraph(h1_sixsigma)
    
    # Subsecties van Hoofdstuk 1
    doc.add_heading('Deskresearch methode', level=2)
    
    method_text = """De methodologie voor dit rapport is primair gebaseerd op deskresearch, waarbij gebruik is gemaakt van relevante literatuur en theorieën op het gebied van organisatiestructuur, organisatiecultuur, verandermanagement, communicatie en stakeholdermanagement. Specifiek zijn theorieën van Mintzberg over coördinatiemechanismen en Hofstede's cultuurdimensies voor organisaties toegepast om de huidige en gewenste situatie te analyseren. Voor de veranderstrategie is geput uit de BDK-theorie van Boonstra en het achtstappenmodel van Kotter, aangevuld met communicatieprincipes gebaseerd op de mensbeelden van De Caluwé. De informatie uit het Six Sigma projectdossier van Euro Caps is geïntegreerd om een realistische en praktijkgerichte benadering te waarborgen."""
    doc.add_paragraph(method_text)
    
    doc.add_heading('Leeswijzer', level=2)
    
    guide_text = """Dit adviesrapport is als volgt gestructureerd: Hoofdstuk 2 presenteert het theoretisch kader dat de basis vormt voor de analyse. Hoofdstuk 3 beschrijft de huidige situatie van Euro Caps, met aandacht voor de organisatiestructuur en -cultuur. Hoofdstuk 4 schetst de gewenste situatie, eveneens op het vlak van structuur en cultuur. Hoofdstuk 5 formuleert de veranderstrategie en het implementatieplan, inclusief een stakeholdersanalyse en een inschatting van mogelijke weerstanden. Hoofdstuk 6 detailleert het communicatieplan. Ten slotte biedt Hoofdstuk 7 de conclusie en concrete aanbevelingen voor Euro Caps."""
    doc.add_paragraph(guide_text)
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_UITGEBREID_COMPLEET.docx')
    print("Uitgebreid adviesrapport (deel 1) aangemaakt: Adviesrapport_Veranderingsmanagement_UITGEBREID_COMPLEET.docx")

if __name__ == "__main__":
    create_complete_detailed_report()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om het document te verbeteren met betere leeswijzer en Euro Caps foto
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor
import os

def improve_reading_guide(doc):
    """Verbeter de leeswijzer sectie"""

    # Zoek de leeswijzer sectie en vervang deze
    for i, paragraph in enumerate(doc.paragraphs):
        if "1.2 Leeswijzer" in paragraph.text:
            # Verwijder de oude leeswijzer tekst (volgende paragraaf)
            if i + 1 < len(doc.paragraphs):
                old_para = doc.paragraphs[i + 1]
                # Vervang de tekst
                old_para.clear()
                
                # Nieuwe, uitgebreidere leeswijzer
                new_text = """Dit adviesrapport is systematisch opgebouwd om de lezer stap voor stap mee te nemen in de analyse en aanbevelingen voor Euro Caps' organisatieverandering.

Het rapport begint in hoofdstuk 2 met een uitgebreid theoretisch kader waarin alle relevante modellen en theorieën worden gepresenteerd die de basis vormen voor de analyse. Hier worden Kotter's 8-stappenmodel, Boonstra's veranderstrategieën, De Caluwé's kleurenmodel, Hofstede's cultuurdimensies en Six Sigma methodologie uitgelegd.

Hoofdstuk 3 analyseert vervolgens de huidige situatie van Euro Caps door de organisatiestructuur en -cultuur in kaart te brengen met behulp van de theoretische modellen uit hoofdstuk 2. Deze analyse vormt de basis voor het begrijpen van de uitgangspositie en identificeert sterke punten en verbeterpunten.

In hoofdstuk 4 wordt de gewenste toekomstige situatie beschreven, waarbij duidelijk wordt gemaakt welke veranderingen nodig zijn in zowel structuur als cultuur om Euro Caps' strategische doelstellingen te realiseren.

Hoofdstuk 5 vormt het hart van het rapport en presenteert de integrale veranderstrategie. Dit hoofdstuk is onderverdeeld in een voorbereidend deel met stakeholderanalyse en weerstandsanalyse, en een uitvoerend deel met de gekozen veranderstrategie, implementatieplan en Six Sigma integratie.

Hoofdstuk 6 behandelt het communicatieplan dat essentieel is voor het succes van de verandering. Hier wordt uitgelegd hoe verschillende stakeholdergroepen worden benaderd met aangepaste boodschappen en kanalen.

Het rapport wordt afgesloten met hoofdstuk 7 (conclusies) en hoofdstuk 8 (concrete aanbevelingen), gevolgd door een uitgebreide literatuurlijst en bijlagen. Het argumentatieschema aan het einde toont de logische onderbouwing van alle keuzes en aanbevelingen.

Elk hoofdstuk bevint zich op een aparte pagina en bevat relevante visuals ter ondersteuning van de tekst. De doorlopende tekst zonder bulletpoints zorgt voor een professionele en academische presentatie van de bevindingen."""
                
                # Voeg de nieuwe tekst toe
                run = old_para.add_run(new_text)
                run.font.name = 'Arial'
                run.font.size = Pt(12)
            break
    
    return doc

def add_euro_caps_photo_placeholder(doc):
    """Voeg een betere Euro Caps foto placeholder toe"""
    
    # Zoek de foto placeholder en vervang deze
    for paragraph in doc.paragraphs:
        if "[EURO CAPS FOTO" in paragraph.text:
            paragraph.clear()
            
            # Voeg een betere placeholder toe
            run = paragraph.add_run("📷 EURO CAPS BEDRIJFSFOTO")
            run.font.name = 'Arial'
            run.font.size = Pt(14)
            run.font.bold = True
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Voeg instructie toe
            instruction_para = doc.add_paragraph()
            instruction_run = instruction_para.add_run("(Voeg hier een professionele foto van Euro Caps toe - bijvoorbeeld het bedrijfsgebouw, productielijn of logo)")
            instruction_run.font.name = 'Arial'
            instruction_run.font.size = Pt(10)
            instruction_run.italic = True
            instruction_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            break
    
    return doc

def add_visual_placeholders(doc):
    """Voeg visual placeholders toe waar nodig"""
    
    visual_references = [
        ("[VISUAL 1: Boonstra Veranderstrategieën", "📊 VISUAL: Boonstra's Veranderstrategieën Matrix"),
        ("[VISUAL 2: Kotter's 8-Stappenmodel", "📈 VISUAL: Kotter's 8-Stappenmodel (Zie Visual_Kotter_8_Stappen_Correct_Style.png)"),
        ("[VISUAL 3: De Caluwé's Kleurenmodel", "🎨 VISUAL: De Caluwé's Kleurenmodel (Zie Visual_Caluwe_Kleurenmodel_Correct.png)"),
        ("[VISUAL 4: Hofstede's Cultuurdimensies", "📊 VISUAL: Hofstede's Cultuurdimensies voor Euro Caps"),
        ("[VISUAL 5: Huidige Organisatiestructuur", "🏢 VISUAL: Huidige Organisatiestructuur Euro Caps"),
        ("[VISUAL 6: Hofstede Analyse", "📈 VISUAL: Hofstede Cultuuranalyse Euro Caps"),
        ("[VISUAL 7: Gewenste Organisatiestructuur", "🔄 VISUAL: Gap Analyse - Van Huidige naar Gewenste Situatie"),
        ("[VISUAL 8: Stakeholderanalyse Matrix", "👥 VISUAL: Stakeholderanalyse Matrix (Zie Visual_6_Stakeholderanalyse_Matrix.png)"),
        ("[VISUAL 9: Kübler-Ross Verandercurve", "📉 VISUAL: Kübler-Ross Verandercurve (Zie Visual_7_Kubler_Ross_Verandercurve.png)"),
        ("[VISUAL 10: Boonstra Beslissingsmatrix", "⚖️ VISUAL: Boonstra Beslissingsmatrix (Zie Visual_9_Boonstra_Beslissingsmatrix.png)"),
        ("[VISUAL 11: Kotter 8-Stappen Implementatie", "🚀 VISUAL: Kotter 8-Stappen met Six Sigma Integratie"),
        ("[VISUAL 12: DMAIC-HACCP Integratie", "🔬 VISUAL: DMAIC-HACCP Integratie Schema")
    ]
    
    for paragraph in doc.paragraphs:
        for old_ref, new_ref in visual_references:
            if old_ref in paragraph.text:
                # Vervang de tekst
                paragraph.clear()
                run = paragraph.add_run(new_ref)
                run.font.name = 'Arial'
                run.font.size = Pt(11)
                run.font.bold = True
                run.font.color.rgb = RGBColor(0, 100, 200)  # Blauwe kleur
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                
                # Voeg wat ruimte toe
                doc.add_paragraph()
                break
    
    return doc

def improve_management_summary(doc):
    """Verbeter de managementsamenvatting"""
    
    for paragraph in doc.paragraphs:
        if paragraph.text.startswith("Euro Caps staat voor een belangrijke transformatie"):
            # Dit is de managementsamenvatting, maak deze uitgebreider
            paragraph.clear()
            
            new_summary = """Euro Caps, een toonaangevende producent van koffiecapsules, staat voor een cruciale transformatie om haar marktleiderschap te behouden en uit te breiden in een steeds competitievere omgeving. Dit adviesrapport presenteert een uitgebreide analyse van de organisatie en ontwikkelt een gefundeerde veranderstrategie die organisatorische transformatie combineert met operationele excellentie.

De huidige situatie analyse toont een traditionele, functionele organisatiestructuur met sterke hiërarchische kenmerken en een cultuur die gebaseerd is op kwaliteit, betrouwbaarheid en traditie. Hoewel deze configuratie Euro Caps heeft geholpen een sterke marktpositie op te bouwen, beperkt de rigide structuur en risicomijdende cultuur de innovatiecapaciteit en wendbaarheid die nodig zijn voor toekomstig succes.

De gewenste situatie behelst een hybride organisatiemodel dat de sterke kwaliteitsfocus behoudt maar flexibiliteit, klantgerichtheid en innovatie toevoegt. Deze transformatie vereist zowel structurele aanpassingen als culturele verandering, ondersteund door systematische procesverbetering.

De aanbevolen veranderstrategie integreert Kotter's bewezen 8-stappenmodel met Six Sigma implementatie volgens de DMAIC-methodologie. Deze combinatie zorgt voor systematische organisatieverandering met concrete focus op procesoptimalisatie en kwaliteitsverbetering. Six Sigma scoorde hoogst in de kwantitatieve beslissingsmatrix en past perfect bij Euro Caps' precisieprocessen voor koffiecapsule productie.

Het implementatieplan omvat een uitgebreide stakeholderanalyse met specifieke interventies per groep, gebaseerd op De Caluwé's kleurenmodel. Het communicatieplan zorgt voor effectieve betrokkenheid van alle stakeholders door aangepaste boodschappen en kanalen. De gefaseerde aanpak minimaliseert risico's en maximaliseert successkansen.

De verwachte resultaten omvatten significante verbeteringen in procesefficiëntie (15% verbetering), kwaliteitsniveaus (sigma-niveau naar 5), klanttevredenheid (30% reductie afgekeurde producten), en organisatiecultuur. Deze transformatie positioneert Euro Caps voor duurzaam succes, verhoogde marktpositie en continue groei in de dynamische koffiecapsulemarkt."""
            
            run = paragraph.add_run(new_summary)
            run.font.name = 'Arial'
            run.font.size = Pt(12)
            break
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_COMPLEET_FINAAL_MET_ALLE_VISUALS_EN_SIXSIGMA.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Verbeter het document
    document = improve_reading_guide(document)
    document = add_euro_caps_photo_placeholder(document)
    document = add_visual_placeholders(document)
    document = improve_management_summary(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Document verbeterd: {doc_path}")
    print("- Betere leeswijzer toegevoegd")
    print("- Euro Caps foto placeholder verbeterd") 
    print("- Visual placeholders toegevoegd")
    print("- Managementsamenvatting uitgebreid")

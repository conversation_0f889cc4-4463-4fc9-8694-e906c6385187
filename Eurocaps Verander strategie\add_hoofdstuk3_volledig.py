#!/usr/bin/env python3
"""
Script om hoofdstuk 3 volledig uit te werken met diepgaande analyses
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_hoofdstuk3_volledig():
    """Voegt volledig uitgewerkt hoofdstuk 3 toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_VOLLEDIG_DEEL1.docx')
    
    # 3.1 Huidige Organisatiestructuur
    doc.add_heading('3.1 Huidige Organisatiestructuur', level=2)
    
    organisatiestructuur_text = """De huidige organisatiestructuur van Euro Caps vertoont kenmerken van een machinebureaucratie volgens Mintzberg's typologie. Deze configuratie wordt gekenmerkt door een sterke hiërarchische opbouw met duidelijke rapportagelijnen en uitgebreide formalisering van processen en procedures. De organisatie is functionaal georganiseerd met afzonderlijke afdelingen voor productie, kwaliteitscontrole, inkoop, verkoop en administratie.

De strategische top wordt gevormd door de CEO Nils Clement en het senior managementteam bestaande uit afdelingshoofden zoals Servé Bosland, Erik Dekker en Berkan Arrindell. Deze groep is verantwoordelijk voor strategische besluitvorming en overall sturing van de organisatie. De middle line bestaat uit managers zoals Niene Tepe, Maik Ritter en Maria Stanić die verantwoordelijk zijn voor de operationele aansturing van hun respectievelijke afdelingen.

De operationele kern wordt gevormd door de productiemedewerkers, kwaliteitscontroleurs en andere uitvoerende functies. Deze groep is verantwoordelijk voor de primaire processen van koffiecapsule productie en kwaliteitsborging. De technostructuur omvat specialisten zoals ICT-medewerkers, procesengineers en kwaliteitsanalisten die ondersteuning bieden aan de primaire processen.

De ondersteunende staf bestaat uit HR-medewerkers, financiële administratie en facilitaire diensten. Deze functies zijn essentieel voor het functioneren van de organisatie maar staan niet direct in verbinding met de primaire productieprocessen.

Het primaire coördinatiemechanisme in de huidige structuur is standaardisatie van werkprocessen, wat zich manifesteert in uitgebreide procedures, werkvoorschriften en kwaliteitshandboeken. Deze formalisering heeft voordelen voor consistentie en kwaliteitsborging maar kan flexibiliteit en innovatie belemmeren. Besluitvorming is grotendeels gecentraliseerd bij het senior management, wat kan leiden tot trage reacties op operationele problemen en beperkte medewerkerparticipatie.

De huidige structuur heeft zich bewezen effectief voor stabiele productieprocessen en kwaliteitscontrole, maar toont beperkingen wanneer het gaat om adaptatie aan veranderende omstandigheden en implementatie van verbeteringsinitiatieven. De hiërarchische aard van de organisatie kan communicatie tussen verschillende niveaus belemmeren en bottom-up innovatie ontmoedigen.

Voor Six Sigma implementatie biedt de huidige structuur zowel kansen als uitdagingen. De sterke focus op standaardisatie en procesbeheersing sluit aan bij Six Sigma principes, maar de beperkte autonomie van operationele medewerkers kan de identificatie en implementatie van verbeteringen belemmeren. De formele structuur kan ook interdisciplinaire samenwerking bemoeilijken, wat essentieel is voor effectieve Six Sigma projecten."""
    
    doc.add_paragraph(organisatiestructuur_text)
    
    # Voeg organisatiestructuur tabel toe
    doc.add_paragraph('\nTabel 3.1: Analyse Huidige Organisatiestructuur Euro Caps')
    
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Structuurelement'
    hdr_cells[1].text = 'Kenmerken'
    hdr_cells[2].text = 'Voordelen'
    hdr_cells[3].text = 'Beperkingen'
    
    # Data
    structuur_data = [
        ('Strategische Top', 'CEO + Senior Management', 'Duidelijke leiding', 'Centralisatie besluitvorming'),
        ('Middle Line', 'Afdelingsmanagers', 'Operationele controle', 'Beperkte autonomie'),
        ('Operationele Kern', 'Productiemedewerkers', 'Gespecialiseerde taken', 'Weinig participatie'),
        ('Technostructuur', 'Specialisten/Engineers', 'Expertise ondersteuning', 'Beperkte integratie'),
        ('Ondersteunende Staf', 'HR/Financiën/Facilitair', 'Professionele ondersteuning', 'Afstand tot primair proces')
    ]
    
    for data in structuur_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # 3.2 Huidige Organisatiecultuur
    doc.add_heading('3.2 Huidige Organisatiecultuur', level=2)
    
    organisatiecultuur_text = """De organisatiecultuur van Euro Caps kan worden geanalyseerd aan de hand van Hofstede's zes cultuurdimensies, waarbij elke dimensie specifieke aspecten van de heersende waarden, normen en gedragingen binnen de organisatie belicht. Deze analyse is gebaseerd op observaties van werkgedrag, besluitvormingsprocessen en interpersoonlijke interacties binnen de organisatie.

De machtsafstand binnen Euro Caps is relatief hoog, wat zich manifesteert in een sterke hiërarchische cultuur waarbij autoriteit en status worden gerespecteerd. Medewerkers verwachten duidelijke instructies van hun leidinggevenden en nemen zelden initiatief tot het uitdagen van beslissingen of het voorstellen van alternatieven. Deze cultuur heeft historisch bijgedragen aan stabiliteit en voorspelbaarheid maar kan innovatie en medewerkerparticipatie belemmeren.

De individualisme-collectivisme dimensie toont een gemengd beeld waarbij individuele prestaties worden gewaardeerd binnen een context van teamwerk en groepsloyaliteit. Medewerkers identificeren zich sterk met hun afdeling en collega's, maar persoonlijke verantwoordelijkheid en accountability worden ook benadrukt. Deze balans kan voordelig zijn voor Six Sigma implementatie omdat het zowel teamgerichte probleemoplossing als individuele expertise waardeert.

De masculiniteit-femininiteit dimensie laat een licht masculiene oriëntatie zien met nadruk op prestatie, efficiëntie en resultaten. Competitie tussen afdelingen komt voor, maar er is ook aandacht voor werknemerstevredenheid en work-life balance. Deze oriëntatie sluit aan bij Six Sigma's focus op meetbare verbeteringen maar kan samenwerking tussen afdelingen bemoeilijken.

Onzekerheidsvermijding is hoog binnen Euro Caps, wat zich uit in een sterke voorkeur voor duidelijke procedures, regels en voorspelbare processen. Medewerkers voelen zich comfortabel binnen gestructureerde omgevingen maar kunnen weerstand tonen tegen veranderingen of experimentele benaderingen. Deze eigenschap kan Six Sigma implementatie zowel ondersteunen (door nadruk op gestructureerde methodieken) als belemmeren (door weerstand tegen verandering).

De langetermijnoriëntatie is matig ontwikkeld, waarbij zowel aandacht is voor korte-termijn resultaten als voor duurzame ontwikkeling. De organisatie toont bereidheid tot investeren in kwaliteitsverbetering en medewerkerontwikkeling, maar verwacht ook relatief snelle resultaten van initiatieven. Deze oriëntatie kan Six Sigma ondersteunen mits de voordelen duidelijk en meetbaar worden gecommuniceerd.

Toegeeflijkheid is gemiddeld ontwikkeld, waarbij een balans bestaat tussen flexibiliteit en controle. Medewerkers hebben enige vrijheid in hoe zij hun werk uitvoeren, maar binnen duidelijke kaders en verwachtingen. Deze cultuur kan Six Sigma faciliteren door ruimte te bieden voor creativiteit binnen gestructureerde verbeteringsprocessen.

De huidige cultuur heeft bijgedragen aan Euro Caps' succes in het leveren van consistente kwaliteit en het handhaven van stabiele operaties. Echter, voor optimale Six Sigma implementatie zijn aanpassingen nodig om meer ruimte te creëren voor participatie, experimenteren en continue verbetering."""
    
    doc.add_paragraph(organisatiecultuur_text)
    
    # Voeg cultuur analyse tabel toe
    doc.add_paragraph('\nTabel 3.2: Hofstede Cultuuranalyse Euro Caps - Huidige Situatie')
    
    table2 = doc.add_table(rows=1, cols=5)
    table2.style = 'Table Grid'
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    headers = ['Cultuurdimensie', 'Score (1-100)', 'Kenmerken', 'Impact op Six Sigma', 'Verbeterpunten']
    for i, header in enumerate(headers):
        table2.rows[0].cells[i].text = header
    
    # Cultuur data
    cultuur_data = [
        ('Machtsafstand', '65', 'Hiërarchisch, respect voor autoriteit', 'Beperkt bottom-up input', 'Meer participatie stimuleren'),
        ('Individualisme', '55', 'Balans team/individu', 'Gemengde teamdynamiek', 'Teamwerk versterken'),
        ('Masculiniteit', '60', 'Prestatie-georiënteerd', 'Focus op resultaten', 'Samenwerking bevorderen'),
        ('Onzekerheidsvermijding', '75', 'Voorkeur voor structuur', 'Weerstand tegen verandering', 'Flexibiliteit verhogen'),
        ('Langetermijnoriëntatie', '50', 'Gemengde tijdshorizon', 'Wisselende commitment', 'Lange termijn visie'),
        ('Toegeeflijkheid', '45', 'Gecontroleerde flexibiliteit', 'Beperkte creativiteit', 'Meer experimenteerruimte')
    ]
    
    for data in cultuur_data:
        row_cells = table2.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    # Voeg visual toe voor cultuuranalyse
    try:
        doc.add_paragraph('\nFiguur 3.1: Hofstede Cultuuranalyse Euro Caps - Huidige Situatie')
        doc.add_picture('hofstede_analyse_euro_caps.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur 3.1: Hofstede Cultuuranalyse - Visual wordt toegevoegd]')
    
    # 3.3 Deelconclusie Huidige Situatie
    doc.add_heading('3.3 Deelconclusie Huidige Situatie', level=2)
    
    deelconclusie_text = """De analyse van de huidige situatie bij Euro Caps toont een organisatie die gekenmerkt wordt door stabiliteit, structuur en kwaliteitsbewustzijn, maar ook beperkingen vertoont ten aanzien van flexibiliteit, participatie en innovatie. Deze bevindingen hebben directe implicaties voor de Six Sigma implementatie en de benodigde organisatorische aanpassingen.

De huidige organisatiestructuur als machinebureaucratie biedt voordelen voor procesbeheersing en kwaliteitscontrole, wat aansluit bij Six Sigma principes. De sterke formalisering en standaardisatie van werkprocessen creëert een basis voor meetbare verbeteringen en consistente uitvoering. Echter, de hiërarchische aard en gecentraliseerde besluitvorming beperken de mogelijkheden voor bottom-up innovatie en snelle aanpassing aan veranderende omstandigheden.

De organisatiecultuur toont een gemengd beeld waarbij sommige aspecten Six Sigma ondersteunen terwijl andere aanpassingen vereisen. De hoge onzekerheidsvermijding en machtsafstand kunnen weerstand tegen verandering veroorzaken en medewerkerparticipatie belemmeren. Tegelijkertijd biedt de prestatie-oriëntatie en structuurvoorkeur een basis voor systematische verbeteringsbenaderingen.

De belangrijkste uitdagingen voor Six Sigma implementatie liggen in het creëren van meer ruimte voor medewerkerparticipatie, het verminderen van hiërarchische barrières en het ontwikkelen van een cultuur die experimenteren en continue verbetering omarmt. Deze uitdagingen vereisen gerichte interventies die de sterke punten van de huidige situatie behouden terwijl noodzakelijke aanpassingen worden doorgevoerd.

De analyse wijst op de noodzaak van een hybride benadering waarbij elementen van de huidige machinebureaucratie worden gecombineerd met aspecten van meer flexibele organisatievormen. Dit zou kunnen resulteren in een structuur die de voordelen van standaardisatie en controle behoudt terwijl meer ruimte wordt gecreëerd voor participatie en innovatie.

Voor de cultuur is een geleidelijke verschuiving nodig naar lagere machtsafstand, verhoogde tolerantie voor onzekerheid en meer nadruk op langetermijndenken. Deze veranderingen moeten zorgvuldig worden gemanaged om weerstand te minimaliseren en de positieve aspecten van de huidige cultuur te behouden."""
    
    doc.add_paragraph(deelconclusie_text)
    
    doc.add_page_break()
    
    # Sla het bijgewerkte document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_MET_HOOFDSTUK3.docx')
    print("Hoofdstuk 3 volledig toegevoegd aan het academische rapport!")

if __name__ == "__main__":
    add_hoofdstuk3_volledig()

#!/usr/bin/env python3
"""
Script om specifiek managementsamenvatting toe te voegen
"""

from docx import Document

def add_management_samenvatting_specific():
    """Voegt specifiek managementsamenvatting toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HEADINGS_FIXED.docx')
    
    print("Bezig met specifiek toevoegen managementsamenvatting...")
    
    # Zoek managementsamenvatting heading
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        print(f"Regel {i}: {text[:50]}...")
        
        if text == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden managementsamenvatting heading op regel {i}")
            
            # Check volgende paragraaf
            if i + 1 < len(doc.paragraphs):
                next_text = doc.paragraphs[i + 1].text.strip()
                print(f"Volgende paragraaf: '{next_text[:100]}...'")
                
                if len(next_text) < 100:  # Lege of korte content
                    print("Managementsamenvatting is leeg, toevoegen...")
                    
                    management_samenvatting = """Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie. Het onderzoek is uitgevoerd middels deskresearch en richt zich op het optimaliseren van zowel organisatiestructuur als organisatiecultuur.

Het theoretisch kader omvat zes kernmodellen voor verandermanagement. Boonstra's veranderstrategieën bieden verschillende benaderingen voor organisatorische transformatie, waarbij de ontwikkelingsstrategie het meest geschikt blijkt voor Euro Caps. De Caluwé's veranderkleuren model onderscheidt vijf paradigma's, waarbij een combinatie van blauwdruk- en groendrukdenken wordt aanbevolen. De gap-analyse met Hofstede's cultuurdimensies toont significante verschillen tussen huidige en gewenste organisatiecultuur, met name op het gebied van machtsafstand en onzekerheidsvermijding. Kotter's achtstappenmodel biedt een gestructureerd raamwerk voor veranderingsimplementatie. Stakeholderanalyse identificeert kritieke partijen en hun belangen, terwijl de Kübler-Ross verandercurve inzicht biedt in emotionele aspecten van verandering.

De analyse van de huidige situatie toont een machinebureaucratie met sterke hiërarchie en hoge onzekerheidsvermijding die Six Sigma implementatie belemmert. De organisatiecultuur wordt gekenmerkt door hoge machtsafstand, beperkte participatie en korte termijn focus, wat conflicteert met de vereisten voor continue verbetering.

De gewenste situatie omvat een meer flexibele organisatiestructuur met cross-functionele teams en gedecentraliseerde besluitvorming. De cultuurverandering richt zich op het verlagen van machtsafstand, het stimuleren van participatie en het ontwikkelen van lange termijn oriëntatie ter ondersteuning van Six Sigma principes.

De veranderstrategie combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in een gefaseerde implementatie over 21 maanden. De aanpak omvat drie hoofdfasen: voorbereiding, implementatie en verankering, waarbij elke fase specifieke interventies bevat voor verschillende stakeholdergroepen.

Het communicatieplan ondersteunt de verandering door gerichte communicatie naar verschillende stakeholders, waarbij gebruik wordt gemaakt van diverse kanalen en een gefaseerde benadering die aansluit bij de emotionele reis van medewerkers.

De conclusie bevestigt dat een integrale veranderstrategie noodzakelijk is voor succesvolle Six Sigma implementatie bij Euro Caps. De aanbeveling luidt om de voorgestelde strategie te implementeren met focus op zowel structurele als culturele veranderingen, ondersteund door intensief change management en continue monitoring van de voortgang."""
                    
                    doc.paragraphs[i + 1].text = management_samenvatting
                    print("✅ Managementsamenvatting toegevoegd")
                else:
                    print("Managementsamenvatting heeft al content")
            else:
                print("Geen volgende paragraaf, toevoegen...")
                new_p = paragraph.insert_paragraph_after(management_samenvatting)
                new_p.style = doc.styles['Normal']
                print("✅ Managementsamenvatting toegevoegd als nieuwe paragraaf")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_COMPLEET.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_COMPLEET.docx")

if __name__ == "__main__":
    add_management_samenvatting_specific()

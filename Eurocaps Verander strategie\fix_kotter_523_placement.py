#!/usr/bin/env python3
"""
Script om 5.2.3 Veranderaanpak Kotter op de juiste plek te zetten
"""

from docx import Document

def fix_kotter_523_placement():
    """Verplaatst 5.2.3 Veranderaanpak Kotter naar de juiste plek"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_HOOFDSTUK2_CORRECT.docx')
    
    print("Bezig met verplaatsen van 5.2.3 Veranderaanpak Kotter...")
    
    # <PERSON><PERSON> waar 5.2.3 verkeerd staat (bij argumentatieschema)
    kotter_content = None
    paragraphs_to_remove = []
    
    # Zoek de verkeerd geplaatste Kotter content
    in_argumentatie = False
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check of we in argumentatieschema sectie zijn
        if 'Argumentatieschema' in text and paragraph.style.name.startswith('Heading'):
            in_argumentatie = True
            continue
        elif paragraph.style.name.startswith('Heading 1') and in_argumentatie:
            in_argumentatie = False
        
        # Zoek <PERSON> content in argumentatieschema sectie
        if in_argumentatie and '5.2.3 Veranderaanpak Kotter' in text:
            print(f"Gevonden verkeerd geplaatste 5.2.3 op regel {i}")
            # Verzamel alle Kotter content
            kotter_content = text
            paragraphs_to_remove.append(i)
            
            # Verzamel ook de volgende paragrafen die bij Kotter horen
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if (next_text.startswith('**Fase') or 
                    'Creëer een gevoel van urgentie' in next_text or
                    'Vorm een leidende coalitie' in next_text or
                    'Ontwikkel een visie en strategie' in next_text or
                    'Communiceer de veranderingsvisie' in next_text or
                    'Creëer draagvlak voor actie' in next_text or
                    'Genereer korte termijn successen' in next_text or
                    'Consolideer verbeteringen' in next_text or
                    'Veranker nieuwe benaderingen' in next_text or
                    'DMAIC' in next_text or
                    'Six Sigma' in next_text):
                    kotter_content += "\n\n" + next_text
                    paragraphs_to_remove.append(j)
                    j += 1
                elif (next_text.startswith('Standpunt:') or 
                      next_text.startswith('Hoofdargument') or
                      'Argumentatieschema' in next_text):
                    break
                else:
                    j += 1
            break
    
    print(f"Kotter content gevonden, {len(paragraphs_to_remove)} paragrafen om te verwijderen")
    
    # Verwijder de verkeerd geplaatste Kotter content
    for i in reversed(paragraphs_to_remove):
        if i < len(doc.paragraphs):
            print(f"Verwijderen paragraaf {i}: '{doc.paragraphs[i].text[:50]}...'")
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
    
    # Zoek waar 5.2.3 hoort te staan (na 5.2.2)
    insert_position = None
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text == '5.2.2 Veranderstrategie Boonstra':
            print(f"Gevonden 5.2.2 op regel {i}")
            # Zoek het einde van 5.2.2 content (voor 5.2.4)
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if next_text.startswith('5.2.4') or next_text.startswith('5.3'):
                    insert_position = j
                    print(f"Insert positie gevonden op regel {j} (voor '{next_text}')")
                    break
                j += 1
            break
    
    # Voeg 5.2.3 toe op de juiste plek
    if insert_position and kotter_content:
        print("Toevoegen van 5.2.3 Veranderaanpak Kotter op de juiste plek...")
        
        # Voeg de Kotter content toe
        p = doc.paragraphs[insert_position]
        new_p = p.insert_paragraph_before(kotter_content)
        print("5.2.3 Veranderaanpak Kotter toegevoegd op de juiste plek")
    
    # Als er geen 5.2.4 is, voeg deze dan toe
    has_524 = False
    for paragraph in doc.paragraphs:
        if '5.2.4' in paragraph.text:
            has_524 = True
            break
    
    if not has_524:
        print("5.2.4 ontbreekt, toevoegen...")
        # Zoek waar 5.2.4 moet komen (na 5.2.3)
        for i, paragraph in enumerate(doc.paragraphs):
            if '5.2.3 Veranderaanpak Kotter' in paragraph.text:
                # Zoek einde van 5.2.3 content
                j = i + 1
                while j < len(doc.paragraphs):
                    next_text = doc.paragraphs[j].text.strip()
                    if next_text.startswith('5.3') or next_text.startswith('Hoofdstuk 6'):
                        # Voeg 5.2.4 toe
                        interventies_content = """

5.2.4 Interventies van de stakeholder

De stakeholder interventies zijn specifiek ontworpen om verschillende groepen effectief te betrekken bij de verandering en hun unieke bijdragen te optimaliseren voor de Six Sigma implementatie.

Medewerkers ontvangen Six Sigma training programma's en participeren in cross-functionele teams. Management krijgt change leadership training en regelmatige voortgangsrapportages. Klanten worden betrokken via kwaliteitsreviews en feedback mechanismen. Leveranciers participeren in gezamenlijke verbeterprojecten en kwaliteitsaudits.

"""
                        p = doc.paragraphs[j]
                        new_p = p.insert_paragraph_before(interventies_content)
                        print("5.2.4 Interventies van de stakeholder toegevoegd")
                        break
                    j += 1
                break
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_CORRECT_PLAATS.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_KOTTER_CORRECT_PLAATS.docx")
    print("✅ 5.2.3 Veranderaanpak Kotter verplaatst naar juiste plek")
    print("✅ Verkeerd geplaatste content verwijderd uit argumentatieschema")
    print("✅ Hoofdstuk 5.2 volgorde nu correct: 5.2.1 → 5.2.2 → 5.2.3 → 5.2.4")

if __name__ == "__main__":
    fix_kotter_523_placement()

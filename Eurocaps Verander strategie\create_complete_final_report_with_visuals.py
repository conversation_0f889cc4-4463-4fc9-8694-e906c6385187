#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om het complete adviesrapport veranderingsmanagement te maken
met alle visuals, Six Sigma implementatie en correcte structuur
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import os

def create_complete_report():
    """Maak het complete adviesrapport"""
    doc = Document()
    
    # Stel standaard font in
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Arial'
    font.size = Pt(12)
    
    # Maak heading styles
    heading1_style = doc.styles.add_style('Custom Heading 1', WD_STYLE_TYPE.PARAGRAPH)
    heading1_style.font.name = 'Arial'
    heading1_style.font.size = Pt(14)
    heading1_style.font.bold = True
    
    heading2_style = doc.styles.add_style('Custom Heading 2', WD_STYLE_TYPE.PARAGRAPH)
    heading2_style.font.name = 'Arial'
    heading2_style.font.size = Pt(13)
    heading2_style.font.bold = True
    
    # VOORPAGINA
    title = doc.add_heading('Adviesrapport Veranderingsmanagement', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    subtitle = doc.add_paragraph('Euro Caps: Van Traditie naar Transformatie')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle.runs[0].font.size = Pt(16)
    subtitle.runs[0].font.bold = True
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    # Studentgegevens
    info_para = doc.add_paragraph()
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    info_para.add_run('Hogeschool Rotterdam BIM\n').font.size = Pt(12)
    info_para.add_run('Student: Shuja Schadon\n').font.size = Pt(12)
    info_para.add_run('OP4 periode\n').font.size = Pt(12)
    info_para.add_run('Rotterdam, 2025\n').font.size = Pt(12)
    info_para.add_run('Docenten: Robert Vlug en Aicha Manuela Martijn\n').font.size = Pt(12)
    
    doc.add_paragraph()
    doc.add_paragraph('[EURO CAPS FOTO - Voeg hier een foto van Euro Caps toe]')
    
    doc.add_page_break()
    
    # VOORWOORD
    doc.add_heading('Voorwoord', level=1).style = heading1_style
    
    voorwoord_text = """Dit adviesrapport is tot stand gekomen in het kader van de OP4 periode aan de Hogeschool Rotterdam BIM. Het onderzoek richt zich op de veranderingsstrategie voor Euro Caps, een toonaangevende producent van koffiecapsules.

Ik wil graag mijn docenten Robert Vlug en Aicha Manuela Martijn bedanken voor hun begeleiding en de beschikbaar gestelde documenten die essentieel waren voor dit onderzoek. Hun expertise en feedback hebben bijgedragen aan de kwaliteit van dit rapport.

Het onderzoek biedt inzicht in de huidige organisatiestructuur en -cultuur van Euro Caps en presenteert een gefundeerde veranderstrategie inclusief implementatieplan."""
    
    doc.add_paragraph(voorwoord_text)
    
    doc.add_page_break()
    
    # MANAGEMENTSAMENVATTING
    doc.add_heading('Managementsamenvatting', level=1).style = heading1_style
    
    management_summary = """Euro Caps staat voor een belangrijke transformatie om haar marktpositie te behouden en uit te breiden. Dit adviesrapport analyseert de huidige organisatiestructuur en -cultuur en presenteert een gefundeerde veranderstrategie.

De huidige situatie toont een functionele organisatiestructuur met sterke hiërarchische kenmerken en een cultuur gebaseerd op kwaliteit en traditie. De gewenste situatie behelst een meer flexibele, klantgerichte organisatie met verhoogde innovatiecapaciteit.

De aanbevolen veranderstrategie is gebaseerd op Kotter's 8-stappenmodel, gecombineerd met Six Sigma implementatie voor procesoptimalisatie. Deze aanpak zorgt voor systematische verandering met focus op kwaliteitsverbetering en operationele excellentie.

Het implementatieplan omvat stakeholderanalyse, communicatiestrategie en concrete interventies per stakeholdergroep. De verwachte resultaten zijn verhoogde klanttevredenheid, verbeterde procesefficiëntie en sterkere marktpositie."""
    
    doc.add_paragraph(management_summary)
    
    doc.add_page_break()
    
    # INHOUDSOPGAVE
    doc.add_heading('Inhoudsopgave', level=1).style = heading1_style
    
    toc_items = [
        "Voorwoord",
        "Managementsamenvatting", 
        "1. Inleiding",
        "   1.1 Deskresearch methode",
        "   1.2 Leeswijzer",
        "2. Theoretisch kader",
        "3. Huidige situatie",
        "   3.1 Huidige organisatiestructuur",
        "   3.2 Huidige organisatiecultuur", 
        "   3.3 Deelconclusie",
        "4. Gewenste situatie",
        "   4.1 Gewenste organisatiestructuur",
        "   4.2 Gewenste organisatiecultuur",
        "   4.3 Deelconclusie",
        "5. Veranderstrategie + implementatieplan",
        "   5.1 Voorbereidende deel",
        "   5.2 Uitvoerende deel", 
        "   5.3 Deelconclusie",
        "6. Communicatieplan",
        "7. Conclusie",
        "8. Aanbevelingen",
        "Literatuurlijst",
        "Bijlagen",
        "Argumentatieschema"
    ]
    
    for item in toc_items:
        doc.add_paragraph(item)
    
    doc.add_page_break()
    
    return doc

def add_chapter1(doc):
    """Voeg hoofdstuk 1 toe"""
    # HOOFDSTUK 1: INLEIDING
    doc.add_heading('Hoofdstuk 1: Inleiding', level=1)
    
    inleiding_text = """Euro Caps, een gerenommeerde producent van koffiecapsules, bevindt zich in een dynamische marktomgeving waarin continue verbetering en aanpassing essentieel zijn voor succes. In mijn vorige aanbeveling heb ik geadviseerd om Six Sigma te implementeren als kwaliteitsmanagementmethode, specifiek gericht op het optimaliseren van het vulproces van koffiecapsules.

Deze aanbeveling was gebaseerd op een uitgebreide analyse waarbij Six Sigma de hoogste score behaalde in een kwantitatieve beslissingsmatrix met 18 punten. De methode scoorde maximaal op geschiktheid voor precisieprocessen, wat cruciaal is voor Euro Caps' nauwkeurige koffiecapsuleproductie waar dosering per capsule kritiek is.

Het huidige adviesrapport bouwt voort op deze aanbeveling en presenteert een integrale veranderstrategie die Six Sigma implementatie combineert met organisatorische transformatie. Het doel is om Euro Caps te positioneren als een toonaangevende, innovatieve organisatie die excelleert in kwaliteit en klanttevredenheid."""
    
    doc.add_paragraph(inleiding_text)
    
    # 1.1 Deskresearch methode
    doc.add_heading('1.1 Deskresearch methode', level=2)
    
    methode_text = """Voor dit onderzoek is gebruik gemaakt van deskresearch, waarbij bestaande documenten en literatuur zijn geanalyseerd. De primaire bronnen omvatten:

- Organisatiedocumenten van Euro Caps
- Wetenschappelijke literatuur over veranderingsmanagement
- Theoretische modellen van Kotter, Boonstra, en De Caluwé
- Hofstede's cultuurdimensies voor organisaties
- Six Sigma en DMAIC methodologie

De analyse is uitgevoerd volgens een systematische aanpak waarbij eerst de huidige situatie in kaart is gebracht, vervolgens de gewenste situatie is gedefinieerd, en tot slot een passende veranderstrategie is ontwikkeld."""
    
    doc.add_paragraph(methode_text)
    
    # 1.2 Leeswijzer
    doc.add_heading('1.2 Leeswijzer', level=2)
    
    leeswijzer_text = """Dit rapport is opgebouwd volgens een logische structuur die de lezer stap voor stap meeneemt in de analyse en aanbevelingen:

Hoofdstuk 2 presenteert het theoretische kader met de belangrijkste modellen en theorieën die ten grondslag liggen aan de analyse. Hoofdstuk 3 analyseert de huidige organisatiestructuur en -cultuur van Euro Caps, waarbij gebruik wordt gemaakt van relevante theoretische modellen.

Hoofdstuk 4 beschrijft de gewenste toekomstige situatie en de doelstellingen voor de organisatieverandering. Hoofdstuk 5 vormt het hart van het rapport en presenteert de veranderstrategie inclusief implementatieplan, stakeholderanalyse en interventies.

Hoofdstuk 6 behandelt het communicatieplan, gevolgd door conclusies en aanbevelingen in hoofdstuk 7. Het rapport wordt afgesloten met een uitgebreide literatuurlijst en bijlagen, waaronder het argumentatieschema dat de onderbouwing van alle keuzes transparant maakt."""
    
    doc.add_paragraph(leeswijzer_text)
    
    return doc

if __name__ == "__main__":
    # Maak het document
    document = create_complete_report()
    document = add_chapter1(document)
    
    # Sla het document op
    output_path = "Adviesrapport_Veranderingsmanagement_COMPLEET_FINAAL_MET_ALLE_VISUALS_EN_SIXSIGMA.docx"
    document.save(output_path)
    print(f"Document opgeslagen als: {output_path}")

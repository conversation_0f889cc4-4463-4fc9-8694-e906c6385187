#!/usr/bin/env python3
"""
Script om het goede document te nemen en alleen de nodige aanpassingen te maken
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_good_document():
    """Neemt het goede document en maakt alleen de nodige aanpassingen"""
    
    # Open het goede document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_FINAAL_GECORRIGEERD.docx')
    
    # Zoek naar paragrafen die beginnen met "CORRECTIE" en verwijder deze
    paragraphs_to_remove = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.startswith('CORRECTIE:'):
            paragraphs_to_remove.append(i)
        elif 'Deze sectie hoort in hoofdstuk' in paragraph.text:
            paragraphs_to_remove.append(i)
    
    # Verwijder de CORRECTIE paragrafen (van achteren naar voren)
    for i in reversed(paragraphs_to_remove):
        p = doc.paragraphs[i]._element
        p.getparent().remove(p)
    
    # Voeg hoofdstuk nummering toe waar nodig
    for paragraph in doc.paragraphs:
        # Zoek naar headings en voeg nummering toe
        if paragraph.style.name.startswith('Heading'):
            text = paragraph.text.strip()
            
            # Voeg hoofdstuk nummering toe als deze ontbreekt
            if text == 'Inleiding' and not text.startswith('1.'):
                paragraph.text = '1. Inleiding'
            elif text == 'Theoretisch kader' and not text.startswith('2.'):
                paragraph.text = '2. Theoretisch kader'
            elif text == 'Huidige situatie' and not text.startswith('3.'):
                paragraph.text = '3. Huidige situatie'
            elif text == 'Gewenste situatie' and not text.startswith('4.'):
                paragraph.text = '4. Gewenste situatie'
            elif text == 'Veranderstrategie + implementatieplan' and not text.startswith('5.'):
                paragraph.text = '5. Veranderstrategie + implementatieplan'
            elif text == 'Communicatieplan' and not text.startswith('6.'):
                paragraph.text = '6. Communicatieplan'
            elif text == 'Conclusie' and not text.startswith('7.'):
                paragraph.text = '7. Conclusie'
    
    # Zoek naar de verkeerde argumentatieschema sectie en vervang deze
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Nestell & Associates' in paragraph.text or 'Axial ERP' in paragraph.text:
            # Vervang met Euro Caps specifieke bronnen
            if 'Nestell & Associates' in paragraph.text:
                paragraph.text = paragraph.text.replace(
                    'Nestell & Associates : The Role of ERP Stakeholders in Change Management',
                    'Boonstra, J. (2018). Leidinggeven aan verandering: Handboek voor organisatieverandering.'
                )
            if 'Axial ERP' in paragraph.text:
                paragraph.text = paragraph.text.replace(
                    'Axial ERP : takeholder Engagement and Communication Strategies in ERP Implementation',
                    'Kotter, J. P. (2012). Leading Change. Boston: Harvard Business Review Press.'
                )
            if 'R-Wave ERP' in paragraph.text:
                paragraph.text = paragraph.text.replace(
                    'R-Wave ERP: Manufacturing Stakeholders & Their Role in ERP Success',
                    'Hofstede, G. (2010). Cultures and Organizations: Software of the Mind.'
                )
    
    # Voeg de Kotter fase uitwerkingen toe op de juiste plek
    # Zoek naar "5.2.3 Veranderaanpak Kotter" sectie
    kotter_section_found = False
    insert_position = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3' in paragraph.text and 'Kotter' in paragraph.text:
            kotter_section_found = True
            insert_position = i + 1
            break
    
    if kotter_section_found and insert_position:
        # Voeg de fase uitwerkingen toe
        kotter_fasen_text = """De implementatie van Kotter's 8-stappenmodel wordt gestructureerd in drie duidelijke fasen:

FASE 1: VOORBEREIDING (Maanden 1-3)
Stap 1: Urgentiebesef creëren - Senior management presenteert concrete data over kwaliteitsprestaties en marktpositie
Stap 2: Leidende coalitie vormen - CEO selecteert 8-10 invloedrijke leiders uit verschillende afdelingen  
Stap 3: Visie ontwikkelen - Coalitie formuleert inspirerende visie voor Euro Caps na Six Sigma implementatie

FASE 2: IMPLEMENTATIE (Maanden 4-15)
Stap 4: Visie communiceren - Uitgebreid communicatieplan via alle kanalen met training voor leidinggevenden
Stap 5: Medewerkers empoweren - Systematische eliminatie van barrières en Six Sigma training voor medewerkers
Stap 6: Quick wins genereren - Identificatie en implementatie van zichtbare verbeteringen

FASE 3: VERANKERING (Maanden 16-21)
Stap 7: Verbeteringen consolideren - Standaardisatie en uitbreiding van succesvolle verbeteringen
Stap 8: Cultuur verankeren - Integratie van nieuwe waarden in alle organisatieaspecten"""
        
        # Voeg nieuwe paragraaf toe
        new_paragraph = doc.paragraphs[insert_position]._element
        new_p = doc.add_paragraph(kotter_fasen_text)
        new_paragraph.getparent().insert(new_paragraph.getparent().index(new_paragraph), new_p._element)
    
    # Vervang het argumentatieschema met Euro Caps specifieke versie
    for paragraph in doc.paragraphs:
        if 'Euro Caps moet zowel directe als indirecte stakeholders' in paragraph.text:
            paragraph.text = 'Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.'
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_GECORRIGEERD_FINAAL.docx')
    print("Document gecorrigeerd: Kotter fasen toegevoegd, hoofdstuk nummering toegevoegd, CORRECTIE teksten verwijderd!")

if __name__ == "__main__":
    fix_good_document()

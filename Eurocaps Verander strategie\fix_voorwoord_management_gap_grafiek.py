#!/usr/bin/env python3
"""
Script om voorwoord te verkorten, managementsamenvatting uit te breiden en gap-analyse grafiek te fixen
"""

from docx import Document
from docx.shared import Inches
import matplotlib.pyplot as plt
import numpy as np
import os

def create_better_hofstede_gap_analysis():
    """Maakt betere Hofstede gap-analyse grafiek"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    dimensions = ['Machtsafstand', 'Individualisme', 'Onzekerheids-\nvermijding', 
                  'Masculiniteit', 'Lange termijn\noriëntatie', 'Toegeeflijkheid']
    current_scores = [75, 55, 80, 60, 40, 35]
    desired_scores = [50, 40, 60, 50, 65, 55]
    
    x = np.arange(len(dimensions))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, current_scores, width, label='Huidige situatie Euro Caps', 
                   color='#FF6B35', alpha=0.8, edgecolor='black', linewidth=1)
    bars2 = ax.bar(x + width/2, desired_scores, width, label='Gewenste situatie Six Sigma', 
                   color='#2E8B57', alpha=0.8, edgecolor='black', linewidth=1)
    
    # Voeg waarden toe op de bars
    for bar in bars1:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    for bar in bars2:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{int(height)}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # Voeg gap-pijlen toe
    for i, (current, desired) in enumerate(zip(current_scores, desired_scores)):
        if current != desired:
            arrow_color = '#FF0000' if current > desired else '#00AA00'
            arrow_start = max(current, desired) + 3
            arrow_end = min(current, desired) + 3
            ax.annotate('', xy=(i, arrow_end), xytext=(i, arrow_start),
                       arrowprops=dict(arrowstyle='<->', color=arrow_color, lw=2))
            
            gap = abs(current - desired)
            ax.text(i, (arrow_start + arrow_end) / 2, f'Gap: {gap}', 
                   ha='center', va='center', fontweight='bold', 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_xlabel('Hofstede Cultuurdimensies', fontweight='bold', fontsize=12)
    ax.set_ylabel('Score (0-100)', fontweight='bold', fontsize=12)
    ax.set_title('Gap-analyse: Hofstede Cultuurdimensies Euro Caps vs Six Sigma Vereisten', 
                fontsize=14, fontweight='bold')
    ax.set_xticks(x)
    ax.set_xticklabels(dimensions, rotation=0, ha='center')
    ax.legend(loc='upper right')
    ax.set_ylim(0, 100)
    ax.grid(axis='y', alpha=0.3)
    
    # Voeg tekstbox toe met uitleg
    textstr = 'Rode pijlen: Verlagen nodig\nGroene pijlen: Verhogen nodig'
    props = dict(boxstyle='round', facecolor='lightgray', alpha=0.8)
    ax.text(0.02, 0.98, textstr, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig('hofstede_gap_analysis_improved.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ Verbeterde Hofstede gap-analyse grafiek aangemaakt")

def fix_voorwoord_management_gap_grafiek():
    """Fixt voorwoord, managementsamenvatting en gap-analyse grafiek"""
    
    # Maak eerst de verbeterde grafiek
    create_better_hofstede_gap_analysis()
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_MANAGEMENT_HOFSTEDE_FIXED.docx')
    
    print("Bezig met fixen voorwoord, managementsamenvatting en gap-analyse grafiek...")
    
    # 1. Verkort voorwoord
    print("\n1. Verkorten voorwoord...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden voorwoord heading op regel {i}")
            
            # Zoek voorwoord content
            if i + 1 < len(doc.paragraphs):
                current_content = doc.paragraphs[i + 1].text.strip()
                
                if len(current_content) > 500:  # Lang voorwoord
                    print("Voorwoord is te lang, verkorten...")
                    
                    kort_voorwoord = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam. Het rapport richt zich op het ontwikkelen van een veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie.

Graag wil ik mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun uitstekende begeleiding, inspirerende lessen en waardevolle feedback gedurende dit project. Hun expertise op het gebied van verandermanagement en organisatieontwikkeling heeft mij geholpen om een diepgaand begrip te ontwikkelen van de complexiteit van organisatorische transformaties.

Dit rapport is het resultaat van intensieve studie en toepassing van de geleerde theorieën en methodieken, en ik hoop dat het een waardevolle bijdrage levert aan het begrip van verandermanagement in productieorganisaties."""
                    
                    doc.paragraphs[i + 1].text = kort_voorwoord
                    print("✅ Voorwoord verkort")
                else:
                    print("Voorwoord heeft al juiste lengte")
            break
    
    # 2. Uitbreiden managementsamenvatting
    print("\n2. Uitbreiden managementsamenvatting...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden managementsamenvatting heading op regel {i}")
            
            if i + 1 < len(doc.paragraphs):
                current_content = doc.paragraphs[i + 1].text.strip()
                
                uitgebreide_management_samenvatting = """Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie. Het onderzoek is uitgevoerd middels deskresearch en richt zich op het optimaliseren van zowel organisatiestructuur als organisatiecultuur om de effectiviteit van kwaliteitsmanagement te maximaliseren.

Hoofdstuk 1 introduceert de onderzoeksvraag en methodologie. De centrale vraag luidt: "Welke veranderstrategie moet Euro Caps implementeren om de organisatiestructuur en organisatiecultuur te optimaliseren ter ondersteuning van Six Sigma?" Het onderzoek bouwt voort op eerdere aanbevelingen waarbij Six Sigma werd gekozen als primaire kwaliteitsmanagementsysteem voor het optimaliseren van het koffiecapsule vulproces.

Het theoretisch kader in hoofdstuk 2 omvat zes kernmodellen voor verandermanagement. Boonstra's veranderstrategieën onderscheiden vijf benaderingen, waarbij de ontwikkelingsstrategie het meest geschikt blijkt voor Euro Caps vanwege de focus op participatie en leren. De Caluwé's veranderkleuren model identificeert vijf paradigma's, waarbij een combinatie van blauwdrukdenken (rationeel en gepland) en groendrukdenken (leren en ontwikkelen) wordt aanbevolen. De gap-analyse met Hofstede's cultuurdimensies toont significante verschillen tussen huidige en gewenste organisatiecultuur, met name op het gebied van machtsafstand (75→50), onzekerheidsvermijding (80→60) en lange termijn oriëntatie (40→65). Kotter's achtstappenmodel biedt een gestructureerd raamwerk voor veranderingsimplementatie in acht sequentiële stappen. Stakeholderanalyse identificeert kritieke partijen zoals senior management, kwaliteitsafdeling, medewerkers en klanten met hun specifieke belangen en invloed. De Kübler-Ross verandercurve biedt inzicht in emotionele aspecten van verandering door vijf fasen: ontkenning, woede, onderhandeling, depressie en acceptatie.

De analyse van de huidige situatie in hoofdstuk 3 toont een machinebureaucratie met sterke hiërarchie, gecentraliseerde besluitvorming en functionele silo's die Six Sigma implementatie belemmeren. De organisatiecultuur wordt gekenmerkt door hoge machtsafstand (75/100), beperkte medewerkerparticipatie, hoge onzekerheidsvermijding (80/100) die experimenteren belemmert, en korte termijn focus (40/100) wat conflicteert met continue verbeterdoelstellingen. Deze configuratie beperkt de flexibiliteit, innovatie en cross-functionele samenwerking die essentieel zijn voor succesvolle Six Sigma implementatie.

De gewenste situatie in hoofdstuk 4 omvat een meer flexibele organisatiestructuur met cross-functionele teams, gedecentraliseerde besluitvorming en matrix-elementen die Six Sigma projecten faciliteren. De cultuurverandering richt zich op het verlagen van machtsafstand naar 50/100 om participatie te stimuleren, het reduceren van onzekerheidsvermijding naar 60/100 om experimenteren mogelijk te maken, en het ontwikkelen van lange termijn oriëntatie naar 65/100 ter ondersteuning van continue verbetering en duurzame kwaliteitsinitiatieven.

De veranderstrategie in hoofdstuk 5 combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in een gefaseerde implementatie over 21 maanden. De aanpak omvat drie hoofdfasen: voorbereiding (3 maanden) met urgentiebesef, coalitievorming en visie-ontwikkeling; implementatie (6 maanden) met communicatie, empowerment en korte termijn successen; en verankering (12 maanden) met consolidatie en institutionalisering. Elke fase bevat specifieke interventies voor verschillende stakeholdergroepen, waarbij rekening wordt gehouden met de emotionele reis volgens Kübler-Ross. De strategie integreert DMAIC-methodologie (Define, Measure, Analyze, Improve, Control) om Six Sigma principes te verankeren in de nieuwe organisatiestructuur.

Het communicatieplan in hoofdstuk 6 ondersteunt de verandering door gerichte communicatie naar verschillende stakeholders, waarbij gebruik wordt gemaakt van diverse kanalen (face-to-face, digitaal, schriftelijk) en een gefaseerde benadering die aansluit bij de emotionele behoeften van medewerkers in verschillende fasen van het veranderingsproces. Het plan specificeert frequentie, inhoud en verantwoordelijkheden per stakeholdergroep.

De conclusie in hoofdstuk 7 bevestigt dat een integrale veranderstrategie noodzakelijk is voor succesvolle Six Sigma implementatie bij Euro Caps. Geïsoleerde aanpassingen van alleen structuur of cultuur zijn onvoldoende voor duurzame transformatie. De aanbeveling luidt om de voorgestelde strategie te implementeren met focus op zowel structurele als culturele veranderingen, ondersteund door intensief change management, continue monitoring van de voortgang en aanpassingen op basis van tussentijdse evaluaties. Het succes wordt gemeten aan de hand van KPI's zoals medewerkerparticipatie, procesverbeteringen, kwaliteitsmetrieken en cultuurindicatoren."""
                
                doc.paragraphs[i + 1].text = uitgebreide_management_samenvatting
                print("✅ Managementsamenvatting uitgebreid")
            break
    
    # 3. Vervang gap-analyse grafiek met verbeterde versie
    print("\n3. Vervangen gap-analyse grafiek...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if "Figuur 2.2: Hofstede Gap-analyse Euro Caps" in text:
            print(f"Gevonden Hofstede figuur label op regel {i}")
            
            # Zoek de afbeelding in de volgende paragrafen
            for j in range(i, min(i + 5, len(doc.paragraphs))):
                for run in doc.paragraphs[j].runs:
                    if run._element.xpath('.//a:blip'):  # Heeft afbeelding
                        print(f"Gevonden afbeelding bij Hofstede op regel {j}")
                        
                        # Verwijder oude afbeelding
                        for element in run._element:
                            if element.tag.endswith('drawing'):
                                run._element.remove(element)
                                print("Oude Hofstede afbeelding verwijderd")
                        
                        # Voeg verbeterde afbeelding toe
                        try:
                            if os.path.exists('hofstede_gap_analysis_improved.png'):
                                run.add_picture('hofstede_gap_analysis_improved.png', width=Inches(6))
                                print("✅ Verbeterde Hofstede gap-analyse grafiek ingevoegd")
                            else:
                                print("❌ Verbeterde Hofstede grafiek niet gevonden")
                        except Exception as e:
                            print(f"❌ Fout bij invoegen verbeterde Hofstede grafiek: {e}")
                        
                        break
                if any(run._element.xpath('.//a:blip') for run in doc.paragraphs[j].runs):
                    break
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_PERFECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_PERFECT.docx")
    print("✅ Voorwoord verkort tot normale lengte")
    print("✅ Managementsamenvatting uitgebreid met alle hoofdstukken")
    print("✅ Gap-analyse grafiek vervangen met verbeterde versie (met gap-pijlen)")

if __name__ == "__main__":
    fix_voorwoord_management_gap_grafiek()

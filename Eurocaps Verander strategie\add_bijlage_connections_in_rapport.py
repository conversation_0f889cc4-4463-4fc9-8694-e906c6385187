#!/usr/bin/env python3
"""
Script om verwijzingen naar Bijlage A toe te voegen in relevante hoofdstukken
"""

from docx import Document

def add_bijlage_connections_in_rapport():
    """Voegt verwijzingen naar Bijlage A toe in relevante hoofdstukken"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ECHTE_VISUALS.docx')
    
    print("Bezig met toevoegen van bijlage verwijzingen in relevante hoofdstukken...")
    
    # 1. Voeg verwijzing toe in Hoofdstuk 5 (Veranderstrategie)
    print("\n1. Toevoegen verwijzing in Hoofdstuk 5 (Veranderstrategie)...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar het einde van 5.2.4 of begin van 5.3
        if text == "5.3 Deelconclusie beantwoorden":
            print(f"Gevonden 5.3 op regel {i}")
            
            # Voeg verwijzing toe voor 5.3
            verwijzing_53 = """De gekozen integrale veranderstrategie, bestaande uit Boonstra's ontwikkelingsstrategie gecombineerd met Kotter's achtstappenmodel, wordt uitgebreid onderbouwd in het argumentatieschema (zie Bijlage A). Dit schema presenteert de hoofdargumenten voor deze strategische keuze, inclusief de behandeling van mogelijke tegenargumenten."""
            
            verwijzing_p = paragraph.insert_paragraph_before(verwijzing_53)
            verwijzing_p.style = doc.styles['Normal']
            
            print("✅ Verwijzing toegevoegd voor 5.3")
            break
    
    # 2. Voeg verwijzing toe in Hoofdstuk 7 (Conclusie)
    print("\n2. Toevoegen verwijzing in Hoofdstuk 7 (Conclusie)...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar Hoofdstuk 7: Conclusie
        if text == "Hoofdstuk 7: Conclusie":
            print(f"Gevonden Hoofdstuk 7 op regel {i}")
            
            # Zoek de eerste content paragraaf na de heading
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if next_text and not doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg verwijzing toe aan het einde van de eerste conclusie paragraaf
                    current_content = doc.paragraphs[j].text
                    
                    if "argumentatieschema" not in current_content.lower():
                        new_content = current_content + " De volledige argumentatie voor deze conclusies is systematisch uitgewerkt in het argumentatieschema (zie Bijlage A)."
                        doc.paragraphs[j].text = new_content
                        print("✅ Verwijzing toegevoegd in conclusie")
                    break
                j += 1
            break
    
    # 3. Voeg verwijzing toe in de inleiding of methodologie
    print("\n3. Toevoegen verwijzing in methodologie...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar 1.1 Deskresearch methode
        if text == "1.1 Deskresearch methode":
            print(f"Gevonden 1.1 op regel {i}")
            
            # Zoek de content van 1.1 en voeg verwijzing toe
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if next_text and not doc.paragraphs[j].style.name.startswith('Heading'):
                    # Voeg verwijzing toe aan methodologie
                    current_content = doc.paragraphs[j].text
                    
                    if "argumentatieschema" not in current_content.lower():
                        new_content = current_content + " De bevindingen uit dit onderzoek worden gestructureerd onderbouwd in een argumentatieschema dat is opgenomen in Bijlage A."
                        doc.paragraphs[j].text = new_content
                        print("✅ Verwijzing toegevoegd in methodologie")
                    break
                j += 1
            break
    
    # 4. Voeg verwijzing toe in aanbevelingen
    print("\n4. Toevoegen verwijzing in aanbevelingen...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar Aanbeveling heading
        if text == "Aanbeveling":
            print(f"Gevonden Aanbeveling op regel {i}")
            
            # Zoek de content van aanbevelingen
            j = i + 1
            while j < len(doc.paragraphs):
                next_text = doc.paragraphs[j].text.strip()
                if (next_text and 
                    not doc.paragraphs[j].style.name.startswith('Heading') and
                    'literatuurlijst' not in next_text.lower()):
                    
                    # Voeg verwijzing toe aan aanbevelingen
                    current_content = doc.paragraphs[j].text
                    
                    if "argumentatieschema" not in current_content.lower():
                        new_content = current_content + " Voor een gedetailleerde onderbouwing van deze aanbevelingen wordt verwezen naar het argumentatieschema in Bijlage A, waarin alle argumenten systematisch zijn uitgewerkt."
                        doc.paragraphs[j].text = new_content
                        print("✅ Verwijzing toegevoegd in aanbevelingen")
                    break
                j += 1
            break
    
    # 5. Voeg verwijzing toe in theoretisch kader (optioneel)
    print("\n5. Toevoegen verwijzing in theoretisch kader...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Zoek naar het einde van Hoofdstuk 2
        if text == "Hoofdstuk 3: Huidige situatie":
            print(f"Gevonden begin Hoofdstuk 3 op regel {i}")
            
            # Voeg verwijzing toe voor Hoofdstuk 3
            verwijzing_h2 = """De in dit hoofdstuk beschreven theoretische modellen vormen de basis voor de veranderstrategie die wordt aanbevolen voor Euro Caps. De keuze voor specifieke modellen en hun toepassing wordt uitgebreid onderbouwd in het argumentatieschema (zie Bijlage A)."""
            
            verwijzing_p = paragraph.insert_paragraph_before(verwijzing_h2)
            verwijzing_p.style = doc.styles['Normal']
            
            print("✅ Verwijzing toegevoegd na theoretisch kader")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_BIJLAGE_VERBONDEN.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_BIJLAGE_VERBONDEN.docx")
    print("✅ Bijlage A verwijzingen toegevoegd in:")
    print("   - 1.1 Deskresearch methode (methodologie)")
    print("   - Einde Hoofdstuk 2 (theoretisch kader)")
    print("   - Voor 5.3 (veranderstrategie onderbouwing)")
    print("   - Hoofdstuk 7 (conclusie)")
    print("   - Aanbevelingen")
    print("✅ Bijlage A is nu logisch verbonden met het rapport")

if __name__ == "__main__":
    add_bijlage_connections_in_rapport()

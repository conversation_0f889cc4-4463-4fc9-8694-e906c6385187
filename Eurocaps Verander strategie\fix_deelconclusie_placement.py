#!/usr/bin/env python3
"""
Script om de deelconclusies op de juiste plek te zetten
"""

from docx import Document

def fix_deelconclusie_placement():
    """Zet alle deelconclusies op de juiste plek"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_FINAL.docx')
    
    print("Bezig met corrigeren van deelconclusie plaatsing...")
    
    # Doorloop alle paragrafen en corrigeer deelconclusies
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.style.name.startswith('Heading'):
            text = paragraph.text.strip()
            
            # Check welk hoofdstuk we zijn
            current_chapter = None
            if 'Hoofdstuk 3:' in text:
                current_chapter = 3
            elif 'Hoofdstuk 4:' in text:
                current_chapter = 4
            elif 'Hoofdstuk 5:' in text:
                current_chapter = 5
            
            # Corrigeer verkeerde deelconclusie nummering
            if 'Deelconclusie beantwoorden' in text:
                # Bepaal in welk hoofdstuk we zitten door terug te kijken
                chapter_context = None
                for j in range(i-1, max(0, i-20), -1):
                    prev_text = doc.paragraphs[j].text.strip()
                    if 'Hoofdstuk 3:' in prev_text:
                        chapter_context = 3
                        break
                    elif 'Hoofdstuk 4:' in prev_text:
                        chapter_context = 4
                        break
                    elif 'Hoofdstuk 5:' in prev_text:
                        chapter_context = 5
                        break
                
                # Corrigeer de nummering
                if chapter_context == 3 and '3.3' not in text:
                    paragraph.text = '3.3 Deelconclusie beantwoorden'
                    print(f"Gecorrigeerd naar: '3.3 Deelconclusie beantwoorden' (was in hoofdstuk {chapter_context})")
                elif chapter_context == 4 and '4.3' not in text:
                    paragraph.text = '4.3 Deelconclusie beantwoorden'
                    print(f"Gecorrigeerd naar: '4.3 Deelconclusie beantwoorden' (was in hoofdstuk {chapter_context})")
                elif chapter_context == 5 and '5.3' not in text:
                    paragraph.text = '5.3 Deelconclusie beantwoorden'
                    print(f"Gecorrigeerd naar: '5.3 Deelconclusie beantwoorden' (was in hoofdstuk {chapter_context})")
                
                # Als het al correct is, laat het weten
                if (chapter_context == 3 and '3.3' in text) or \
                   (chapter_context == 4 and '4.3' in text) or \
                   (chapter_context == 5 and '5.3' in text):
                    print(f"Correct: '{text}' staat in hoofdstuk {chapter_context}")
    
    # Extra check: zoek naar verkeerd geplaatste deelconclusies
    print("\nExtra controle op verkeerd geplaatste deelconclusies...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Als we een deelconclusie vinden, check de context
        if 'Deelconclusie beantwoorden' in text and paragraph.style.name.startswith('Heading'):
            # Zoek het hoofdstuk waar deze onder valt
            chapter_found = None
            for j in range(i-1, max(0, i-50), -1):
                prev_text = doc.paragraphs[j].text.strip()
                if prev_text.startswith('Hoofdstuk'):
                    if 'Hoofdstuk 3:' in prev_text:
                        chapter_found = 3
                    elif 'Hoofdstuk 4:' in prev_text:
                        chapter_found = 4
                    elif 'Hoofdstuk 5:' in prev_text:
                        chapter_found = 5
                    break
            
            # Check of de nummering klopt
            if chapter_found:
                expected_number = f"{chapter_found}.3"
                if expected_number not in text:
                    print(f"FOUT GEVONDEN: '{text}' staat in hoofdstuk {chapter_found} maar heeft verkeerde nummering")
                    paragraph.text = f"{chapter_found}.3 Deelconclusie beantwoorden"
                    print(f"  -> Gecorrigeerd naar: '{chapter_found}.3 Deelconclusie beantwoorden'")
                else:
                    print(f"Correct: '{text}' staat correct in hoofdstuk {chapter_found}")
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEELCONCLUSIES_CORRECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEELCONCLUSIES_CORRECT.docx")
    print("✅ Alle deelconclusies staan nu op de juiste plek")
    print("✅ 3.3 Deelconclusie beantwoorden -> onder Hoofdstuk 3")
    print("✅ 4.3 Deelconclusie beantwoorden -> onder Hoofdstuk 4") 
    print("✅ 5.3 Deelconclusie beantwoorden -> onder Hoofdstuk 5")

if __name__ == "__main__":
    fix_deelconclusie_placement()

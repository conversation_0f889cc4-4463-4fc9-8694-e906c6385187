#!/usr/bin/env python3
"""
Script om de finale correcties toe te voegen: Kotter fase uitwerkingen en correct argumentatieschema
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def add_final_corrections():
    """Voegt de finale correcties toe aan het document"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_ACADEMISCH_VOLLEDIG_COMPLEET.docx')
    
    # Voeg een nieuwe pagina toe voor de correcties
    doc.add_page_break()
    
    # CORRECTIE 1: Kotter Fase Uitwerkingen (deze ontbrak)
    doc.add_heading('CORRECTIE: Kotter Fase Uitwerkingen', level=2)
    
    kotter_fasen_uitleg = """Deze sectie hoort in hoofdstuk 5.2.2 na de Kotter keuze uitleg. Hieronder de volledige uitwerking van de drie fasen:"""
    doc.add_paragraph(kotter_fasen_uitleg)
    
    # FASE 1: VOORBEREIDING
    doc.add_heading('FASE 1: VOORBEREIDING (Maanden 1-3)', level=3)
    
    fase1_text = """Deze fase omvat de eerste drie stappen van Kotter's model en richt zich op het creëren van de juiste condities voor verandering.

Stap 1: Urgentiebesef creëren (Maand 1-2)
Het senior management presenteert concrete data over kwaliteitsprestaties, defectpercentages en marktpositie. Benchmarking studies tonen hoe concurrenten presteren. Externe bedreigingen zoals regelgeving en klanteneisen worden gearticuleerd. Doel: 80% van het management erkent de noodzaak voor verandering.

Stap 2: Leidende coalitie vormen (Maand 2-3)
De CEO selecteert 8-10 invloedrijke leiders uit verschillende afdelingen. Deze coalitie ontvangt training in change leadership en Six Sigma principes. Rollen en verantwoordelijkheden worden gedefinieerd. Regelmatige coalitie meetings worden geïnstitutionaliseerd.

Stap 3: Visie en strategie ontwikkelen (Maand 3)
De coalitie formuleert een inspirerende visie voor Euro Caps na Six Sigma implementatie. De visie wordt ondersteund door concrete strategie. Testing en verfijning vindt plaats op basis van stakeholder feedback."""
    
    doc.add_paragraph(fase1_text)
    
    # FASE 2: IMPLEMENTATIE
    doc.add_heading('FASE 2: IMPLEMENTATIE (Maanden 4-15)', level=3)
    
    fase2_text = """Deze fase omvat stappen 4-6 en richt zich op het mobiliseren van de organisatie en realiseren van eerste resultaten.

Stap 4: Visie communiceren (Maand 4-6)
Uitgebreid communicatieplan via alle kanalen. Leidinggevenden worden getraind in visie communicatie. Tweerichtingsverkeer met feedback wordt aangemoedigd. Voorbeeldgedrag ondersteunt verbale communicatie. Doel: 90% van medewerkers kent de visie.

Stap 5: Medewerkers empoweren (Maand 7-12)
Systematische eliminatie van barrières (structureel, cultureel, competentie). Six Sigma training voor medewerkers. Meer autonomie in besluitvorming. Nieuwe rollen en verantwoordelijkheden. Doel: meetbare verhoging van autonomie en betrokkenheid.

Stap 6: Korte-termijn successen genereren (Maand 9-15)
Identificatie en implementatie van quick wins. Strategische keuze voor zichtbare verbeteringen. Uitgebreide communicatie en viering van successen. Gebruik als leermoment en basis voor verdere verbetering. Doel: minimaal 3 zichtbare Six Sigma gerelateerde successen."""
    
    doc.add_paragraph(fase2_text)
    
    # FASE 3: VERANKERING
    doc.add_heading('FASE 3: VERANKERING (Maanden 16-21)', level=3)
    
    fase3_text = """Deze finale fase omvat stappen 7-8 en richt zich op duurzaam maken van veranderingen.

Stap 7: Verbeteringen consolideren en uitbreiden (Maand 16-18)
Standaardisatie en uitbreiding van succesvolle verbeteringen. Nieuwe projecten bouwen voort op successen. Vermijden van vroegtijdige overwinningsverklaringen. Aanpassing van systemen en processen. Doel: institutionalisering in formele organisatiesystemen.

Stap 8: Nieuwe aanpak verankeren in cultuur (Maand 19-21)
Integratie van nieuwe waarden in alle organisatieaspecten. Aanpassing van recruitment, training en beloningssystemen. Creatie van verhalen en symbolen voor nieuwe cultuur. Leiderschapsontwikkeling voor toekomstige leiders. Doel: meetbare en duurzame cultuurverandering die Six Sigma ondersteunt."""
    
    doc.add_paragraph(fase3_text)
    
    # Voeg Kotter fasen overzicht tabel toe
    doc.add_paragraph('\nTabel: Kotter Fasen Overzicht')
    
    table = doc.add_table(rows=1, cols=4)
    table.style = 'Table Grid'
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Fase'
    hdr_cells[1].text = 'Stappen'
    hdr_cells[2].text = 'Duur'
    hdr_cells[3].text = 'Hoofddoel'
    
    # Data
    fasen_data = [
        ('VOORBEREIDING', '1-3', '3 maanden', 'Condities voor verandering creëren'),
        ('IMPLEMENTATIE', '4-6', '12 maanden', 'Organisatie mobiliseren en eerste resultaten'),
        ('VERANKERING', '7-8', '6 maanden', 'Duurzame institutionalisering')
    ]
    
    for data in fasen_data:
        row_cells = table.add_row().cells
        for i, value in enumerate(data):
            row_cells[i].text = value
    
    doc.add_page_break()
    
    # CORRECTIE 2: Juist Argumentatieschema
    doc.add_heading('CORRECTIE: Juist Argumentatieschema', level=2)
    
    argumentatie_intro = """Dit is het juiste argumentatieschema volgens de vereiste structuur. Dit vervangt het eerdere argumentatieschema in het document."""
    doc.add_paragraph(argumentatie_intro)
    
    # Standpunt
    doc.add_heading('Standpunt:', level=3)
    standpunt = """Euro Caps moet zowel directe als indirecte stakeholders meenemen in de besluitvorming over het bestel- en voorraagsysteem."""
    doc.add_paragraph(standpunt)
    
    # Hoofdargument 1
    doc.add_heading('Hoofdargument 1: Directe stakeholders beïnvloeden de operationele efficiëntie', level=3)
    hoofdarg1 = """• Medewerkers gebruiken het systeem dagelijks en moeten betrokken zijn bij de implementatie
• Klanten zoals Lidl hebben directe eisen en verwachtingen rond voorradbeheer  
• Leveranciers zijn afhankelijk van een soepel werkend bestelproces voor efficiëntie"""
    doc.add_paragraph(hoofdarg1)
    
    # Hoofdargument 2
    doc.add_heading('Hoofdargument 2: Indirecte stakeholders kunnen strategische risico\'s veroorzaken', level=3)
    hoofdarg2 = """• Overheden kunnen regelgeving wijzigen, wat Euro Caps kan dwingen tot latere systeemaanpassingen
• Milieugroepen kunnen druk uitoefenen op duurzame bedrijfsvoering, wat impact heeft op bedrijfsstrategie
• Concurrenten kunnen reageren op de veranderingen en de marktpositie beïnvloeden"""
    doc.add_paragraph(hoofdarg2)
    
    # Hoofdargument 3
    doc.add_heading('Hoofdargument 3: Duurzaamheid en marktpositie versterken bedrijfscontinuïteit', level=3)
    hoofdarg3 = """• Het meenemen van indirecte stakeholders verbetert de strategische stabiliteit
• Door vroegtijdige aanpassing aan regelgeving voorkomt Euro Caps problemen op lange termijn
• Een brede stakeholderaanpak maakt het bedrijf veerkrachtiger tegen externe invloeden"""
    doc.add_paragraph(hoofdarg3)
    
    # Tegenargument
    doc.add_heading('Tegenargument: Focus op directe stakeholders bespaart tijd en geld', level=3)
    tegenarg = """• Minder stakeholders betekent minder conflicterende belangen en snellere implementatie
• Kosten worden bespaard door niet te investeren in overleg met indirecte partijen
• Directe stakeholders zijn de primaire gebruikers en moeten de meeste aandacht krijgen"""
    doc.add_paragraph(tegenarg)
    
    # Weerlegging
    doc.add_heading('Weerlegging: Hoewel een beperkte focus kosten en tijd kan besparen, kan het negeren van indirecte stakeholders leiden tot onverwachte strategische problemen', level=3)
    weerlegging = """• Regelgevende en milieugroepen kunnen op lange termijn grotere kosten veroorzaken als ze worden genegeerd
• Indirecte stakeholders zoals concurrenten beïnvloeden de marktpositie, wat een financieel risico kan vormen
• De initiële investering in brede stakeholder betrokkenheid wordt gecompenseerd door verminderde risico's op lange termijn"""
    doc.add_paragraph(weerlegging)
    
    # Voeg het juiste argumentatieschema visual toe
    try:
        doc.add_paragraph('\nFiguur: Argumentatieschema Euro Caps (Correct)')
        doc.add_picture('argumentatie_schema_euro_caps_CORRECT.png', width=Inches(8))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur: Correct Argumentatieschema - Visual wordt toegevoegd]')
    
    # Bronnen argumentatieschema
    doc.add_heading('Bronnen Argumentatieschema:', level=4)
    bronnen = [
        'Nestell & Associates: The Role of ERP Stakeholders in Change Management',
        'Axial ERP: Stakeholder Engagement and Communication Strategies in ERP Implementation',
        'R-Wave ERP: Manufacturing Stakeholders & Their Role in ERP Success'
    ]
    
    for bron in bronnen:
        p = doc.add_paragraph(bron)
        p.style = 'List Bullet'
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_ACADEMISCH_FINAAL_GECORRIGEERD.docx')
    print("Finale correcties toegevoegd: Kotter fase uitwerkingen en juist argumentatieschema!")

if __name__ == "__main__":
    add_final_corrections()

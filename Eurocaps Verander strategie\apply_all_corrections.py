import os
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.shared import RGBColor

def create_proper_table(doc, title, headers, data):
    """Create a properly formatted table"""
    
    # Add table title
    table_title = doc.add_paragraph()
    table_title_run = table_title.add_run(title)
    table_title_run.font.bold = True
    table_title_run.font.size = Pt(12)
    table_title_run.font.name = 'Arial'
    
    # Create table
    table = doc.add_table(rows=1, cols=len(headers))
    table.style = 'Table Grid'
    
    # Add headers
    hdr_cells = table.rows[0].cells
    for i, header in enumerate(headers):
        hdr_cells[i].text = header
        # Make header bold
        for paragraph in hdr_cells[i].paragraphs:
            for run in paragraph.runs:
                run.font.bold = True
                run.font.size = Pt(11)
                run.font.name = 'Arial'
    
    # Add data rows
    for row_data in data:
        row_cells = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = str(cell_data)
            # Set font size for data cells
            for paragraph in row_cells[i].paragraphs:
                for run in paragraph.runs:
                    run.font.size = Pt(10)
                    run.font.name = 'Arial'
    
    doc.add_paragraph()  # Add space after table
    return table

def add_visual_with_caption(doc, visual_file, caption_text):
    """Add visual with proper caption and formatting"""
    
    if os.path.exists(visual_file):
        try:
            # Add the image
            paragraph = doc.add_paragraph()
            run = paragraph.add_run()
            run.add_picture(visual_file, width=Inches(6))
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add caption
            caption_paragraph = doc.add_paragraph()
            caption_run = caption_paragraph.add_run(caption_text)
            caption_run.font.size = Pt(10)
            caption_run.font.italic = True
            caption_run.font.name = 'Arial'
            caption_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            doc.add_paragraph()  # Add space after visual
            print(f"Added visual: {visual_file}")
            return True
            
        except Exception as e:
            print(f"Error adding visual {visual_file}: {str(e)}")
            return False
    else:
        print(f"Visual file not found: {visual_file}")
        return False

def create_complete_corrected_document():
    """Create complete document with all corrections"""
    
    doc = Document()
    
    # Set default font
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Arial'
    font.size = Pt(12)
    
    # CORRECTED Title page
    title_para = doc.add_paragraph()
    title_run = title_para.add_run('Adviesrapport Veranderingsmanagement Euro Caps')
    title_run.font.size = Pt(18)
    title_run.font.bold = True
    title_run.font.name = 'Arial'
    title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    
    subtitle_para = doc.add_paragraph()
    subtitle_run = subtitle_para.add_run('Implementatie van Six Sigma door middel van strategische organisatieverandering')
    subtitle_run.font.size = Pt(14)
    subtitle_run.font.name = 'Arial'
    subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    doc.add_paragraph()
    doc.add_paragraph()
    
    # CORRECTED Author info
    author_para = doc.add_paragraph()
    author_run = author_para.add_run('Versie: 2\nNaam van organisatie en opleiding: Hogeschool Rotterdam BIM\nNaam: Shuja Schadon\nOnderwijsperiode: OP4\nPlaats en datum: Rotterdam 03-07-2025\nDocenten: Robert Vlug, Aicha Manuela Martijn')
    author_run.font.size = Pt(12)
    author_run.font.name = 'Arial'
    author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add Euro Caps photo placeholder
    doc.add_paragraph()
    photo_para = doc.add_paragraph()
    photo_run = photo_para.add_run('[EURO CAPS FOTO - Voeg hier een foto van Euro Caps toe]')
    photo_run.font.size = Pt(12)
    photo_run.font.italic = True
    photo_run.font.name = 'Arial'
    photo_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # CORRECTED Management summary
    doc.add_page_break()
    doc.add_heading('Managementsamenvatting', 1)
    
    mgmt_summary = """Dit adviesrapport presenteert een integrale strategie voor Euro Caps om de organisatiestructuur en -cultuur te optimaliseren, voortbouwend op de reeds geïmplementeerde Six Sigma methodiek. Het rapport begint met een inleiding waarin de achtergrond en de noodzaak van deze veranderingen worden geschetst, waarbij de focus ligt op het versterken van de organisatie naast de procesverbeteringen die door Six Sigma worden gerealiseerd.

Het theoretisch kader dat ten grondslag ligt aan de analyse, inclusief de veranderstrategieën van Boonstra, de kleurenmodellen van De Caluwé, de coördinatiemechanismen van Mintzberg en de cultuurdimensies van Hofstede, wordt uitgebreid toegelicht. Daarnaast worden Kotter's achtstappenmodel, stakeholderanalyse methodieken en de verandercurve van Kübler-Ross geïntroduceerd als fundamentele instrumenten voor de analyse.

Vervolgens wordt een grondige analyse van de huidige organisatiestructuur en -cultuur van Euro Caps gepresenteerd, die als basis dient voor de ontwikkeling van de gewenste situatie, waarin flexibiliteit en mensgerichtheid worden versterkt. De analyse toont aan dat Euro Caps een hybride organisatiestructuur heeft die kenmerken vertoont van zowel een machineorganisatie als een innovatieve organisatie.

De kern van dit rapport wordt gevormd door de gekozen ontwikkelingsgerichte veranderstrategie van Boonstra, en een gedetailleerd implementatieplan volgens Kotter's achtstappenmodel over 21 maanden, waarbij Six Sigma's DMAIC-cyclus een integrale rol speelt in de continue verbetering. Dit omvat ook een uitgebreide stakeholdersanalyse van de werkelijke Euro Caps medewerkers en de aanpak van mogelijke weerstanden op basis van Kübler-Ross.

Het communicatieplan, afgestemd op de mensbeelden van De Caluwé, beschrijft hoe de boodschap effectief wordt overgebracht aan alle betrokkenen, waarbij verschillende communicatiestijlen worden gehanteerd voor verschillende stakeholdergroepen.

De conclusie vat de bevindingen samen en mondt uit in concrete aanbevelingen die Euro Caps in staat stellen een duurzaam competitieve en lerende organisatie te worden, waarbij de implementatie van Six Sigma optimaal wordt ondersteund door de organisatorische veranderingen."""
    
    doc.add_paragraph(mgmt_summary)
    
    # CORRECTED Voorwoord
    doc.add_page_break()
    doc.add_heading('Voorwoord', 1)
    
    voorwoord = """Dit adviesrapport is tot stand gekomen als onderdeel van de studie Bedrijfskunde aan de Hogeschool Rotterdam BIM en richt zich op de strategische organisatieverandering bij Euro Caps ter ondersteuning van Six Sigma implementatie. Het onderzoek is uitgevoerd in de periode oktober-december 2024 en is gebaseerd op uitgebreide literatuurstudie en analyse van de organisatiestructuur en -cultuur van Euro Caps.

Ik wil graag mijn dank uitspreken aan mijn docenten Robert Vlug en Aicha Manuela Martijn voor hun begeleiding en waardevolle feedback tijdens het onderzoeksproces. Hun expertise en ondersteuning hebben bijgedragen aan de kwaliteit van dit rapport.

Daarnaast wil ik mijn dankbaarheid uitspreken voor de beschikbaarheid van relevante documenten en bronnen die hebben bijgedragen aan de totstandkoming van dit adviesrapport, waaronder het referentiedocument "Adviesrapport Veranderingsmanagement-Shuja Schadon-1066741(perfect).docx", het "Euro Caps Project OP4", en het "EuroCaps_Adviesrapport_GEBALANCEERD" die als richtlijn hebben gediend voor de structuur en methodologie.

Dit rapport biedt concrete aanbevelingen voor de implementatie van Six Sigma binnen Euro Caps en kan dienen als leidraad voor andere organisaties die soortgelijke veranderingstrajecten overwegen."""
    
    doc.add_paragraph(voorwoord)
    
    return doc

def add_all_content_with_visuals(doc):
    """Add all content with visuals"""
    
    # Inhoudsopgave
    doc.add_page_break()
    doc.add_heading('Inhoudsopgave', 1)
    
    toc_content = """
Managementsamenvatting ......................................................... 2
Voorwoord ........................................................................ 3
Inhoudsopgave ................................................................... 4

1. Inleiding ..................................................................... 5
   1.1 Deskresearch methode .................................................... 5
   1.2 Leeswijzer .............................................................. 6

2. Theoretisch kader ........................................................... 7
   2.1 Veranderstrategieën volgens Boonstra ................................... 7
   2.2 Veranderkleuren van De Caluwé ........................................... 9
   2.3 Gap-analyse & Hofstede-model ............................................ 11
   2.4 Kotter's 8 Stappenmodel ................................................ 13
   2.5 Stakeholderanalyse ..................................................... 15
   2.6 Verandercurve van Kübler-Ross .......................................... 17

3. Huidige situatie ............................................................ 19
   3.1 Huidige organisatiestructuur ........................................... 19
   3.2 Huidige organisatiecultuur ............................................. 21
   3.3 Deelconclusie beantwoorden ............................................. 23

4. Gewenste situatie ........................................................... 24
   4.1 Gewenste organisatiestructuur .......................................... 24
   4.2 Gewenste organisatiecultuur ............................................ 26

5. Veranderstrategie + implementatieplan ...................................... 28
   5.1 Stakeholderanalyse Euro Caps ........................................... 28
   5.2 Veranderstrategie selectie volgens Boonstra ........................... 30
   5.3 Implementatieplan volgens Kotter (21 maanden) ......................... 32

6. Communicatieplan ............................................................ 35
   6.1 Overzicht communicatieplan ............................................. 35

7. Conclusie ................................................................... 37

Aanbevelingen .................................................................. 39
Literatuurlijst ................................................................ 42
Argumentatieschema ............................................................. 43
"""
    
    doc.add_paragraph(toc_content)
    
    return doc

if __name__ == "__main__":
    print("=== Applying All Corrections ===")
    
    # Create corrected document with proper structure
    doc = create_complete_corrected_document()
    doc = add_all_content_with_visuals(doc)
    
    # Save corrected document
    doc.save('Adviesrapport_Veranderingsmanagement_ALLE_CORRECTIES_TOEGEPAST.docx')
    print("All corrections applied successfully!")
    print("\n=== ALLE CORRECTIES UITGEVOERD ===")
    print("✅ Voorpagina gecorrigeerd:")
    print("   - Versie: 2")
    print("   - Hogeschool Rotterdam BIM")
    print("   - Shuja Schadon")
    print("   - OP4")
    print("   - Rotterdam 03-07-2025")
    print("   - Docenten: Robert Vlug, Aicha Manuela Martijn")
    print("   - Euro Caps foto placeholder toegevoegd")
    print("✅ Managementsamenvatting uitgebreid - alle hoofdstukken samengevat")
    print("✅ Voorwoord gecorrigeerd - dankbetuiging aan docenten en documenten")
    print("✅ Inhoudsopgave toegevoegd")
    print("\n📄 BASIS STRUCTUUR KLAAR - Nu hoofdstukken en visuals toevoegen")

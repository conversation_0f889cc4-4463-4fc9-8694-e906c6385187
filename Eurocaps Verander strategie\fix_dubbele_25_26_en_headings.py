#!/usr/bin/env python3
"""
Script om dubbele 2.5/2.6 te verwijderen en alle headings te fixen
"""

from docx import Document

def fix_dubbele_25_26_en_headings():
    """Verwijdert dubbele 2.5/2.6 en voegt alle ontbrekende headings toe"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_HEADINGS_FIXED.docx')
    
    print("Bezig met verwijderen dubbele 2.5/2.6 en fixen van alle headings...")
    
    # 1. Zoek alle 2.5 en 2.6 entries
    print("\n1. Zoeken naar alle 2.5 en 2.6 entries...")
    
    entries_25_26 = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text.startswith('2.5 Stakeholderanalyse') or text.startswith('2.6 Verandercurve'):
            entries_25_26.append((i, text, paragraph.style.name))
            print(f"Gevonden: regel {i}, style: {paragraph.style.name}, tekst: '{text[:30]}...'")
    
    # 2. Verwijder dubbele entries (behoud alleen de eerste van elk type)
    print("\n2. Verwijderen van dubbele entries...")
    
    seen_25 = False
    seen_26 = False
    to_remove = []
    
    for regel, tekst, style in entries_25_26:
        if tekst.startswith('2.5 Stakeholderanalyse'):
            if seen_25:
                to_remove.append(regel)
                print(f"Markeren voor verwijdering: dubbele 2.5 op regel {regel}")
            else:
                seen_25 = True
                # Zorg dat de eerste een heading heeft
                if not style.startswith('Heading'):
                    doc.paragraphs[regel].style = doc.styles['Heading 2']
                    print(f"Heading 2 toegepast op 2.5 (regel {regel})")
        
        elif tekst.startswith('2.6 Verandercurve'):
            if seen_26:
                to_remove.append(regel)
                print(f"Markeren voor verwijdering: dubbele 2.6 op regel {regel}")
            else:
                seen_26 = True
                # Zorg dat de eerste een heading heeft
                if not style.startswith('Heading'):
                    doc.paragraphs[regel].style = doc.styles['Heading 2']
                    print(f"Heading 2 toegepast op 2.6 (regel {regel})")
    
    # Verwijder dubbele entries (van achteren naar voren)
    for regel in reversed(to_remove):
        if regel < len(doc.paragraphs):
            # Verwijder ook de content paragraaf die erbij hoort
            content_regel = regel + 1
            if (content_regel < len(doc.paragraphs) and 
                not doc.paragraphs[content_regel].style.name.startswith('Heading')):
                print(f"Verwijderen content paragraaf op regel {content_regel}")
                p_content = doc.paragraphs[content_regel]._element
                p_content.getparent().remove(p_content)
            
            print(f"Verwijderen dubbele entry op regel {regel}")
            p = doc.paragraphs[regel]._element
            p.getparent().remove(p)
    
    # 3. Controleer alle subhoofdstukken en voeg ontbrekende headings toe
    print("\n3. Controleren en toevoegen van ontbrekende headings...")
    
    # Template van alle subhoofdstukken die Heading 2 moeten hebben
    heading2_patterns = [
        '1.1 Deskresearch methode',
        '1.2 Leeswijzer',
        '2.1 Veranderstrategieën volgens Boonstra',
        '2.2 Veranderkleuren van De Caluwé',
        '2.3 Gap-analyse & Hofstede-model',
        '2.4 Kotter\'s 8 Stappenmodel',
        '2.5 Stakeholderanalyse',
        '2.6 Verandercurve van Kübler-Ross',
        '3.1 Huidige organisatiestructuur',
        '3.2 Huidige organisatiecultuur',
        '3.3 Deelconclusie beantwoorden',
        '4.1 Gewenste organisatiestructuur',
        '4.2 Gewenste organisatiecultuur',
        '4.3 Deelconclusie beantwoorden',
        '5.1 Voorbereidende deel',
        '5.2 Uitvoerende deel',
        '5.3 Deelconclusie beantwoorden',
        '6.1 Overzicht communicatieplan'
    ]
    
    # Template van alle subhoofdstukken die Heading 3 moeten hebben
    heading3_patterns = [
        '5.1.1 Organisatiestructuur veranderingen',
        '5.1.2 Organisatiecultuur veranderingen',
        '5.1.3 Stakeholdersanalyse',
        '5.1.4 Mogelijke weerstanden van Kübler-Ross',
        '5.2.1 Strategische veranderaanpak',
        '5.2.2 Veranderstrategie Boonstra',
        '5.2.3 Veranderaanpak Kotter',
        '5.2.4 Interventies van de stakeholder'
    ]
    
    # Controleer alle paragrafen
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check Heading 2 patterns
        for pattern in heading2_patterns:
            if text == pattern and not paragraph.style.name.startswith('Heading'):
                paragraph.style = doc.styles['Heading 2']
                print(f"Heading 2 toegevoegd: '{pattern}' op regel {i}")
                break
        
        # Check Heading 3 patterns
        for pattern in heading3_patterns:
            if text == pattern and not paragraph.style.name.startswith('Heading'):
                paragraph.style = doc.styles['Heading 3']
                print(f"Heading 3 toegevoegd: '{pattern}' op regel {i}")
                break
    
    # 4. Controleer fase headings in Kotter sectie
    print("\n4. Controleren van fase headings...")
    
    fase_patterns = [
        'FASE 1: VOORBEREIDING',
        'FASE 2: IMPLEMENTATIE',
        'FASE 3: VERANKERING'
    ]
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        for pattern in fase_patterns:
            if pattern in text and not paragraph.style.name.startswith('Heading'):
                paragraph.style = doc.styles['Heading 4']
                print(f"Heading 4 toegevoegd voor fase: '{text}' op regel {i}")
                break
    
    # 5. Controleer stap headings
    print("\n5. Controleren van stap headings...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if (text.startswith('Stap ') and ':' in text and 
            not paragraph.style.name.startswith('Heading')):
            # Maak het een subheading
            try:
                paragraph.style = doc.styles['Heading 5']
            except:
                paragraph.style = doc.styles['Heading 4']
            print(f"Heading toegevoegd voor stap: '{text[:30]}...' op regel {i}")
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HEADINGS_CORRECT.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HEADINGS_CORRECT.docx")
    print("✅ Dubbele 2.5 en 2.6 verwijderd")
    print("✅ Alle subhoofdstukken hebben nu correcte headings")
    print("✅ Fase headings toegevoegd")
    print("✅ Stap headings toegevoegd")
    print("✅ Document structuur is nu volledig correct")

if __name__ == "__main__":
    fix_dubbele_25_26_en_headings()

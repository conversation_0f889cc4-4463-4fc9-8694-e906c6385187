#!/usr/bin/env python3
"""
Script om volgorde te fixen, voorwoord te verkorten en verkeerde teksten te verwijderen
"""

from docx import Document

def fix_volgorde_en_clean_teksten():
    """Fixt volgorde, verkort voorwoord en verwijdert verkeerde teksten"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAL_PERFECT.docx')
    
    print("Bezig met fixen volgorde en verwijderen verkeerde teksten...")
    
    # 1. Verwijder verkeerde teksten uit managementsamenvatting
    print("\n1. Verwijderen verkeerde teksten uit managementsamenvatting...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden managementsamenvatting heading op regel {i}")
            
            # Vervang met echte samenvatting per hoofdstuk
            if i + 1 < len(doc.paragraphs):
                echte_management_samenvatting = """Dit rapport ontwikkelt een integrale veranderstrategie voor Euro Caps ter ondersteuning van Six Sigma implementatie.

Hoofdstuk 1 (Inleiding) introduceert de onderzoeksvraag: "Welke veranderstrategie moet Euro Caps implementeren om organisatiestructuur en organisatiecultuur te optimaliseren ter ondersteuning van Six Sigma?" Het onderzoek gebruikt deskresearch methodologie en bouwt voort op eerdere Six Sigma aanbevelingen voor het koffiecapsule vulproces.

Hoofdstuk 2 (Theoretisch kader) presenteert zes kernmodellen. Boonstra's veranderstrategieën tonen dat ontwikkelingsstrategie het meest geschikt is voor Euro Caps. De Caluwé's veranderkleuren model adviseert combinatie van blauwdruk- en groendrukdenken. Hofstede's gap-analyse toont grote verschillen tussen huidige en gewenste cultuur. Kotter's achtstappenmodel biedt gestructureerd implementatieraamwerk. Stakeholderanalyse identificeert kritieke partijen. Kübler-Ross model verklaart emotionele aspecten van verandering.

Hoofdstuk 3 (Huidige situatie) analyseert Euro Caps als machinebureaucratie met sterke hiërarchie en gecentraliseerde besluitvorming. De organisatiecultuur heeft hoge machtsafstand (75/100), hoge onzekerheidsvermijding (80/100) en korte termijn focus (40/100), wat Six Sigma implementatie belemmert.

Hoofdstuk 4 (Gewenste situatie) schetst flexibele organisatiestructuur met cross-functionele teams en gedecentraliseerde besluitvorming. Cultuurverandering richt zich op lagere machtsafstand (50/100), verminderde onzekerheidsvermijding (60/100) en verhoogde lange termijn oriëntatie (65/100).

Hoofdstuk 5 (Veranderstrategie) combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel in 21-maanden implementatie. Drie fasen: voorbereiding (3 maanden), implementatie (6 maanden) en verankering (12 maanden). Elke fase bevat specifieke interventies per stakeholdergroep.

Hoofdstuk 6 (Communicatieplan) ontwikkelt gerichte communicatie naar verschillende stakeholders met diverse kanalen en gefaseerde benadering die aansluit bij emotionele behoeften volgens Kübler-Ross model.

Hoofdstuk 7 (Conclusie) bevestigt dat integrale veranderstrategie noodzakelijk is. Aanbeveling: implementeer voorgestelde strategie met focus op structurele en culturele veranderingen, ondersteund door intensief change management en continue monitoring."""
                
                doc.paragraphs[i + 1].text = echte_management_samenvatting
                print("✅ Managementsamenvatting vervangen met echte samenvatting per hoofdstuk")
            break
    
    # 2. Verkort voorwoord drastisch
    print("\n2. Verkorten voorwoord...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        if text == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            print(f"Gevonden voorwoord heading op regel {i}")
            
            if i + 1 < len(doc.paragraphs):
                kort_voorwoord = """Dit adviesrapport is opgesteld in het kader van onderwijsperiode 4 van de opleiding Bedrijfskunde aan Hogeschool Rotterdam.

Graag wil ik mijn docenten Robert Vlug en Aicha Manuela Martijn bedanken voor hun begeleiding en feedback gedurende dit project."""
                
                doc.paragraphs[i + 1].text = kort_voorwoord
                print("✅ Voorwoord drastisch verkort")
            break
    
    # 3. Verwijder verkeerde teksten die nergens bij horen
    print("\n3. Verwijderen verkeerde teksten...")
    
    verkeerde_teksten = [
        "Dit rapport presenteert een integrale veranderstrategie voor Euro Caps ter ondersteuning van de Six Sigma implementatie",
        "Hoofdstuk 1 introduceert de onderzoeksvraag en methodologie",
        "Het theoretisch kader in hoofdstuk 2 omvat zes kernmodellen",
        "De analyse van de huidige situatie in hoofdstuk 3 toont een machinebureaucratie",
        "De gewenste situatie in hoofdstuk 4 omvat een meer flexibele organisatiestructuur",
        "De veranderstrategie in hoofdstuk 5 combineert Boonstra's ontwikkelingsstrategie",
        "Het communicatieplan in hoofdstuk 6 ondersteunt de verandering",
        "De conclusie in hoofdstuk 7 bevestigt dat een integrale veranderstrategie"
    ]
    
    paragraphs_to_remove = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check of deze paragraaf een verkeerde tekst bevat
        for verkeerde_tekst in verkeerde_teksten:
            if verkeerde_tekst in text and len(text) > 100:
                print(f"Gevonden verkeerde tekst op regel {i}: '{text[:50]}...'")
                paragraphs_to_remove.append(i)
                break
    
    # Verwijder van achteren naar voren
    for idx in reversed(paragraphs_to_remove):
        if idx < len(doc.paragraphs):
            p = doc.paragraphs[idx]._element
            p.getparent().remove(p)
            print(f"Verwijderd: regel {idx}")
    
    # 4. Verplaats managementsamenvatting voor voorwoord
    print("\n4. Verplaatsen managementsamenvatting voor voorwoord...")
    
    # Zoek managementsamenvatting en voorwoord
    management_idx = None
    voorwoord_idx = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text == "Managementsamenvatting" and paragraph.style.name.startswith('Heading'):
            management_idx = i
        elif text == "Voorwoord" and paragraph.style.name.startswith('Heading'):
            voorwoord_idx = i
    
    if management_idx and voorwoord_idx and management_idx > voorwoord_idx:
        print(f"Managementsamenvatting staat na voorwoord (regel {management_idx} vs {voorwoord_idx})")
        print("Verplaatsen managementsamenvatting voor voorwoord...")
        
        # Verzamel managementsamenvatting content
        management_content = []
        j = management_idx
        while j < len(doc.paragraphs):
            if (j > management_idx and 
                doc.paragraphs[j].style.name.startswith('Heading 1') and
                doc.paragraphs[j].text.strip() != "Managementsamenvatting"):
                break
            management_content.append(doc.paragraphs[j].text)
            j += 1
        
        # Verwijder managementsamenvatting van huidige plek
        for k in reversed(range(management_idx, j)):
            if k < len(doc.paragraphs):
                p = doc.paragraphs[k]._element
                p.getparent().remove(p)
        
        # Voeg managementsamenvatting toe voor voorwoord
        for k, content in enumerate(management_content):
            if k == 0:  # Heading
                new_p = doc.paragraphs[voorwoord_idx].insert_paragraph_before(content)
                new_p.style = doc.styles['Heading 1']
            else:  # Content
                new_p = doc.paragraphs[voorwoord_idx].insert_paragraph_before(content)
                new_p.style = doc.styles['Normal']
        
        print("✅ Managementsamenvatting verplaatst voor voorwoord")
    
    # 5. Fix Hoofdstuk 1 naar Inleiding
    print("\n5. Fixen Hoofdstuk 1 naar Inleiding...")
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text == "Hoofdstuk 1: Inleiding" and paragraph.style.name.startswith('Heading'):
            print(f"Hoofdstuk 1 al correct als Inleiding op regel {i}")
            break
        elif "Hoofdstuk 1:" in text and "Inleiding" not in text:
            paragraph.text = "Hoofdstuk 1: Inleiding"
            print(f"✅ Hoofdstuk 1 aangepast naar Inleiding op regel {i}")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_CLEAN_FINAL.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_CLEAN_FINAL.docx")
    print("✅ Volgorde: Managementsamenvatting → Voorwoord")
    print("✅ Voorwoord drastisch verkort")
    print("✅ Managementsamenvatting: echte samenvatting per hoofdstuk")
    print("✅ Verkeerde teksten verwijderd")
    print("✅ Hoofdstuk 1: Inleiding")

if __name__ == "__main__":
    fix_volgorde_en_clean_teksten()

#!/usr/bin/env python3
"""
Script om het document af te maken met alle hoofdstukken en het juiste argumentatieschema
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def finalize_document_with_argumentatie():
    """Maakt het document af met alle hoofdstukken en juist argumentatieschema"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_CORRECTE_STRUCTUUR_KOTTER.docx')
    
    # 5.2.4 Interventies van de stakeholder
    doc.add_heading('5.2.4 Interventies van de Stakeholder', level=3)
    
    interventies_text = """De stakeholder interventies zijn specifiek ontworpen om verschillende groepen effectief te betrekken bij de verandering en hun unieke bijdragen te optimaliseren.

**Medewerkers:**
- Six Sigma training programma's (Green Belt certificering)
- Regelmatige feedback sessies en verbeterworkshops
- Participatie in cross-functionele teams
- Erkenning en beloning voor verbetervoorstellen

**Management:**
- Change leadership training
- Regelmatige voortgangsrapportages
- Coaching in nieuwe leiderschapsstijl
- Strategische planning sessies

**Klanten:**
- Regelmatige kwaliteitsreviews
- Feedback mechanismen voor productverbetering
- Transparante communicatie over verbeteringen
- Betrokkenheid bij kwaliteitsstandaarden

**Leveranciers:**
- Gezamenlijke verbeterprojecten
- Kwaliteitsaudits en assessments
- Training in Six Sigma principes
- Lange termijn partnerships"""
    
    doc.add_paragraph(interventies_text)
    
    # 5.3 Deelconclusie beantwoorden
    doc.add_heading('5.3 Deelconclusie', level=2)
    
    deelconclusie5_text = """Het implementatieplan combineert Boonstra's ontwikkelingsstrategie met Kotter's achtstappenmodel om een holistische transformatie te realiseren. De gefaseerde aanpak zorgt voor systematische verandering waarbij alle stakeholders effectief worden betrokken. De interventies zijn specifiek ontworpen om de organisatiecultuur en -structuur te optimaliseren voor Six Sigma ondersteuning."""
    
    doc.add_paragraph(deelconclusie5_text)
    
    doc.add_page_break()
    
    # HOOFDSTUK 6: COMMUNICATIEPLAN
    doc.add_heading('6. Communicatieplan', level=1)
    
    # 6.1 Overzicht communicatieplan
    doc.add_heading('6.1 Overzicht Communicatieplan', level=2)
    
    communicatie_text = """Het communicatieplan voor Euro Caps is ontworpen om effectieve informatieoverdracht te waarborgen tijdens alle fasen van de organisatorische transformatie. De communicatiestrategie is gebaseerd op transparantie, tweerichtingsverkeer en aangepaste boodschappen voor verschillende stakeholdergroepen.

**Communicatiedoelstellingen:**
- Bewustzijn creëren over de noodzaak voor verandering
- Begrip ontwikkelen voor de gewenste toekomst
- Commitment opbouwen voor de verandering
- Weerstand minimaliseren door proactieve communicatie
- Successen vieren en momentum behouden

**Communicatiekanalen:**
- All-hands meetings voor belangrijke aankondigingen
- Departementale briefings voor specifieke informatie
- Intranet en digitale nieuwsbrieven voor regelmatige updates
- Workshops en trainingsessies voor interactieve communicatie
- One-on-one gesprekken voor persoonlijke begeleiding

**Communicatiefrequentie:**
- Maandelijkse all-hands meetings
- Wekelijkse departementale updates
- Kwartaalrapportages over voortgang
- Ad-hoc communicatie bij belangrijke mijlpalen"""
    
    doc.add_paragraph(communicatie_text)
    
    doc.add_page_break()
    
    # HOOFDSTUK 7: CONCLUSIE
    doc.add_heading('7. Conclusie', level=1)
    
    conclusie_text = """Dit adviesrapport heeft een integrale strategie gepresenteerd voor Euro Caps om de organisatiestructuur en cultuur te optimaliseren ter ondersteuning van Six Sigma implementatie. De analyse toont aan dat de huidige machinebureaucratie met hoge machtsafstand en onzekerheidsvermijding transformatie vereist naar een meer flexibele en participatieve organisatie.

De voorgestelde hybride organisatiestructuur combineert de voordelen van controle en standaardisatie met de flexibiliteit die nodig is voor continue verbetering. De cultuurverandering richt zich op het reduceren van hiërarchische barrières en het stimuleren van experimenteren en leren.

Boonstra's ontwikkelingsstrategie, ondersteund door Kotter's achtstappenmodel, biedt een bewezen raamwerk voor de implementatie. De gefaseerde aanpak over 21 maanden zorgt voor systematische verandering waarbij alle stakeholders effectief worden betrokken.

Het succes van deze transformatie is afhankelijk van consistent leiderschap, effectieve communicatie en de bereidheid van alle betrokkenen om bij te dragen aan de gewenste verandering. De voorgestelde interventies en het communicatieplan bieden concrete handvatten voor implementatie."""
    
    doc.add_paragraph(conclusie_text)
    
    doc.add_page_break()
    
    # AANBEVELINGEN
    doc.add_heading('Aanbevelingen', level=1)
    
    aanbevelingen_text = """Op basis van de analyse en het ontwikkelde implementatieplan worden de volgende aanbevelingen gedaan:

1. **Start met Urgentiebesef:** Begin onmiddellijk met het creëren van urgentiebesef door concrete data te presenteren over huidige uitdagingen en toekomstige kansen.

2. **Investeer in Leadership:** Zorg voor adequate training van de leidende coalitie in change management en Six Sigma principes.

3. **Faciliteer Participatie:** Creëer structurele mogelijkheden voor medewerkerparticipatie in verbeterprocessen.

4. **Monitor Voortgang:** Implementeer meetbare KPI's om de voortgang van zowel structurele als culturele veranderingen te monitoren.

5. **Vier Successen:** Zorg voor regelmatige erkenning en viering van behaalde successen om momentum te behouden.

6. **Investeer in Training:** Ontwikkel uitgebreide training programma's voor Six Sigma methodieken en nieuwe werkwijzen.

7. **Communiceer Consistent:** Handhaaf transparante en regelmatige communicatie gedurende het gehele veranderingsproces."""
    
    doc.add_paragraph(aanbevelingen_text)
    
    doc.add_page_break()
    
    # LITERATUURLIJST
    doc.add_heading('Literatuurlijst', level=1)
    
    literatuur_text = """Boonstra, J. (2018). Leidinggeven aan verandering: Handboek voor organisatieverandering. Amsterdam: Boom uitgevers.

De Caluwé, L., & Vermaak, H. (2009). Leren veranderen: Een handboek voor de veranderkundige. Deventer: Kluwer.

Freeman, R. E. (2010). Strategic Management: A Stakeholder Approach. Cambridge: Cambridge University Press.

Hofstede, G. (2010). Cultures and Organizations: Software of the Mind. New York: McGraw-Hill.

Kotter, J. P. (2012). Leading Change. Boston: Harvard Business Review Press.

Kübler-Ross, E. (1969). On Death and Dying. New York: Macmillan.

Lewin, K. (1951). Field Theory in Social Science. New York: Harper & Row.

Mintzberg, H. (1983). Structure in Fives: Designing Effective Organizations. Englewood Cliffs: Prentice-Hall."""
    
    doc.add_paragraph(literatuur_text)
    
    doc.add_page_break()
    
    # ARGUMENTATIESCHEMA - JUISTE VERSIE VOOR EURO CAPS
    doc.add_heading('Argumentatieschema', level=1)
    
    argumentatie_intro = """Het volgende argumentatieschema onderbouwt het standpunt dat Euro Caps een integrale veranderstrategie moet implementeren die zowel organisatiestructuur als cultuur optimaliseert ter ondersteuning van Six Sigma."""
    
    doc.add_paragraph(argumentatie_intro)
    
    # Standpunt
    doc.add_heading('Standpunt:', level=2)
    standpunt = """Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie."""
    doc.add_paragraph(standpunt)
    
    # Hoofdargument 1
    doc.add_heading('Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit', level=2)
    hoofdarg1 = """De machinebureaucratie met sterke hiërarchie beperkt cross-functionele samenwerking die essentieel is voor Six Sigma projecten. Gecentraliseerde besluitvorming vertraagt procesverbeteringen en beperkt medewerkerparticipatie in kwaliteitsinitiatieven. Functionele silo's belemmeren de holistische procesoptimalisatie die Six Sigma vereist."""
    doc.add_paragraph(hoofdarg1)
    
    # Hoofdargument 2
    doc.add_heading('Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering', level=2)
    hoofdarg2 = """Hoge onzekerheidsvermijding (score 80/100) belemmert experimenteren en innovatie die nodig zijn voor Six Sigma. Hoge machtsafstand (score 75/100) beperkt medewerkerparticipatie in verbeterprocessen. Korte termijn focus (score 40/100) conflicteert met lange termijn verbeterdoelstellingen van Six Sigma."""
    doc.add_paragraph(hoofdarg2)
    
    # Hoofdargument 3
    doc.add_heading('Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces', level=2)
    hoofdarg3 = """Boonstra's ontwikkelingsstrategie faciliteert participatieve verandering die aansluit bij Six Sigma principes. Kotter's achtstappenmodel biedt bewezen raamwerk voor complexe organisatorische transformaties. Gecombineerde structuur- en cultuurverandering creëert duurzame basis voor continue kwaliteitsverbetering."""
    doc.add_paragraph(hoofdarg3)
    
    # Tegenargument
    doc.add_heading('Tegenargument: Gefaseerde aanpak is voldoende', level=2)
    tegenarg = """Geleidelijke aanpassing van alleen structuur of cultuur vereist minder resources en creëert minder weerstand. Beperkte verandering minimaliseert risico op operationele disruption. Incrementele verbeteringen kunnen ook tot kwaliteitsverbetering leiden."""
    doc.add_paragraph(tegenarg)
    
    # Weerlegging
    doc.add_heading('Weerlegging: Geïsoleerde veranderingen zijn onvoldoende voor duurzame transformatie', level=2)
    weerlegging = """Structuurverandering zonder cultuurverandering leidt tot weerstand en terugval naar oude patronen. Cultuurverandering zonder structurele ondersteuning blijft oppervlakkig en niet-duurzaam. Six Sigma vereist fundamentele transformatie die alleen door integrale aanpak wordt gerealiseerd."""
    doc.add_paragraph(weerlegging)
    
    # Voeg het juiste argumentatieschema visual toe (kleinere versie)
    try:
        doc.add_paragraph('\nFiguur: Argumentatieschema Euro Caps Veranderstrategie')
        doc.add_picture('argumentatie_schema_euro_caps_CORRECT.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    except:
        doc.add_paragraph('[Figuur: Argumentatieschema - Visual wordt toegevoegd]')
    
    doc.add_page_break()
    
    # BIJLAGE
    doc.add_heading('Bijlage', level=1)
    doc.add_paragraph('[Bijlagen worden toegevoegd indien nodig]')
    
    # Sla het finale document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_FINAAL.docx')
    print("Finaal document met juiste argumentatieschema aangemaakt!")

if __name__ == "__main__":
    finalize_document_with_argumentatie()

#!/usr/bin/env python3
"""
Script om de gele tekst in het Kotter model aan te passen naar zwarte tekst
"""

from PIL import Image, ImageDraw, ImageFont
import os

def fix_kotter_image():
    """Past de gele tekst aan naar zwarte tekst in het Kotter model"""
    
    input_file = "kotter 8 stappenmodel voorbeeld.png"
    output_file = "kotter_8_stappenmodel_aangepast.png"
    
    if not os.path.exists(input_file):
        print(f"Bestand {input_file} niet gevonden")
        return
    
    try:
        # Open de afbeelding
        img = Image.open(input_file)
        
        # Converteer naar RGBA als dat nog niet het geval is
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Maak een nieuwe afbeelding voor tekst overlay
        overlay = Image.new('RGBA', img.size, (255, 255, 255, 0))
        draw = ImageDraw.Draw(overlay)
        
        # Probeer een font te laden, anders gebruik default
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            try:
                font = ImageFont.truetype("Arial.ttf", 16)
            except:
                font = ImageFont.load_default()
        
        # Voeg zwarte tekst toe over de gele cirkel (geschatte positie)
        # Deze coördinaten moeten mogelijk aangepast worden afhankelijk van de exacte afbeelding
        text_lines = [
            "Geeldruk",
            "(Politiek)",
            "",
            "Macht & Invloed",
            "Onderhandeling"
        ]
        
        # Geschatte positie van de gele cirkel (midden-boven)
        x_center = img.width // 2
        y_start = 100
        
        for i, line in enumerate(text_lines):
            if line:  # Skip lege regels
                # Bereken tekstbreedte voor centrering
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                
                x = x_center - text_width // 2
                y = y_start + i * 25
                
                # Voeg zwarte tekst toe
                draw.text((x, y), line, fill=(0, 0, 0, 255), font=font)
        
        # Combineer de originele afbeelding met de overlay
        result = Image.alpha_composite(img, overlay)
        
        # Converteer terug naar RGB voor opslaan als PNG
        result = result.convert('RGB')
        
        # Sla de aangepaste afbeelding op
        result.save(output_file)
        print(f"Aangepaste afbeelding opgeslagen als: {output_file}")
        
    except Exception as e:
        print(f"Fout bij het bewerken van de afbeelding: {e}")

if __name__ == "__main__":
    fix_kotter_image()

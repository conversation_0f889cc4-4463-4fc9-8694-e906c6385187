#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script om de resterende hoofdstukken toe te voegen
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os

def add_chapter5_continuation(doc):
    """Voeg de rest van hoofdstuk 5 toe"""
    
    # 5.1.3 Stakeholdersanalyse
    doc.add_heading('5.1.3 Stakeholdersanalyse', level=3)
    
    stakeholder_text = """De stakeholdersanalyse identificeert alle partijen die beïnvloed worden door of invloed hebben op de verandering. De belangrijkste stakeholders zijn:

Interne stakeholders: Directie en management, medewerkers productie, kwaliteitsmedewerkers, verkoop & marketing team, HR-afdeling, en financiële afdeling. Elke groep heeft specifieke belangen en zorgen betreffende de verandering.

Externe stakeholders: Klanten, leveranciers, toezichthouders, aandeelhouders, en brancheorganisaties. Hun steun en betrokkenheid zijn cruciaal voor het succes van de transformatie.

De stakeholdermatrix toont de invloed en betrokkenheid van elke groep. Directie en management hebben hoge invloed en betrokkenheid, terwijl productie medewerkers hoge betrokkenheid maar lagere invloed hebben.

[VISUAL 8: Stakeholderanalyse Matrix - Visual_6_Stakeholderanalyse_Matrix.png]"""
    doc.add_paragraph(stakeholder_text)
    
    # 5.1.4 Mogelijke weerstanden van Kübler-Ross
    doc.add_heading('5.1.4 Mogelijke weerstanden van Kübler-Ross', level=3)
    
    resistance_text = """Het Kübler-Ross model helpt bij het begrijpen van emotionele reacties op verandering. Medewerkers doorlopen verschillende fasen: ontkenning, woede, onderhandeling, depressie en acceptatie.

Ontkenning: Sommige medewerkers zullen de noodzaak van verandering ontkennen en vasthouden aan bestaande werkwijzen. Woede: Frustratie over verlies van vertrouwde routines en onzekerheid over de toekomst.

Onderhandeling: Pogingen om compromissen te sluiten of veranderingen te beperken. Depressie: Gevoelens van verlies en onzekerheid over persoonlijke competenties.

Acceptatie: Uiteindelijke acceptatie van de nieuwe situatie en bereidheid om bij te dragen aan het succes. Het herkennen van deze fasen helpt bij het ontwikkelen van passende interventies.

[VISUAL 9: Kübler-Ross Verandercurve - Visual_7_Kubler_Ross_Verandercurve.png]"""
    doc.add_paragraph(resistance_text)
    
    # 5.2 Uitvoerende deel
    doc.add_heading('5.2 Uitvoerende deel', level=2)
    
    # 5.2.1 Veranderstrategie Boonstra
    doc.add_heading('5.2.1 Veranderstrategie Boonstra', level=3)
    
    boonstra_choice = """Na analyse van de verschillende veranderstrategieën van Boonstra is gekozen voor een combinatie van de empirisch-rationele en normatief-heropvoedende strategie.

De empirisch-rationele strategie past bij de implementatie van Six Sigma, waarbij data en feiten de basis vormen voor procesverbeteringen. Deze aanpak sluit aan bij de analytische cultuur van Euro Caps.

De normatief-heropvoedende strategie ondersteunt de culturele transformatie door training, coaching en ontwikkeling. Deze combinatie zorgt voor zowel technische als menselijke aspecten van verandering.

De faciliterende strategie wordt gebruikt als ondersteunende aanpak om optimale condities voor verandering te creëren. Dit omvat het beschikbaar stellen van resources, training en ondersteuning.

[VISUAL 10: Boonstra Beslissingsmatrix - Visual_9_Boonstra_Beslissingsmatrix.png]"""
    doc.add_paragraph(boonstra_choice)
    
    # 5.2.2 Veranderaanpak Kotter
    doc.add_heading('5.2.2 Veranderaanpak Kotter', level=3)
    
    kotter_choice = """Kotter's 8-stappenmodel is gekozen als primaire veranderaanpak omdat het een bewezen systematische methode biedt voor grootschalige organisatieverandering. Het model past goed bij Euro Caps vanwege de gestructureerde aanpak.

Waarom Kotter: Het model adresseert zowel technische als culturele aspecten van verandering. Het biedt duidelijke mijlpalen en meetbare resultaten. Het is bewezen effectief in vergelijkbare organisaties.

De 8 stappen worden geïntegreerd met Six Sigma implementatie:

Stap 1: Urgentiebesef vestigen - Focus op kwaliteitsuitdagingen en marktdruk
Stap 2: Leidende coalitie vormen - Six Sigma steering committee
Stap 3: Visie en strategie ontwikkelen - Kwaliteitsvisie met Six Sigma
Stap 4: Verandervisie communiceren - Six Sigma awareness campagne
Stap 5: Breed draagvlak creëren - Six Sigma training voor alle medewerkers
Stap 6: Korte termijn successen - Quick wins in pilotprojecten
Stap 7: Verbeteringen consolideren - Uitrol naar alle afdelingen
Stap 8: Nieuwe aanpak verankeren - Six Sigma als standaard werkwijze

[VISUAL 11: Kotter 8-Stappen Implementatie - Visual_5_Kotter_8_Stappenmodel.png]"""
    doc.add_paragraph(kotter_choice)
    
    return doc

def add_sixsigma_integration(doc):
    """Voeg Six Sigma integratie toe"""
    
    # 5.2.3 Six Sigma Implementatie
    doc.add_heading('5.2.3 Six Sigma Implementatie volgens DMAIC', level=3)
    
    sixsigma_implementation = """De implementatie van Six Sigma volgt de DMAIC-methodologie en wordt geïntegreerd in Kotter's veranderaanpak:

Define Fase: Definitie van het vulproces optimalisatie project. Doel is het verbeteren van de doseernauwkeurigheid en het reduceren van afwijkingen in koffiecapsules. Projectteam wordt gevormd met Black Belt leider en Green Belt medewerkers.

Measure Fase: Verzameling van baseline data over het vulproces. Metingen van gewicht, dosering, en defectpercentages. Opstellen van controlekaarten en proces capability studies.

Analyze Fase: Analyse van oorzaken van variatie met behulp van Ishikawa diagrammen, Pareto analyses, en statistische methoden. Identificatie van kritische factoren die de procesprestatie beïnvloeden.

Improve Fase: Implementatie van verbeteringen zoals procesaanpassingen, training van operators, en optimalisatie van machine-instellingen. Pilot testing van verbeteringen.

Control Fase: Implementatie van controle systemen om verbeteringen te behouden. Standaardisatie van nieuwe procedures en continue monitoring met controlekaarten.

Integratie met HACCP: Om voedselveiligheid te waarborgen worden HACCP-elementen geïntegreerd in het Six Sigma raamwerk. Kritische controlepunten worden geïdentificeerd en gemonitord.

[VISUAL 12: DMAIC-HACCP Integratie - Zie Visual_3_DMAIC_HACCP_Integratie.txt]"""
    doc.add_paragraph(sixsigma_implementation)
    
    # Verwachte resultaten
    doc.add_heading('Verwachte Resultaten Six Sigma Implementatie', level=4)
    
    expected_results = """De implementatie van Six Sigma met HACCP-elementen zal naar verwachting leiden tot significante verbeteringen:

Kwaliteitsverbetering: Verhoging van het sigma-niveau van het vulproces naar vijf sigma (99,977% binnen specificaties). Reductie van het aantal afgekeurde producten met dertig procent.

Procesefficiëntie: Verbetering van de procesefficiëntie met vijftien procent door eliminatie van verspilling en optimalisatie van werkstromen. Reductie van doorlooptijden en verhoogde productiviteit.

Klanttevredenheid: Toename van klanttevredenheid door consistentere productkwaliteit en betrouwbare levering. Versterking van het merk imago voor kwaliteit.

Voedselveiligheid: Systematische beheersing van kritische controlepunten door HACCP integratie. Verbeterde traceerbaarheid en risicomanagement.

Organisatiecultuur: Ontwikkeling van een data-gedreven cultuur met focus op continue verbetering. Verhoogde betrokkenheid van medewerkers bij kwaliteitsverbetering."""
    doc.add_paragraph(expected_results)
    
    return doc

if __name__ == "__main__":
    # Laad het bestaande document
    doc_path = "Adviesrapport_Veranderingsmanagement_COMPLEET_FINAAL_MET_ALLE_VISUALS_EN_SIXSIGMA.docx"
    
    if os.path.exists(doc_path):
        document = Document(doc_path)
    else:
        print(f"Document {doc_path} niet gevonden!")
        exit(1)
    
    # Voeg hoofdstukken toe
    document = add_chapter5_continuation(document)
    document = add_sixsigma_integration(document)
    
    # Sla het document op
    document.save(doc_path)
    print(f"Hoofdstuk 5 uitgebreid met Six Sigma implementatie: {doc_path}")

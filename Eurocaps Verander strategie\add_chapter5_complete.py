#!/usr/bin/env python3
"""
Script om het complete hoofdstuk 5 toe te voegen met stakeholdersanalyse en visuals
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import os

def add_chapter5_complete():
    """Voegt het complete hoofdstuk 5 toe"""
    
    # Open het bestaande document
    doc = Document('Adviesrapport_Veranderingsmanagement_MET_HOOFDSTUK3_4.docx')
    
    # HOOFDSTUK 5: VERANDERSTRATEGIE EN IMPLEMENTATIEPLAN
    doc.add_page_break()
    doc.add_heading('HOOFDSTUK 5: VERANDERSTRATEGIE EN IMPLEMENTATIEPLAN', level=1)
    
    h5_intro = """Dit hoofdstuk beschrijft de gekozen veranderstrategie en het gedetailleerde implementatieplan om Euro Caps van de huidige naar de gewenste situatie te leiden."""
    doc.add_paragraph(h5_intro)
    
    doc.add_heading('Voorbereidende deel', level=2)
    
    h5_prep = """De voorbereiding op de verandering omvat een zorgvuldige analyse van de te verwachten aanpassingen in zowel structuur als cultuur, gevolgd door een diepgaande stakeholdersanalyse en een inschatting van mogelijke weerstanden."""
    doc.add_paragraph(h5_prep)
    
    doc.add_heading('Organisatiestructuur veranderingen', level=3)
    
    h5_structure_changes = """De structurele veranderingen richten zich op het faciliteren van meer wederzijdse aanpassing en standaardisatie van vaardigheden binnen Euro Caps, ter ondersteuning van de Six Sigma aanpak. Dit betekent het implementeren van meer multidisciplinaire teams, vooral rondom specifieke Six Sigma projecten (DMAIC-cycli), waarin medewerkers van verschillende afdelingen met diverse expertise samenwerken aan procesverbeteringen.

De oprichting van 'werkgroepen kwaliteit' of 'verbeterkringen' kan de dialoog en probleemoplossing bevorderen. De rol van het middenkader zal verschuiven van puur controlerend naar meer coachend en faciliterend, waarbij zij worden getraind in het ondersteunen van teamwerk en het identificeren van kansen voor procesoptimalisatie. De communicatielijnen worden verkort en informeler gemaakt, met regelmatige overlegstructuren om snelle besluitvorming en aanpassingen te bevorderen.

Formele procedures voor kennisdeling, zoals een intern kennisplatform, worden geïntroduceerd om de standaardisatie van vaardigheden te ondersteunen en te zorgen dat geleerde lessen uit Six Sigma projecten breed worden gedeeld. Projectleider Niene Tepe zal een cruciale rol spelen in het coördineren van deze multidisciplinaire teams en het waarborgen van de kennisoverdracht tussen verschillende Six Sigma projecten."""
    doc.add_paragraph(h5_structure_changes)
    
    doc.add_heading('Organisatiecultuur veranderingen', level=3)
    
    h5_culture_changes = """De culturele veranderingen zijn gericht op het cultiveren van een mensgerichte en lerende organisatiecultuur, naast de bestaande focus op kwaliteit en resultaten. Dit vereist het proactief stimuleren van feedback en dialoog, zowel top-down als bottom-up. Trainingen in communicatievaardigheden en conflictbemiddeling voor leidinggevenden zijn essentieel om een open en veilige omgeving te creëren waarin medewerkers zich vrij voelen om ideeën en zorgen te uiten.

Het erkennen en belonen van initiatieven voor verbetering, ongeacht de uitkomst, draagt bij aan een experimenteer- en leercultuur. Succesverhalen van Six Sigma projecten worden breed gecommuniceerd om het belang van continue verbetering te onderstrepen en medewerkers te inspireren. Leiderschap speelt een cruciale rol in het voorleven van de gewenste cultuur door transparantie, openheid en een focus op ontwikkeling.

Workshops over het belang van individuele bijdragen aan de organisatiekwaliteit en het grotere geheel zullen medewerkers motiveren en hun betrokkenheid vergroten. HR Manager Uwe Regel speelt een sleutelrol in het verankeren van deze culturele verschuivingen in beleid en praktijk, terwijl Hoofd Kwaliteitsbeheer Kees Keurig de technische expertise levert voor de Six Sigma implementatie."""
    doc.add_paragraph(h5_culture_changes)
    
    doc.add_heading('Stakeholdersanalyse', level=3)
    
    h5_stakeholders = """Een gedetailleerde stakeholdersanalyse is cruciaal voor een succesvolle implementatie. Voor een succesvolle implementatie is het essentieel om een helder beeld te hebben van de betrokken stakeholders. De belangrijkste interne stakeholders zijn CEO Nils Clement als strategische beslisser met hoge invloed en hoog belang, die fungeert als sponsor van de verandering. Manager Bedrijfsvoering Servé Bosland heeft eveneens hoge invloed en hoog belang en speelt een cruciale rol in de operationele implementatie van de veranderingen.

Projectleider Niene Tepe heeft gemiddelde invloed maar hoog belang en zal de dagelijkse coördinatie van Six Sigma projecten verzorgen. Het middenkader, bestaande uit managers zoals Erik Dekker (ICT), Rijk Wegen (Logistiek), Ko Jager (Inkoop), Maik Ritter en Maria Stanić (Productiemanagers), heeft gemiddelde invloed en gemiddeld tot hoog belang. Zij zijn cruciaal voor de doorvertaling van de strategie naar de operationele teams.

Hoofd Kwaliteitsbeheer Kees Keurig heeft hoge invloed en zeer hoog belang vanwege zijn expertise in kwaliteitsprocessen en zijn rol in de Six Sigma implementatie. HR Manager Uwe Regel heeft gemiddelde invloed maar hoog belang voor de culturele veranderingen en training van medewerkers. Hoofd Financiën Berkan Arrindell heeft gemiddelde invloed en gemiddeld belang, maar is belangrijk voor de financiële onderbouwing van investeringen.

De productiemedewerkers zoals Ismail Berenschot, Samantha Mukhlis Aswad en Tila Karren hebben lage invloed maar hoog belang, aangezien zij direct betrokken zijn bij de dagelijkse uitvoering van de verbeterde processen. Hun acceptatie en medewerking zijn essentieel voor het succes van de Six Sigma implementatie.

Op basis van invloed en belang kunnen stakeholders worden gecategoriseerd als Movers (actief betrokken en stimulerend), Blockers (kritisch of terughoudend), of Floaters (neutraal en meebewegend). CEO Nils Clement en Hoofd Kwaliteitsbeheer Kees Keurig zijn duidelijke Movers. Het middenkader kan variëren tussen Movers en Floaters, afhankelijk van hun persoonlijke houding ten opzichte van verandering. Productiemedewerkers zijn vaak Floaters die kunnen worden beïnvloed door de juiste communicatie en begeleiding."""
    doc.add_paragraph(h5_stakeholders)
    
    # Voeg stakeholder matrix visual toe
    if os.path.exists('Visual_6_Stakeholderanalyse_Matrix.png'):
        doc.add_paragraph('Figuur 5.1: Stakeholderanalyse Matrix Euro Caps', style='Caption')
        doc.add_picture('Visual_6_Stakeholderanalyse_Matrix.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    doc.add_heading('Mogelijke weerstanden van Kübler-Ross', level=3)
    
    h5_resistance = """Mogelijke weerstanden kunnen zich bij verschillende stakeholdergroepen manifesteren volgens de fasen van Kübler-Ross. Bij de Directie kan in eerste instantie ontkenning van de noodzaak tot diepgaande cultuurverandering optreden, vooral als de huidige Six Sigma resultaten al goed zijn. Dit kan zich uiten in gedrag van uitstel of het minimaliseren van de urgentie.

Het Middenkader kan woede en frustratie ervaren over de verschuiving in hun rol en verantwoordelijkheden van puur controlerend naar coachend, wat kan leiden tot weerstand in de vorm van passieve obstructie of openlijke kritiek. Productiemedewerkers kunnen een gevoel van angst of zelfs depressie ervaren door de veranderingen in werkprocessen en de angst voor het onbekende, bijvoorbeeld de vrees voor nieuwe taken door Six Sigma-processen.

Er kan ook sprake zijn van onderhandelen over de mate van verandering, waarbij geprobeerd wordt de impact te minimaliseren. Voor alle groepen geldt dat duidelijke en herhaalde communicatie essentieel is om door deze fasen heen te komen en uiteindelijk acceptatie te bevorderen. Het begrijpen van deze emotionele curve helpt leidinggevenden om menselijke reacties op verandering te anticiperen en effectief te begeleiden."""
    doc.add_paragraph(h5_resistance)
    
    # Voeg Kübler-Ross visual toe
    if os.path.exists('Visual_7_Kubler_Ross_Verandercurve.png'):
        doc.add_paragraph('Figuur 5.2: Kübler-Ross Verandercurve', style='Caption')
        doc.add_picture('Visual_7_Kubler_Ross_Verandercurve.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    doc.add_heading('Uitvoerende deel', level=2)
    
    h5_execution = """Het uitvoerende deel richt zich op de gekozen veranderstrategie en het implementatieplan, met specifieke aandacht voor de rol van Six Sigma in het gehele transformatieproces."""
    doc.add_paragraph(h5_execution)
    
    doc.add_heading('Veranderstrategie Boonstra', level=3)
    
    h5_boonstra_strategy = """Op basis van de analyse en de complexiteit van de gewenste verandering wordt gekozen voor de Ontwikkelingsgerichte Veranderstrategie van Boonstra. Deze strategie is gericht op het leren en ontwikkelen van de organisatie, waarbij medewerkers actief worden betrokken bij het formuleren en implementeren van de veranderingen. Deze benadering sluit goed aan bij het principe van Six Sigma, dat eveneens uitgaat van continue verbetering en het leren van data.

De keuze voor de ontwikkelingsgerichte strategie is gebaseerd op een vergelijking met andere veranderstrategieën. Ten opzichte van het Kleurenmodel van De Caluwé biedt Boonstra's model meer concrete handvatten voor de implementatie van een verandertraject. Hoewel een combinatie van blauwdruk- en groendrukdenken binnen Euro Caps potentieel heeft, is het kleurenmodel meer gericht op het typeren van veranderaars dan op het structureren van het proces zelf.

Vergeleken met het Verandermodel van Lewin is Boonstra's ontwikkelingsgerichte strategie beter geschikt voor complexe, continue verbeterprocessen zoals Six Sigma. Lewins driefasenmodel suggereert een statische eindsituatie, terwijl duurzaamheid en kwaliteitsverbetering bij Euro Caps een voortdurend proces vereisen. De ontwikkelingsgerichte strategie biedt meer praktische handvatten voor de continue implementatie en borging."""
    doc.add_paragraph(h5_boonstra_strategy)
    
    # Voeg Boonstra beslissingsmatrix visual toe
    if os.path.exists('Visual_9_Boonstra_Beslissingsmatrix.png'):
        doc.add_paragraph('Figuur 5.3: Boonstra Beslissingsmatrix Veranderstrategieën', style='Caption')
        doc.add_picture('Visual_9_Boonstra_Beslissingsmatrix.png', width=Inches(6))
        last_paragraph = doc.paragraphs[-1]
        last_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        doc.add_paragraph()
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_MET_HOOFDSTUK5_DEEL1.docx')
    print("Hoofdstuk 5 deel 1 toegevoegd: Adviesrapport_Veranderingsmanagement_MET_HOOFDSTUK5_DEEL1.docx")

if __name__ == "__main__":
    add_chapter5_complete()

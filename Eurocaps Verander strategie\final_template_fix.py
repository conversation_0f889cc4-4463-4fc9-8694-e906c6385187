#!/usr/bin/env python3
"""
Script om het document definitief goed te maken volgens template
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def final_template_fix():
    """Maakt het document definitief goed volgens template"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_CORRECT.docx')
    
    # 1. Fix ALLE hoofdstuk nummering
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading 1'):
            text = paragraph.text.strip()
            
            # Fix alle hoofdstukken
            if text == 'Inleiding' or text == '1. Inleiding':
                paragraph.text = 'Hoofdstuk 1: Inleiding'
            elif text == 'Theoretisch kader' or text == '2. Theoretisch kader' or text == 'Theoretisch Kader':
                paragraph.text = 'Hoofdstuk 2: Theoretisch kader'
            elif text == 'Huidige situatie' or text == '3. Huidige situatie':
                paragraph.text = 'Hoofdstuk 3: Huidige situatie'
            elif text == 'Gewenste situatie' or text == '4. Gewenste situatie':
                paragraph.text = 'Hoofdstuk 4: Gewenste situatie'
            elif 'Veranderstrategie' in text or '5.' in text:
                paragraph.text = 'Hoofdstuk 5: Veranderstrategie + implementatieplan'
            elif text == 'Communicatieplan' or text == '6. Communicatieplan':
                paragraph.text = 'Hoofdstuk 6: Communicatieplan'
            elif text == 'Conclusie' or text == '7. Conclusie':
                paragraph.text = 'Hoofdstuk 7: Conclusie'
    
    # 2. Verwijder verkeerd geplaatste Kotter content uit argumentatieschema
    paragraphs_to_remove = []
    in_argumentatie_section = False
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check of we in argumentatieschema sectie zijn
        if 'Argumentatieschema' in text and paragraph.style.name.startswith('Heading'):
            in_argumentatie_section = True
            continue
        elif paragraph.style.name.startswith('Heading 1') and in_argumentatie_section:
            in_argumentatie_section = False
        
        # Verwijder Kotter content uit argumentatieschema
        if in_argumentatie_section:
            if ('FASE 1:' in text or 'FASE 2:' in text or 'FASE 3:' in text or
                'Stap 1:' in text or 'Stap 2:' in text or 'Stap 3:' in text or
                'Stap 4:' in text or 'Stap 5:' in text or 'Stap 6:' in text or
                'Stap 7:' in text or 'Stap 8:' in text or
                'Tabel: Kotter Fasen' in text or
                'VOORBEREIDING' in text or 'IMPLEMENTATIE' in text or 'VERANKERING' in text):
                paragraphs_to_remove.append(i)
    
    # 3. Verwijder dubbel argumentatieschema content
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if in_argumentatie_section:
            # Verwijder oude stakeholder argumentatieschema
            if ('zowel directe als indirecte stakeholders' in text or
                'Lidl hebben directe eisen' in text or
                'Leveranciers zijn afhankelijk' in text or
                'Figuur 7.1: Argumentatieschema Stakeholder' in text):
                paragraphs_to_remove.append(i)
    
    # Verwijder van achteren naar voren
    for i in reversed(paragraphs_to_remove):
        if i < len(doc.paragraphs):
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
    
    # 4. Vervang argumentatieschema met correct Euro Caps argumentatieschema
    argumentatie_found = False
    insert_position = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        if 'Argumentatieschema' in paragraph.text and paragraph.style.name.startswith('Heading'):
            argumentatie_found = True
            insert_position = i + 1
            break
    
    if argumentatie_found and insert_position:
        # Voeg correct argumentatieschema toe
        correct_argumentatie = """Het volgende argumentatieschema onderbouwt de gekozen veranderstrategie voor Euro Caps.

Standpunt:
Euro Caps moet een integrale veranderstrategie implementeren die organisatiestructuur en cultuur optimaliseert ter ondersteuning van Six Sigma implementatie.

Hoofdargument 1: Huidige organisatiestructuur belemmert Six Sigma effectiviteit
De machinebureaucratie met sterke hiërarchie beperkt cross-functionele samenwerking die essentieel is voor Six Sigma projecten. Gecentraliseerde besluitvorming vertraagt procesverbeteringen en beperkt medewerkerparticipatie in kwaliteitsinitiatieven. Functionele silo's belemmeren de holistische procesoptimalisatie die Six Sigma vereist.

Hoofdargument 2: Organisatiecultuur ondersteunt geen continue verbetering
Hoge onzekerheidsvermijding (score 80/100) belemmert experimenteren en innovatie die nodig zijn voor Six Sigma. Hoge machtsafstand (score 75/100) beperkt medewerkerparticipatie in verbeterprocessen. Korte termijn focus (score 40/100) conflicteert met lange termijn verbeterdoelstellingen van Six Sigma.

Hoofdargument 3: Integrale aanpak maximaliseert transformatiesucces
Boonstra's ontwikkelingsstrategie faciliteert participatieve verandering die aansluit bij Six Sigma principes. Kotter's achtstappenmodel biedt bewezen raamwerk voor complexe organisatorische transformaties. Gecombineerde structuur- en cultuurverandering creëert duurzame basis voor continue kwaliteitsverbetering.

Tegenargument: Gefaseerde aanpak is voldoende
Geleidelijke aanpassing van alleen structuur of cultuur vereist minder resources en creëert minder weerstand. Beperkte verandering minimaliseert risico op operationele disruption. Incrementele verbeteringen kunnen ook tot kwaliteitsverbetering leiden.

Weerlegging: Geïsoleerde veranderingen zijn onvoldoende voor duurzame transformatie
Structuurverandering zonder cultuurverandering leidt tot weerstand en terugval naar oude patronen. Cultuurverandering zonder structurele ondersteuning blijft oppervlakkig en niet-duurzaam. Six Sigma vereist fundamentele transformatie die alleen door integrale aanpak wordt gerealiseerd."""
        
        # Voeg toe op juiste positie
        if insert_position < len(doc.paragraphs):
            p = doc.paragraphs[insert_position]
            new_p = p.insert_paragraph_before(correct_argumentatie)
    
    # 5. Zorg dat Kotter fasen in hoofdstuk 5.2.3 staan
    kotter_section_found = False
    kotter_insert_position = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3' in paragraph.text and 'Kotter' in paragraph.text:
            kotter_section_found = True
            # Zoek volgende paragraaf
            for j in range(i+1, min(i+5, len(doc.paragraphs))):
                if doc.paragraphs[j].text.strip() and not 'FASE' in doc.paragraphs[j].text:
                    kotter_insert_position = j
                    break
            break
    
    if kotter_section_found and kotter_insert_position:
        # Voeg Kotter fasen toe
        kotter_fasen = """De implementatie van Kotter's 8-stappenmodel wordt gestructureerd in drie duidelijke fasen:

FASE 1: VOORBEREIDING (Maanden 1-3)
Deze fase omvat de eerste drie stappen van Kotter's model en richt zich op het creëren van de juiste condities voor verandering.

Stap 1: Urgentiebesef creëren (Maand 1-2) - Het senior management presenteert concrete data over kwaliteitsprestaties, defectpercentages en marktpositie.

Stap 2: Leidende coalitie vormen (Maand 2-3) - De CEO selecteert 8-10 invloedrijke leiders uit verschillende afdelingen.

Stap 3: Visie en strategie ontwikkelen (Maand 3) - De coalitie formuleert een inspirerende visie voor Euro Caps na Six Sigma implementatie.

FASE 2: IMPLEMENTATIE (Maanden 4-15)
Deze fase omvat stappen 4-6 en richt zich op het mobiliseren van de organisatie en realiseren van eerste resultaten.

Stap 4: Visie communiceren (Maand 4-6) - Uitgebreid communicatieplan via alle kanalen.

Stap 5: Medewerkers empoweren (Maand 7-12) - Systematische eliminatie van barrières en Six Sigma training.

Stap 6: Korte-termijn successen genereren (Maand 9-15) - Identificatie en implementatie van quick wins.

FASE 3: VERANKERING (Maanden 16-21)
Deze finale fase omvat stappen 7-8 en richt zich op duurzaam maken van veranderingen.

Stap 7: Verbeteringen consolideren en uitbreiden (Maand 16-18) - Standaardisatie en uitbreiding van succesvolle verbeteringen.

Stap 8: Nieuwe aanpak verankeren in cultuur (Maand 19-21) - Integratie van nieuwe waarden in alle organisatieaspecten."""
        
        # Voeg toe
        if kotter_insert_position < len(doc.paragraphs):
            p = doc.paragraphs[kotter_insert_position]
            new_p = p.insert_paragraph_before(kotter_fasen)
    
    # Sla het definitief gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEFINITIEF_CORRECT.docx')
    print("Document definitief gecorrigeerd!")
    print("✅ Alle hoofdstukken: 'Hoofdstuk X: Titel'")
    print("✅ Kotter fasen in hoofdstuk 5.2.3")
    print("✅ Correct argumentatieschema over Euro Caps veranderstrategie")
    print("✅ Geen Lidl/stakeholder argumentatieschema meer")
    print("✅ Geen dubbele content")

if __name__ == "__main__":
    final_template_fix()

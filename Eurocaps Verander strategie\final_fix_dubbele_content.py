#!/usr/bin/env python3
"""
Script om definitief alle dubbele content te verwijderen en Kotter headings te splitsen
"""

from docx import Document

def final_fix_dubbele_content():
    """Verwijdert definitief alle dubbele content en splitst Kotter headings"""
    
    # Open het document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_ALLE_HEADINGS_CORRECT.docx')
    
    print("Bezig met definitieve fix van dubbele content en Kotter headings...")
    
    # 1. Zoek ALLE paragrafen met 2.5 en 2.6 content
    print("\n1. Zoeken naar ALLE 2.5 en 2.6 content...")
    
    paragraphs_to_remove = []
    found_25_heading = False
    found_26_heading = False
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Check voor 2.5 content
        if '2.5 Stakeholderanalyse' in text:
            if paragraph.style.name.startswith('Heading') and not found_25_heading:
                found_25_heading = True
                print(f"Behouden 2.5 heading op regel {i}")
            else:
                paragraphs_to_remove.append(i)
                print(f"Markeren voor verwijdering: 2.5 op regel {i} (style: {paragraph.style.name})")
        
        # Check voor 2.6 content  
        elif '2.6 Verandercurve' in text:
            if paragraph.style.name.startswith('Heading') and not found_26_heading:
                found_26_heading = True
                print(f"Behouden 2.6 heading op regel {i}")
            else:
                paragraphs_to_remove.append(i)
                print(f"Markeren voor verwijdering: 2.6 op regel {i} (style: {paragraph.style.name})")
        
        # Check voor stakeholder content zonder nummer
        elif ('Stakeholderanalyse is een systematische methode' in text or
              'Freeman\'s stakeholdertheorie' in text):
            paragraphs_to_remove.append(i)
            print(f"Markeren voor verwijdering: stakeholder content op regel {i}")
        
        # Check voor Kübler-Ross content zonder nummer
        elif ('Het Kübler-Ross model beschrijft' in text or
              'ontkenning, woede, onderhandeling' in text):
            paragraphs_to_remove.append(i)
            print(f"Markeren voor verwijdering: Kübler-Ross content op regel {i}")
    
    # Verwijder alle gemarkeerde paragrafen (van achteren naar voren)
    for i in reversed(paragraphs_to_remove):
        if i < len(doc.paragraphs):
            print(f"Verwijderen paragraaf {i}: '{doc.paragraphs[i].text[:50]}...'")
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
    
    # 2. Fix Kotter sectie - splits de grote heading op
    print("\n2. Splitsen van Kotter content in aparte headings...")
    
    # Zoek de 5.2.3 Kotter sectie
    kotter_start = None
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3 Veranderaanpak Kotter' in paragraph.text and paragraph.style.name.startswith('Heading'):
            kotter_start = i
            print(f"Kotter sectie gevonden op regel {i}")
            break
    
    if kotter_start:
        # Zoek alle content na 5.2.3 tot de volgende hoofdsectie
        j = kotter_start + 1
        kotter_content = []
        
        while j < len(doc.paragraphs):
            text = doc.paragraphs[j].text.strip()
            
            # Stop bij volgende hoofdsectie
            if (doc.paragraphs[j].style.name.startswith('Heading') and 
                ('5.2.4' in text or '5.3' in text or 'Hoofdstuk 6' in text)):
                break
            
            kotter_content.append((j, text, doc.paragraphs[j].style.name))
            j += 1
        
        print(f"Gevonden {len(kotter_content)} paragrafen Kotter content")
        
        # Splits de content en voeg headings toe waar nodig
        for idx, (regel, tekst, style) in enumerate(kotter_content):
            if regel < len(doc.paragraphs):
                # Check of dit een fase is die een heading moet krijgen
                if ('FASE 1:' in tekst or 'FASE 2:' in tekst or 'FASE 3:' in tekst):
                    if not style.startswith('Heading'):
                        doc.paragraphs[regel].style = doc.styles['Heading 4']
                        print(f"Heading 4 toegevoegd voor: '{tekst[:30]}...'")
                
                # Check of dit een stap is die een heading moet krijgen
                elif tekst.startswith('Stap ') and ':' in tekst:
                    if not style.startswith('Heading'):
                        try:
                            doc.paragraphs[regel].style = doc.styles['Heading 5']
                        except:
                            doc.paragraphs[regel].style = doc.styles['Heading 4']
                        print(f"Heading toegevoegd voor: '{tekst[:30]}...'")
    
    # 3. Voeg ontbrekende content toe voor 2.5 en 2.6 als ze alleen headings zijn
    print("\n3. Controleren of 2.5 en 2.6 content hebben...")
    
    # Zoek 2.5 heading
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == '2.5 Stakeholderanalyse' and paragraph.style.name.startswith('Heading'):
            # Check of er content na deze heading staat
            next_paragraph = i + 1
            if (next_paragraph < len(doc.paragraphs) and 
                (doc.paragraphs[next_paragraph].style.name.startswith('Heading') or
                 not doc.paragraphs[next_paragraph].text.strip())):
                # Voeg content toe
                content_25 = """Stakeholderanalyse is een systematische methode voor het identificeren en analyseren van alle partijen die invloed hebben op of beïnvloed worden door organisatorische veranderingen. Freeman's stakeholdertheorie onderscheidt primaire stakeholders (directe invloed) en secundaire stakeholders (indirecte invloed)."""
                
                new_p = paragraph.insert_paragraph_after(content_25)
                new_p.style = doc.styles['Normal']
                print("Content toegevoegd voor 2.5 Stakeholderanalyse")
            break
    
    # Zoek 2.6 heading
    for i, paragraph in enumerate(doc.paragraphs):
        if paragraph.text.strip() == '2.6 Verandercurve van Kübler-Ross' and paragraph.style.name.startswith('Heading'):
            # Check of er content na deze heading staat
            next_paragraph = i + 1
            if (next_paragraph < len(doc.paragraphs) and 
                (doc.paragraphs[next_paragraph].style.name.startswith('Heading') or
                 not doc.paragraphs[next_paragraph].text.strip())):
                # Voeg content toe
                content_26 = """Het Kübler-Ross model beschrijft de emotionele fasen die individuen doorlopen tijdens veranderingsprocessen: ontkenning, woede, onderhandeling, depressie en acceptatie. Dit model helpt bij het begrijpen en begeleiden van weerstand tijdens organisatorische veranderingen."""
                
                new_p = paragraph.insert_paragraph_after(content_26)
                new_p.style = doc.styles['Normal']
                print("Content toegevoegd voor 2.6 Verandercurve van Kübler-Ross")
            break
    
    # Sla het document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEFINITIEF_CLEAN.docx')
    print("\n✅ Document opgeslagen: Adviesrapport_Veranderingsmanagement_EURO_CAPS_DEFINITIEF_CLEAN.docx")
    print("✅ Alle dubbele 2.5 en 2.6 content definitief verwijderd")
    print("✅ Kotter content opgesplitst in aparte headings")
    print("✅ Fase en stap headings toegevoegd")
    print("✅ Document is nu volledig schoon en correct gestructureerd")

if __name__ == "__main__":
    final_fix_dubbele_content()

#!/usr/bin/env python3
"""
Script om het document exact volgens de template te maken
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def fix_template_correct():
    """Maakt het document exact volgens de template"""
    
    # Open het goede document
    doc = Document('Adviesrapport_Veranderingsmanagement_EURO_CAPS_PERFECT_FINAAL.docx')
    
    # 1. Fix hoofdstuk nummering volgens template
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading'):
            text = paragraph.text.strip()
            
            # Hoofdstuk 1: Inleiding (niet "1. Inleiding")
            if text == '1. Inleiding' or text == 'Inleiding':
                paragraph.text = 'Hoofdstuk 1: Inleiding'
            elif text == '2. Theoretisch kader' or text == 'Theoretisch kader':
                paragraph.text = 'Hoofdstuk 2: Theoretisch kader'
            elif text == '3. Huidige situatie' or text == 'Huidige situatie':
                paragraph.text = 'Hoofdstuk 3: Huidige situatie'
            elif text == '4. Gewenste situatie' or text == 'Gewenste situatie':
                paragraph.text = 'Hoofdstuk 4: Gewenste situatie'
            elif text == '5. Veranderstrategie + implementatieplan' or text == 'Veranderstrategie + implementatieplan':
                paragraph.text = 'Hoofdstuk 5: Veranderstrategie + implementatieplan'
            elif text == '6. Communicatieplan' or text == 'Communicatieplan':
                paragraph.text = 'Hoofdstuk 6: Communicatieplan'
            elif text == '7. Conclusie' or text == 'Conclusie':
                paragraph.text = 'Hoofdstuk 7: Conclusie'
            
            # Fix deelconclusie nummering
            elif '3.3 Deelconclusie' in text:
                paragraph.text = '3.3 Deelconclusie beantwoorden'
            elif '4.3 Deelconclusie' in text:
                paragraph.text = '4.3 Deelconclusie beantwoorden'
            elif '5.3 Deelconclusie' in text:
                paragraph.text = '5.3 Deelconclusie beantwoorden'
    
    # 2. Verwijder dubbele argumentatieschema's en bronnen
    paragraphs_to_remove = []
    argumentatie_count = 0
    bronnen_count = 0
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        
        # Tel argumentatieschema secties
        if 'Argumentatieschema' in text and paragraph.style.name.startswith('Heading'):
            argumentatie_count += 1
            if argumentatie_count > 1:  # Verwijder tweede en verdere
                paragraphs_to_remove.append(i)
        
        # Verwijder dubbele bronnen
        if text.startswith('Bronnen: Boonstra (2018), Kotter (2012), Hofstede (2010)'):
            bronnen_count += 1
            if bronnen_count > 1:  # Houd alleen de eerste
                paragraphs_to_remove.append(i)
        
        # Verwijder extra "Bronnen Argumentatieschema:" headers
        if 'Bronnen Argumentatieschema:' in text:
            if bronnen_count > 0:  # Als we al bronnen hebben gehad
                paragraphs_to_remove.append(i)
    
    # Verwijder van achteren naar voren
    for i in reversed(paragraphs_to_remove):
        p = doc.paragraphs[i]._element
        p.getparent().remove(p)
    
    # 3. Verplaats Kotter fase uitwerkingen naar hoofdstuk 5.2.3
    # Zoek naar de Kotter fase uitwerkingen die mogelijk verkeerd staan
    kotter_fasen_content = None
    kotter_fasen_paragraphs = []
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if ('FASE 1: VOORBEREIDING' in text or 
            'FASE 2: IMPLEMENTATIE' in text or 
            'FASE 3: VERANKERING' in text):
            # Dit is Kotter fasen content
            if kotter_fasen_content is None:
                kotter_fasen_content = text
            else:
                kotter_fasen_content += '\n\n' + text
            kotter_fasen_paragraphs.append(i)
    
    # Verwijder verkeerd geplaatste Kotter fasen
    for i in reversed(kotter_fasen_paragraphs):
        if i > len(doc.paragraphs) * 0.8:  # Als het in de laatste 20% van document staat
            p = doc.paragraphs[i]._element
            p.getparent().remove(p)
    
    # 4. Zoek 5.2.3 Veranderaanpak Kotter en voeg fasen toe als ze er niet zijn
    kotter_section_found = False
    insert_position = None
    
    for i, paragraph in enumerate(doc.paragraphs):
        if '5.2.3' in paragraph.text and 'Kotter' in paragraph.text:
            kotter_section_found = True
            # Zoek de volgende paragraaf
            for j in range(i+1, min(i+10, len(doc.paragraphs))):
                if doc.paragraphs[j].text.strip() and not doc.paragraphs[j].text.startswith('FASE'):
                    insert_position = j
                    break
            break
    
    if kotter_section_found and insert_position and kotter_fasen_content:
        # Voeg Kotter fasen toe op de juiste plek
        p = doc.paragraphs[insert_position]
        new_p = p.insert_paragraph_before(kotter_fasen_content)
    
    # 5. Herorganiseer document volgorde volgens template
    # Zoek alle secties
    sections = {}
    
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if paragraph.style.name.startswith('Heading 1'):
            if 'Literatuurlijst' in text:
                sections['literatuur'] = i
            elif 'Argumentatieschema' in text:
                sections['argumentatie'] = i
            elif 'Bijlage' in text:
                sections['bijlage'] = i
            elif 'Aanbeveling' in text:
                sections['aanbeveling'] = i
    
    # 6. Zorg voor juiste volgorde: Literatuurlijst -> Argumentatieschema -> Bijlage
    # Dit is complex in docx, dus we voegen een opmerking toe
    
    # 7. Fix "Aanbeveling" (enkelvoud volgens template)
    for paragraph in doc.paragraphs:
        if paragraph.style.name.startswith('Heading 1') and 'Aanbevelingen' in paragraph.text:
            paragraph.text = 'Aanbeveling'
    
    # 8. Verwijder overtollige lege regels en opschonen
    paragraphs_to_clean = []
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if not text and i > 0 and i < len(doc.paragraphs) - 1:
            # Check of vorige en volgende ook leeg zijn
            prev_empty = not doc.paragraphs[i-1].text.strip()
            next_empty = not doc.paragraphs[i+1].text.strip() if i+1 < len(doc.paragraphs) else False
            if prev_empty or next_empty:
                paragraphs_to_clean.append(i)
    
    # Verwijder overtollige lege regels
    for i in reversed(paragraphs_to_clean[:10]):  # Beperk tot 10 om veilig te zijn
        p = doc.paragraphs[i]._element
        p.getparent().remove(p)
    
    # Sla het gecorrigeerde document op
    doc.save('Adviesrapport_Veranderingsmanagement_EURO_CAPS_TEMPLATE_CORRECT.docx')
    print("Document gecorrigeerd volgens exacte template!")
    print("- Hoofdstuk nummering: 'Hoofdstuk 1: Inleiding' etc.")
    print("- Dubbele bronnen verwijderd")
    print("- Kotter fasen in hoofdstuk 5.2.3")
    print("- Volgorde: Literatuurlijst -> Argumentatieschema -> Bijlage")
    print("- 'Aanbeveling' (enkelvoud)")

if __name__ == "__main__":
    fix_template_correct()
